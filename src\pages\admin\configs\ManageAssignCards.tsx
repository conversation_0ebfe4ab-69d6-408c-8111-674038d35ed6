import { useEffect, useRef, useState, useCallback } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
    Divider,
    Tag,
    Popover,
    Alert,
    Progress,
    Tooltip,
    Spin,
    Statistic,
    Card,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined, CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import { useDispatch } from "react-redux";
import { deleteAgentCardsAffectation, getAgentCardsAffectationAll, getAgentCardsAffectations, storeAgentCardsAffectation, updateAgentCardsAffectation } from "../../../features/admin/agentCardsAffectationSlice.ts";
import { getAdminsAll } from "../../../features/admin/adminSlice.ts";
import { getSalePointAll } from "../../../features/admin/salePointSlice.ts";
import { getSalesPeriodsAll } from "../../../features/admin/salesPeriodsSlice.ts";
import { toast } from "react-toastify";
import { getCardTypesAll } from "../../../features/admin/cardTypeSlice.ts";
import { IdCard } from "lucide-react";
import { useSelector } from "react-redux";
import { getStockCardsAll } from "../../../features/admin/stockCardSlice.ts";
import { getAvailableSequences, getOccupiedSequences, getSequenceStats, checkSequenceAvailability, clearSequenceAvailability, recalculateAvailableRanges } from "../../../features/admin/cardSequenceTrackingSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageAssignCards() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch:any = useDispatch<any>();
    const debounceTimers = useRef<{[key: string]: NodeJS.Timeout}>({});

    const [loading, setLoading] = useState(false);
    const [sequenceLoading, setSequenceLoading] = useState<{[key: string]: boolean}>({});
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingAssignAgents, setEditingAssignAgents] = useState<any>(null);
    const [selectedTypes, setSelectedTypes] = useState<any[]>([]);
    const [form] = Form.useForm();
    const [validatingSequence, setValidatingSequence] = useState<{[key: string]: boolean}>({});
    const [sequenceValidationResults, setSequenceValidationResults] = useState<{[key: string]: boolean | null}>({});

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    const agents = useSelector((state: any) => state.admin.items.data);
    const salesPoints = useSelector((state: any) => state.salePoint.items.data);
    const salesPeriods = useSelector((state: any) => state.salesPeriod.items.data);
    const cardsTypes = useSelector((state: any) => state.cardType.items.data);
    const availableSequences = useSelector((state: any) => state.cardSequenceTracking.availableSequences);
    const occupiedSequences = useSelector((state: any) => state.cardSequenceTracking.occupiedSequences);
    const sequenceStats = useSelector((state: any) => state.cardSequenceTracking.sequenceStats);


    /*|--------------------------------------------------------------------------
    |  - FETCH AFFECTATION WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetAgentCardsAffectations = (params: any, sort: any, filter: any) => {
            return dispatch(getAgentCardsAffectations({
                pageNumber,
                perPage: pageSize,
                params, sort, filter
            }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
        }

       const fetchStoreData = async () => {
            try {
                setLoading(true);
                const promises = [];
                if(!agents?.length){
                    promises.push(dispatch(getAdminsAll()).unwrap());
                }
                if(!salesPoints?.length){
                    promises.push(dispatch(getSalePointAll()).unwrap());
                }
                if(!salesPeriods?.length){
                    promises.push(dispatch(getSalesPeriodsAll()).unwrap());
                }
                if(!cardsTypes?.length){
                    promises.push(dispatch(getCardTypesAll()).unwrap());
                }

                promises.push(dispatch(getStockCardsAll()).unwrap());

                await Promise.all(promises);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        }

        const fetchCardSequenceData = async (cardTypeId: number) => {
            try {
                setSequenceLoading(prev => ({ ...prev, [cardTypeId]: true }));
                await Promise.all([
                    dispatch(getAvailableSequences(cardTypeId)).unwrap(),
                    dispatch(getOccupiedSequences(cardTypeId)).unwrap(),
                    dispatch(getSequenceStats(cardTypeId)).unwrap()
                ]);
            } catch (error) {
                console.error('Error fetching card sequence data:', error);
                toast.error(t("manage_assignAgents.messages.errorLoadingSequences"), {
                    position: 'top-center',
                    autoClose: 3000
                });
            } finally {
                setSequenceLoading(prev => ({ ...prev, [cardTypeId]: false }));
            }
        }

        const handleRecalculateRanges = async (cardTypeId: number) => {
            try {
                setSequenceLoading(prev => ({ ...prev, [cardTypeId]: true }));
                await dispatch(recalculateAvailableRanges(cardTypeId)).unwrap();
                toast.success(t("manage_assignAgents.messages.recalculateSuccess"), {
                    position: 'top-center',
                    autoClose: 3000
                });
            } catch (error) {
                console.error('Error recalculating available ranges:', error);
                toast.error(t("manage_assignAgents.messages.recalculateError"), {
                    position: 'top-center',
                    autoClose: 3000
                });
            } finally {
                setSequenceLoading(prev => ({ ...prev, [cardTypeId]: false }));
            }
        }
    useEffect(() => {
        setLoading(true);
        fetchStoreData();
    }, []);



    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_assignAgents.labels.agent")}`,
            dataIndex: "agent",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => `${data.agent?.firstname} ${data.agent?.lastname}`,
            renderFormItem: () => {
                return (
                    <Select
                        showSearch
                        optionFilterProp="children"
                        filterOption={(input, option:any) =>
                            (option?.children as string)?.toLowerCase()?.includes(input.toLowerCase()) ||
                            (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                        }
                        allowClear
                        placeholder={t("manage_assignAgents.placeholders.agent")}
                        options={agents?.map((el:any) => ({
                            label: `${el.cin} | ${el.firstname} ${el.lastname}`,
                            value: el.id,
                        }))}
                    />
                )
            }
        },
         {
            title: `${t("manage_assignAgents.labels.cardType")}`,
            dataIndex: "card_type",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <Tag color={record.card_type?.id === 1 ? "geekblue" : "pink"}>
                    {record.card_type?.[`nom_${currentLang}`] || record.card_type?.nom_fr}
                </Tag>
            ),
            renderFormItem: () => {
                return (
                    <Select
                        showSearch
                        filterOption={(input, option) =>
                            (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                        }
                        allowClear
                        placeholder={t("manage_assignAgents.placeholders.cardTypes")}
                        options={cardsTypes?.map((el:any) => ({
                            label: el[`nom_${currentLang}`],
                            value: el.id,
                        }))}
                    />
                )
            }
        },
        {
            title: `${t("manage_assignAgents.labels.cardSequence")}`,
            dataIndex: "sequence",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <Tooltip title={`${record.card_type?.nom_fr || record.card_type?.nom_en || record.card_type?.nom_ar}`}>
                    <Tag color={record.card_type?.id === 1 ? "geekblue" : "pink"}>
                        {record.start_serial_number} - {record.end_serial_number}
                    </Tag>
                </Tooltip>
            ),
            search : false,
        },
        {
            title: `${t("manage_assignAgents.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 100,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_assignAgents.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {/* {
                        hasPermission("edit_assign_agents") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    } */}
                    {
                        hasPermission("delete_assign_agents") && (
                            <Popconfirm
                                title={t("manage_assignAgents.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_assignAgents.yes")}
                                cancelText={t("manage_assignAgents.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.sales")}</Link>,
        },
        {
            title: t("manage_assignAgents.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingAssignAgents(record)
        const formValues = {
            id_agent: record.agent.id,
            cardTypes: record.id_card_type,
            ranges: {
                [record.id_card_type]: {
                    start_serial_number: record.start_serial_number,
                    end_serial_number: record.end_serial_number,
                }
            }
        };
        setSelectedTypes([record.id_card_type]);
        form.setFieldsValue(formValues);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingAssignAgents(record);
        const formValues = {
            id_agent: record.agent.id,
            cardTypes: record.id_card_type,
            ranges: {
                [record.id_card_type]: {
                    start_serial_number: record.start_serial_number,
                    end_serial_number: record.end_serial_number,
                }
            }
        };
        setSelectedTypes([record.id_card_type]);
        form.setFieldsValue(formValues);
        setViewMode(false);
        setModalVisible(true);
    };
    
    const handleAdd = () => {
        setEditingAssignAgents(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - VALIDATE SEQUENCE
    |-------------------------------------------------------------------------- */
    const validateSequence = async (cardTypeId: string, start: number, end: number) => {
        if (!start || !end) return;

        if (editingAssignAgents) return;

        setValidatingSequence(prev => ({ ...prev, [cardTypeId]: true }));
        setSequenceValidationResults(prev => ({ ...prev, [cardTypeId]: null }));

        try {
            const result = await dispatch(checkSequenceAvailability({
                id_card_type: Number(cardTypeId),
                start,
                end
            })).unwrap();

            setSequenceValidationResults(prev => ({
                ...prev,
                [cardTypeId]: result.is_available
            }));

            // Set field validation status
            if (!result.is_available) {
                form.setFields([
                    {
                        name: ['ranges', cardTypeId, 'end_serial_number'],
                        errors: [t("manage_assignAgents.errors.sequenceNotAvailable")]
                    }
                ]);
            } else {
                form.setFields([
                    {
                        name: ['ranges', cardTypeId, 'end_serial_number'],
                        errors: []
                    }
                ]);
            }

            return result.is_available;
        } catch (error) {
            console.error('Error validating sequence:', error);
            setSequenceValidationResults(prev => ({ ...prev, [cardTypeId]: false }));

            form.setFields([
                {
                    name: ['ranges', cardTypeId, 'end_serial_number'],
                    errors: [t("manage_assignAgents.errors.sequenceValidationError")]
                }
            ]);

            return false;
        } finally {
            setValidatingSequence(prev => ({ ...prev, [cardTypeId]: false }));
        }
    };

    // Debounced version of validateSequence
    const debouncedValidateSequence = useCallback((cardTypeId: string, start: number, end: number) => {
        // Clear any existing timer for this cardTypeId
        if (debounceTimers.current[cardTypeId]) {
            clearTimeout(debounceTimers.current[cardTypeId]);
        }

        // Set a new timer
        debounceTimers.current[cardTypeId] = setTimeout(() => {
            validateSequence(cardTypeId, start, end);
        }, 1000); // 1000ms debounce time
    }, [validateSequence, debounceTimers]);

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE SALES POINT
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {

        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        // Validate all sequences before submitting
        if (!editingAssignAgents) {
            const ranges = values.ranges || {};
            const validationPromises = Object.entries(ranges).map(async ([cardTypeId, range]: [string, any]) => {
                const start = Number(range.start_serial_number);
                const end = Number(range.end_serial_number);

                try {
                    const result = await dispatch(checkSequenceAvailability({
                        id_card_type: Number(cardTypeId),
                        start,
                        end
                    })).unwrap();

                    return {
                        cardTypeId,
                        isAvailable: result.is_available
                    };
                } catch (error) {
                    return {
                        cardTypeId,
                        isAvailable: false,
                        error
                    };
                }
            });

            const validationResults = await Promise.all(validationPromises);
            const invalidSequences = validationResults.filter(result => !result.isAvailable);

            if (invalidSequences.length > 0) {
                setLoading(false);

                // Set field errors for invalid sequences
                const fieldErrors = invalidSequences.map(result => ({
                    name: ['ranges', result.cardTypeId, 'end_serial_number'],
                    errors: [t("manage_assignAgents.errors.sequenceNotAvailable")]
                }));

                form.setFields(fieldErrors);

                toast.error(t("manage_assignAgents.errors.someSequencesNotAvailable"), {
                    position: 'top-center',
                    autoClose: 5000
                });

                return;
            }
        }

        const transformedValues = {
            ...values,
            ranges: Object.entries(values.ranges || {}).map(([cardTypeId, range]:any) => ({
                cardType: parseInt(cardTypeId),
                ...range
            }))
        };

        const payload = editingAssignAgents
            ? { id: editingAssignAgents.id, ...transformedValues }
            : transformedValues;

        try {
            if (editingAssignAgents) {
                await dispatch(updateAgentCardsAffectation(payload)).unwrap();
            } else {
                await dispatch(storeAgentCardsAffectation(transformedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            setLoading(false);
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_assignAgents.confirmAction"),
            content: editingAssignAgents
                ? t("manage_assignAgents.confirmUpdate")
                : t("manage_assignAgents.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE SALES POINT
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteAgentCardsAffectation(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

   /*|--------------------------------------------------------------------------
   |  - HANDLE RESET
   |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setSelectedTypes([]);
        setValidatingSequence({});
        setSequenceValidationResults({});

        // Clear all debounce timers
        Object.keys(debounceTimers.current).forEach(key => {
            clearTimeout(debounceTimers.current[key]);
        });
        debounceTimers.current = {};

        dispatch(clearSequenceAvailability());
        form.resetFields();
    }

    // Clean up timers when component unmounts
    useEffect(() => {
        return () => {
            Object.keys(debounceTimers.current).forEach(key => {
                clearTimeout(debounceTimers.current[key]);
            });
        };
    }, []);

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_assignAgents.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetAgentCardsAffectations(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={'id'}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_assignAgents.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1100}
                title={
                    viewMode
                        ? t("manage_assignAgents.details")
                        : editingAssignAgents
                            ? t("manage_assignAgents.edit")
                            : t("manage_assignAgents.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_assignAgents.save")}
                footer={viewMode ? null : undefined}
            >
                {viewMode ? (
                    <>
                        <Row gutter={[16, 16]}>
                            <Col xs={12} sm={12}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_assignAgents.labels.agent")}
                                    </div>
                                    <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingAssignAgents?.agent
                                            ? `${editingAssignAgents.agent.cin} | ${editingAssignAgents.agent.firstname} ${editingAssignAgents.agent.lastname}`
                                            : "-"}
                                    </div>
                                </div>
                            </Col>
                            <Col xs={12} sm={12}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_assignAgents.labels.cardTypes")}
                                    </div>
                                    <div className="bg-purple-50 text-purple-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingAssignAgents?.card_type?.[`nom_${currentLang}`] || editingAssignAgents?.card_type?.nom_fr || "-"}
                                    </div>
                                </div>
                            </Col>
                        </Row>

                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={12}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_assignAgents.labels.startNumber")}
                                    </div>
                                    <div className="bg-amber-50 text-amber-700 px-3 py-2 rounded-md text-base font-mono font-medium">
                                        {editingAssignAgents?.start_serial_number || "-"}
                                    </div>
                                </div>
                            </Col>
                            <Col xs={24} sm={12}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_assignAgents.labels.endNumber")}
                                    </div>
                                    <div className="bg-amber-50 text-amber-700 px-3 py-2 rounded-md text-base font-mono font-medium">
                                        {editingAssignAgents?.end_serial_number || "-"}
                                    </div>
                                </div>
                            </Col>
                        </Row>

                        <div className="mb-4">
                            <div className="text-sm font-medium text-gray-500 mb-1">
                                {t("manage_assignAgents.labels.createdAt")}
                            </div>
                            <div className="bg-emerald-50 text-emerald-700 px-3 py-2 rounded-md text-base font-medium">
                                {editingAssignAgents?.created_at ? new Date(editingAssignAgents.created_at).toLocaleDateString() : "-"}
                            </div>
                        </div>
                    </>
                ) : (
                    <Form
                        className="form-inputs"
                        form={form}
                        layout="vertical"
                        onFinish={confirmSubmit}
                    >
                        <Row gutter={[16,16]}>
                            <Col xs={24} sm={12}>
                                <Form.Item
                                    label={t("manage_assignAgents.labels.agent")}
                                    name="id_agent"
                                    rules={[{ required: true, message: t("manage_assignAgents.errors.agentRequired") }]}
                                >
                                    <Select
                                        showSearch
                                        optionFilterProp="children"
                                        filterOption={(input, option:any) =>
                                            (option?.children as string)?.includes(input.toLowerCase())
                                        }
                                        placeholder={t("manage_assignAgents.placeholders.agent")}
                                        >
                                        {agents?.map((el:any) => (
                                            <Select.Option key={el.id} value={el.id}>
                                                {el.cin} | {el.firstname} {el.lastname}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Form.Item
                                    label={t("manage_assignAgents.labels.cardTypes")}
                                    name="cardTypes"
                                    rules={[{ required: true, message: t("manage_assignAgents.errors.cardTypesRequired") }]}
                                >
                                    <Select
                                        placeholder={t("manage_assignAgents.placeholders.cardTypes")}
                                        onChange={(values) => {
                                            setSelectedTypes(values);
                                            form.setFieldValue('ranges', {});

                                            if (Array.isArray(values)) {
                                                values.forEach((cardTypeId) => {
                                                    fetchCardSequenceData(cardTypeId);
                                                });
                                            }
                                        }}
                                        mode="multiple"
                                    >
                                        {cardsTypes?.map((cardType: any) => (
                                            <Select.Option key={cardType.id} value={cardType.id}>
                                                {cardType[`nom_${currentLang}`]}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>
                   
                    {selectedTypes?.map((cardTypeId: number) => {
                        const cardType = cardsTypes?.find((ct: any) => ct.id === cardTypeId);
                        const cardTypeIdStr = cardTypeId.toString();
                        const availableRanges = availableSequences[cardTypeId] || [];
                        const stats = sequenceStats[cardTypeId] || {};
                        const isValidating = validatingSequence[cardTypeIdStr];

                        // Fonction pour valider la séquence lorsque les valeurs changent
                        const handleSequenceChange = () => {
                            const startValue = form.getFieldValue(['ranges', cardTypeIdStr, 'start_serial_number']);
                            const endValue = form.getFieldValue(['ranges', cardTypeIdStr, 'end_serial_number']);

                            if (startValue && endValue) {
                                if (Number(endValue) < Number(startValue)) {
                                    form.setFields([
                                        {
                                            name: ['ranges', cardTypeIdStr, 'end_serial_number'],
                                            errors: [t("manage_assignAgents.errors.endNumberLessThanStart")]
                                        }
                                    ]);
                                    return;
                                }

                                debouncedValidateSequence(cardTypeIdStr, Number(startValue), Number(endValue));
                            }
                        };

                        return (
                            <div key={cardTypeId}>
                                <Divider>
                                    <Tag className="!border shadow-sm px-12 !text-black font-semibold py-1" color={"#FFF"}>
                                        {cardType?.[`nom_${currentLang}`]}
                                    </Tag>
                                </Divider>

                                {/* Statistiques des séquences */}
                                {!viewMode && (
                                    <Row gutter={[16, 16]} className="mb-4">
                                        <Col xs={24}>
                                            <Card
                                                title={
                                                    <div className="flex items-center">
                                                        <span className="mr-2">{t("manage_assignAgents.labels.sequenceStats")}</span>
                                                    </div>
                                                }
                                                size="small"
                                                bordered={false}
                                            >
                                                {stats && !loading ? (
                                                    <Row gutter={[16, 16]}>
                                                        <Col xs={24} sm={8}>
                                                            <Card className="bg-green-50 border-0">
                                                                <Statistic
                                                                    title={
                                                                        <div className="flex items-center text-green-700">
                                                                            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                                                            {t("manage_assignAgents.labels.totalAvailable")}
                                                                        </div>
                                                                    }
                                                                    value={stats.available_count || 0}
                                                                    valueStyle={{ color: '#52c41a', fontWeight: 'bold' }}
                                                                    suffix={
                                                                        <div className="text-xs text-gray-500">
                                                                            {stats.total_count ?
                                                                                `(${Math.round((stats.available_count / stats.total_count) * 100)}%)` :
                                                                                '(0%)'
                                                                            }
                                                                        </div>
                                                                    }
                                                                />
                                                                {stats.total_count > 0 ? (
                                                                    <Progress
                                                                        percent={Math.round((stats.available_count / stats.total_count) * 100)}
                                                                        showInfo={false}
                                                                        strokeColor="#52c41a"
                                                                        className="mt-2"
                                                                        size="small"
                                                                    />
                                                                ): (
                                                                    <Progress
                                                                        percent={0}
                                                                        showInfo={false}
                                                                        strokeColor="#52c41a"
                                                                        className="mt-2"
                                                                        size="small"
                                                                    />
                                                                )}
                                                            </Card>
                                                        </Col>
                                                        <Col xs={24} sm={8}>
                                                            <Card className="bg-red-50 border-0 shadow-sm">
                                                                <Statistic
                                                                    title={
                                                                        <div className="flex items-center text-red-700">
                                                                            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                                                            {t("manage_assignAgents.labels.totalOccupied")}
                                                                        </div>
                                                                    }
                                                                    value={stats.occupied_count || 0}
                                                                    valueStyle={{ color: '#f5222d', fontWeight: 'bold' }}
                                                                    suffix={
                                                                        <div className="text-xs text-gray-500">
                                                                            {stats.total_count ?
                                                                                `(${Math.round((stats.occupied_count / stats.total_count) * 100)}%)` :
                                                                                '(0%)'
                                                                            }
                                                                        </div>
                                                                    }
                                                                />
                                                                {stats.total_count > 0 ? (
                                                                    <Progress
                                                                        percent={Math.round((stats.occupied_count / stats.total_count) * 100)}
                                                                        showInfo={false}
                                                                        strokeColor="#f5222d"
                                                                        className="mt-2"
                                                                        size="small"
                                                                    />
                                                                ): (
                                                                    <Progress
                                                                        percent={0}
                                                                        showInfo={false}
                                                                        strokeColor="#f5222d"
                                                                        className="mt-2"
                                                                        size="small"
                                                                    />
                                                                )}
                                                            </Card>
                                                        </Col>
                                                        <Col xs={24} sm={8}>
                                                            <Card className="bg-blue-50 border-0 shadow-sm">
                                                                <Statistic
                                                                    title={
                                                                        <div className="flex items-center text-blue-700">
                                                                            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                                                            {t("manage_assignAgents.labels.totalCards")}
                                                                        </div>
                                                                    }
                                                                    value={stats.total_count || 0}
                                                                    valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
                                                                />
                                                                <div className="flex items-center mt-2 text-xs text-gray-500">
                                                                    <div className="flex-1 flex items-center">
                                                                        <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                                                                        <span>{Math.round((stats.available_count / (stats.total_count || 1)) * 100)}%</span>
                                                                    </div>
                                                                    <div className="flex-1 flex items-center">
                                                                        <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                                                                        <span>{Math.round((stats.occupied_count / (stats.total_count || 1)) * 100)}%</span>
                                                                    </div>
                                                                </div>
                                                            </Card>
                                                        </Col>
                                                    </Row>
                                                ) : (
                                                    <div className="flex justify-center items-center py-6">
                                                        <Spin />
                                                        <span className="ml-2 text-gray-500">{t("manage_assignAgents.messages.loadingStats")}</span>
                                                    </div>
                                                )}
                                            </Card>
                                        </Col>
                                    </Row>
                                )}

                                {!viewMode && (
                                    <Row gutter={[16, 16]} className="mb-4">
                                        <Col xs={24}>
                                            <div className="border rounded-md p-3">
                                                <div className="flex justify-between items-center mb-2">
                                                    <div className="font-medium flex items-center">
                                                        <span className="mr-2">{t("manage_assignAgents.labels.sequenceVisualization")}</span>
                                                        {sequenceLoading[cardTypeId] && <Spin size="small" />}
                                                    </div>
                                                    <Button

                                                        size="small"
                                                        onClick={() => handleRecalculateRanges(cardTypeId)}
                                                        icon={<span className="mr-1">🔄</span>}
                                                        className="btn-view"
                                                        loading={sequenceLoading[cardTypeId]}
                                                    >
                                                        {t("manage_assignAgents.recalculate")}
                                                    </Button>
                                                </div>

                                                {sequenceLoading[cardTypeId] && !(availableRanges.length > 0 || occupiedSequences[cardTypeId]?.length > 0) ? (
                                                    <div className="flex justify-center items-center py-12">
                                                        <Spin size="default" />
                                                        <span className="ml-3 text-gray-500">{t("manage_assignAgents.messages.loadingSequences")}</span>
                                                    </div>
                                                ) : (availableRanges.length > 0 || occupiedSequences[cardTypeId]?.length > 0) ? (
                                                    <div className="mt-4">
                                                        <div className="flex items-center gap-4 mb-2">
                                                            <div className="flex items-center">
                                                                <div className="w-4 h-4 bg-green-500 rounded-sm mr-1"></div>
                                                                <span className="text-xs">{t("manage_assignAgents.available")}</span>
                                                            </div>
                                                            <div className="flex items-center">
                                                                <div className="w-4 h-4 bg-red-500 rounded-sm mr-1"></div>
                                                                <span className="text-xs">{t("manage_assignAgents.occupied")}</span>
                                                            </div>
                                                        </div>

                                                        <div className="relative">
                                                            {(() => {
                                                                const allRanges = [
                                                                    ...(availableRanges || []),
                                                                    ...(occupiedSequences[cardTypeId] || [])
                                                                ];

                                                                if (allRanges.length === 0) return null;

                                                                const minSequence = Math.min(...allRanges.map(r => r.start_sequence));
                                                                const maxSequence = Math.max(...allRanges.map(r => r.end_sequence));
                                                                const totalRange = maxSequence - minSequence;

                                                                return (
                                                                    <>
                                                                        <div className="h-8 bg-gray-200 rounded-full mb-4 relative">
                                                                            {availableRanges.map((range: any, index: number) => {
                                                                                const startPercent = ((range.start_sequence - minSequence) / totalRange) * 100;
                                                                                const widthPercent = ((range.end_sequence - range.start_sequence + 1) / totalRange) * 100;

                                                                                return (
                                                                                    <Tooltip
                                                                                        key={`available-${index}`}
                                                                                        title={
                                                                                            <div>
                                                                                                <div>{t("manage_assignAgents.available")}</div>
                                                                                                <div>{range.start_sequence} - {range.end_sequence}</div>
                                                                                                <div>({range.end_sequence - range.start_sequence + 1} {t("manage_assignAgents.labels.cardsInRange")})</div>
                                                                                            </div>
                                                                                        }
                                                                                    >
                                                                                        <div
                                                                                            className="absolute h-full bg-[#52C41A] cursor-pointer"
                                                                                            style={{
                                                                                                left: `${startPercent}%`,
                                                                                                width: `${widthPercent}%`,
                                                                                                minWidth: '4px',
                                                                                            }}
                                                                                        ></div>
                                                                                    </Tooltip>
                                                                                );
                                                                            })}

                                                                            {occupiedSequences[cardTypeId]?.map((range: any, index: number) => {
                                                                                const startPercent = ((range.start_sequence - minSequence) / totalRange) * 100;
                                                                                const widthPercent = ((range.end_sequence - range.start_sequence + 1) / totalRange) * 100;

                                                                                return (
                                                                                    <Tooltip
                                                                                        key={`occupied-${index}`}
                                                                                        title={
                                                                                            <div>
                                                                                                <div>{t("manage_assignAgents.occupied")}</div>
                                                                                                <div>{range.start_sequence} - {range.end_sequence}</div>
                                                                                                <div>({range.end_sequence - range.start_sequence + 1} {t("manage_assignAgents.labels.cardsInRange")})</div>
                                                                                                {range.agent && (
                                                                                                    <div>{range.agent.firstname} {range.agent.lastname}</div>
                                                                                                )}
                                                                                            </div>
                                                                                        }
                                                                                    >
                                                                                        <div
                                                                                            className="absolute h-full bg-[#F5222D] cursor-pointer"
                                                                                            style={{
                                                                                                left: `${startPercent}%`,
                                                                                                width: `${widthPercent}%`,
                                                                                                minWidth: '4px',
                                                                                            }}
                                                                                        ></div>
                                                                                    </Tooltip>
                                                                                );
                                                                            })}
                                                                        </div>

                                                                        <div className="flex justify-between text-xs text-gray-600">
                                                                            <div className="font-bold">{minSequence}</div>
                                                                            <div className="font-bold">{maxSequence}</div>
                                                                        </div>
                                                                    </>
                                                                );
                                                            })()}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <Alert
                                                        message={t("manage_assignAgents.messages.noSequences")}
                                                        type="warning"
                                                        showIcon
                                                    />
                                                )}
                                            </div>
                                        </Col>
                                    </Row>
                                )}

                                <Row gutter={[16, 16]}>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.startNumber")}
                                            name={["ranges", cardTypeIdStr, "start_serial_number"]}
                                            rules={[
                                                { required: true, message: t("manage_assignAgents.errors.startNumberRequired") }
                                            ]}
                                        >
                                            <Input
                                                type="number"
                                                disabled={viewMode}
                                                placeholder={t("manage_assignAgents.placeholders.startNumber")}
                                                onChange={() => handleSequenceChange()}
                                                suffix={
                                                    isValidating ? (
                                                        <Spin size="small" />
                                                    ) : sequenceValidationResults[cardTypeIdStr] === true ? (
                                                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                                    ) : sequenceValidationResults[cardTypeIdStr] === false ? (
                                                        <CloseCircleOutlined style={{ color: '#f5222d' }} />
                                                    ) : null
                                                }
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.endNumber")}
                                            name={["ranges", cardTypeIdStr, "end_serial_number"]}
                                            rules={[
                                                { required: true, message: t("manage_assignAgents.errors.endNumberRequired") }
                                            ]}
                                        >
                                            <Input
                                                type="number"
                                                disabled={viewMode || isValidating}
                                                placeholder={t("manage_assignAgents.placeholders.endNumber")}
                                                onChange={() => handleSequenceChange()}
                                                suffix={
                                                    isValidating ? (
                                                        <Spin size="small" />
                                                    ) : sequenceValidationResults[cardTypeIdStr] === true ? (
                                                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                                    ) : sequenceValidationResults[cardTypeIdStr] === false ? (
                                                        <CloseCircleOutlined style={{ color: '#f5222d' }} />
                                                    ) : null
                                                }
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </div>
                        );
                    })}
                    </Form>
                )}
            </Modal>
        </>
    );
}

export default ManageAssignCards;