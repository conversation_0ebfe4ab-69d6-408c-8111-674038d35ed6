export const rgbToHex = (r: number | undefined, g: number | undefined, b: number | undefined): string => {
    // Default to black (0,0,0) if any value is undefined
    const red = r ?? 0;
    const green = g ?? 0;
    const blue = b ?? 0;

    // Ensure values are within valid RGB range (0-255)
    const validRed = Math.min(255, Math.max(0, Math.round(red)));
    const validGreen = Math.min(255, Math.max(0, Math.round(green)));
    const validBlue = Math.min(255, Math.max(0, Math.round(blue)));

    // Convert to hex with padding
    const hex = ((1 << 24) | (validRed << 16) | (validGreen << 8) | validBlue)
        .toString(16)
        .slice(1)
        .toUpperCase();

    return `#${hex}`;
};

/*|--------------------------------------------------------------------------
|  - CHECK FOR MISSED TRIPS
|-------------------------------------------------------------------------- */
export const checkForMissedTrips = (stations: any[], routes: any[]) => {
    try {
        if (!Array.isArray(stations) || !Array.isArray(routes)) {
            console.warn('checkForMissedTrips: stations or routes is not an array');
            return [];
        }

        const missedTrips = [];

        for (let i = 0; i < stations.length - 1; i++) {
            const currentStation = stations[i];
            const nextStation = stations[i + 1];

            if (!currentStation || !nextStation) {
                console.warn(`checkForMissedTrips: station at index ${i} or ${i+1} is undefined`);
                continue;
            }

            // Vérifier si une route existe entre la station actuelle et la suivante
            try {
                const hasRoute = routes.some(
                    (route: any) => {
                        if (!route || !route.station_depart || !route.station_arrival) {
                            return false;
                        }
                        return route.station_depart.id === currentStation.id &&
                               route.station_arrival.id === nextStation.id;
                    }
                );

                if (!hasRoute) {
                    missedTrips.push({
                        from: currentStation.name || `Station ${i}`,
                        to: nextStation.name || `Station ${i+1}`,
                    });
                }
            } catch (routeError) {
                console.error('Error checking route:', routeError);
            }
        }

        return missedTrips;
    } catch (error) {
        console.error('Error in checkForMissedTrips:', error);
        return [];
    }
};

/*|--------------------------------------------------------------------------
|  - Utility function to format image paths
|-------------------------------------------------------------------------- */
export const formatImagePath = (imagePath: string | null | undefined): string => {
    if (!imagePath) return '';

    // Get the base URL from environment variables
    const baseUrl = import.meta.env.VITE_API_URL_IMG_PRV || '';

    // If the path already includes the full URL, return it as is
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath;
    }

    // If the path already starts with /storage but doesn't have the base URL
    if (imagePath.startsWith('/storage/')) {
        return `${baseUrl}${imagePath}`;
    }

    // Otherwise, add both the base URL and /storage/ prefix
    return `${baseUrl}storage/${imagePath}`;
};