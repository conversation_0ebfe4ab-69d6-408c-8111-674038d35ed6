export const stationsData = [
    {
        id: 1,
        name: "<PERSON><PERSON>",
        delegation: {
            id: 1,
            name: "<PERSON><PERSON>"
        },
        governorate: {
            id: 1,
            name: "<PERSON><PERSON>"
        },
        created_at: "2024-02-01",
        station_type: "TERMINUS",
        latitude: 33.8806,
        longitude: 10.1647
    },
    {
        id: 2,
        name: "<PERSON><PERSON>",
        delegation: {
            id: 2,
            name: "<PERSON><PERSON>"
        },
        governorate: {
            id: 2,
            name: "<PERSON><PERSON>"
        },
        created_at: "2024-02-01",
        station_type: "INTER",
        latitude: 33.8907,
        longitude: 10.1892
    },
    {
        id: 3,
        name: "<PERSON>",
        delegation: {
            id: 3,
            name: "<PERSON>"
        },
        governorate: {
            id: 3,
            name: "<PERSON>"
        },
        created_at: "2024-02-02",
        station_type: "INTER",
        latitude: 35.8256,
        longitude: 10.6397
    },
    {
        id: 4,
        name: "<PERSON><PERSON><PERSON>",
        delegation: {
            id: 4,
            name: "<PERSON><PERSON><PERSON>"
        },
        governorate: {
            id: 5,
            name: "<PERSON><PERSON><PERSON>"
        },
        created_at: "2024-02-02",
        station_type: "TERMINUS",
        latitude: 35.8256,
        longitude: 10.6397
    },
    {
        id: 5,
        name: "<PERSON><PERSON><PERSON>",
        delegation: {
            id: 4,
            name: "<PERSON><PERSON><PERSON>"
        },
        governorate: {
            id: 5,
            name: "Hammamet"
        },
        created_at: "2024-02-02",
        station_type: "HIDDEN",
        latitude: 35.8256,
        longitude: 10.6397
    }
];

export const AbnTypesData = [
    { 
        id: 1, 
        name: "Abonnement civil", 
        color: "#cf7689",
        hasCIN: true,
        is_student:false,
        is_impersonal: false,
        created_at: "2023-07-20" 
    },
    { 
        id: 2, 
        name: "Abonnement scolaire",
        hasCIN: false,
        is_student:true,
        is_impersonal: false,
        color: "#c5bb85",
        created_at: "2023-07-21" 
    },
    {
        id: 3,
        name: "Abonnement universitaire",
        hasCIN: true,
        is_student:true,
        is_impersonal: false,
        color: "#6a6ab3",
        created_at: "2023-07-21"
    },
    {
        id: 4,
        name: "Abonnement impersonnel",
        hasCIN: true,
        is_student:false,
        is_impersonal: true,
        color: "#88c3ba",
        created_at: "2023-07-21"
    },
];

export const paymentMethods = [
    { id: 1, name: "Espèce", created_at: "2023-07-20" },
    { id: 2, name: "Chèque", created_at: "2023-07-21" },
];

export const duplicateMotifs = [
    { id: 1, name: "Carte perdue", created_at: "2023-07-20" },
    { id: 2, name: "Carte détruite", created_at: "2023-07-21" },
    { id: 3, name: "Autre", created_at: "2023-07-21" },
];

export const cardFees = [
    {
        id: 1,
        name: "Frais carte Civil",
        amount: 3,
        abn_type: {
            id: 1,
            name: "Abonnement civil",
            color: "#cf7689"
        },
        created_at: "2023-07-20"
    },
    {
        id: 2,
        name: "Frais carte Etudiant",
        amount: 1.5,
        abn_type: {
            id: 2,
            name: "Abonnement scolaire",
            color: "#c5bb85"
        },
        created_at: "2023-07-21"
    },
    {
        id: 3,
        name: "Frais carte Impersonnel",
        abn_type: {
            id: 3,
            name: "Abonnement impersonnel",
            color: "#000000"
        },
        amount: 2,
        created_at: "2023-07-21"
    },
];

export const clientsData = [
    {
        id: 1,
        lastname: "Dupont",
        firstname: "Jean",
        phone: "0601020304",
        genre : "male",
        address: "123 Rue Principale",
        dob:"2000-01-18",
        client_type: {
            id: 2,
            name: "éleve",
            hasCIN: false,
            color: "#c5bb85",
            is_impersonal: false,
            is_student:true,
            created_at: "2024-01-15"
        },
        governorate: {
            id: 1,
            name: "ariana"
        },
        delegation: {
            id: 1,
            name: "Ariana",
        },
        establishment: {
            id : 2,
            name: "FAC DES SCIENCES MATHS",
            delegation: {
                id: 1,
                name: "tunis"
            }
        },
        school_degree:{
            id : 2,
            name: "2ème année primaire",
        },
        cin: null,
        matriculate: "AB123456",
        email: "<EMAIL>",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        lastname: "Durand",
        firstname: "Marie",
        genre : "female",
        phone: "0612345678",
        dob:"2000-01-18",
        client_type: {
            id: 1,
            name: "civil",
            hasCIN: true,
            is_impersonal: false,
            color: "#cf7689",
            is_student:false,
            created_at: "2024-01-18"
        },
        governorate: {
            id: 1,
            name: "ariana"
        },
        delegation: {
            id: 1,
            name: "Ariana",
        },
        establishment: {
            id : 2,
            name: "FAC DES SCIENCES MATHS",
            delegation: {
                id: 1,
                name: "tunis"
            }
        },
        school_degree:{
        },
        address: "456 Avenue République",
        cin: "23455434",
        email: "<EMAIL>",
        created_at: "2023-07-21"
    },
    {
        id: 3,
        lastname: "Martin",
        firstname: "Paul",
        genre : "male",
        phone: "0623456789",
        dob:"2000-01-18",
        client_type: {
            id: 2,
            name: "Etudiant",
            hasCIN: true,
            is_impersonal: false,
            color: "#6a6ab3",
            is_student:true,
            created_at: "2024-01-15"
        },
        governorate: {
            id: 2,
            name: "Tunis"
        },
        delegation: {
            id: 1,
            name: "Ariana",
        },
        establishment: {
            id : 2,
            name: "FAC DES SCIENCES MATHS",
            delegation: {
                id: 1,
                name: "tunis"
            }
        },
        school_degree:{
            id : 2,
            name: "2ème année primaire",
        },
        address: "789 Boulevard Saint-Michel",
        cin: "23455434",
        email: "<EMAIL>",
        created_at: "2023-07-22"
    },
    {
        id: 4,
        lastname: "Dekhil",
        firstname: "Omran",
        genre : "male",
        dob:"2000-01-18",
        governorate: {
            id: 2,
            name: "Tunis"
        },
        delegation: {
            id: 1,
            name: "Ariana",
        },
        client_type: {
            id: 4,
            name: "impersonnelle",
            is_impersonal: true,
            hasCIN: true,
            color: "#88c3ba",
            is_student:false,
            created_at: "2024-01-18"
        },
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "23455434",
        email: "<EMAIL>",
        created_at: "2023-07-22"
    },
];

export const clientSchoolData = [
    {
        id: 1,
        lastname: "Dupont",
        firstname: "Jean",
        client_type:"SCHOOL",
        phone: "0601020304",
        address: "123 Rue Principale",
        cin: "23455434",
        email: "<EMAIL>",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        lastname: "Durand",
        firstname: "Marie",
        client_type:"UNIVERSITY",
        phone: "0612345678",
        address: "456 Avenue République",
        cin: "23455434",
        email: "<EMAIL>",
        created_at: "2023-07-21"
    },
    {
        id: 3,
        lastname: "Martin",
        firstname: "Paul",
        client_type:"UNIVERSITY",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "23455434",
        email: "<EMAIL>",
        created_at: "2023-07-22"
    },
    {
        id: 4,
        lastname: "Dekhil",
        firstname: "Omran",
        phone: "0623456789",
        client_type: {
            id: 2,
            name: "Etudiant",
            hasCIN: true,
            isScholar: true,
            color:"#11885588",
            isStudent:true,
            abn_type:{
                id:2,
                name: "Abonnement scolaire",
                color: "#FFE361",
            },
            created_at: "2024-01-15"
        },
        governorate: {
            id: 2,
            name: "Tunis"
        },
        delegation: {
            id: 1,
            name: "Ariana",
        },
        establishment: {
            id : 1,
            name: "FACULTE DE MEDECINE DE TUNIS",
        },
        school_degree:{
            id : 1,
            name: "1ère année primaire",
        },
        address: "789 Boulevard Saint-Michel",
        cin: "23455434",
        email: "<EMAIL>",
        created_at: "2023-07-22"
    },
];

export const line2Data = [
    {
        id: 1,
        code_line: "L001",
        name: "Ariana-Nabeul",
        service_type: "NORMAL",
        status: "ACTIF",
        created_at: "2024-02-10T12:34:56Z",
        commercial_speed: 60,
        stations: [
            {
                id: 1,
                name: "Ariana",
                order_in_line: 1,
                departure_time: ["08:00","16:00"]
            },
            {
                id: 2,
                name: "Tunis",
                order_in_line: 2,
                departure_time: []
            },
            {
                id: 3,
                name: "Ben Arous",
                order_in_line: 3,
                departure_time: []
            },
            {
                id: 4,
                name: "Nabeul",
                order_in_line: 4,
                departure_time: ["12:00","14:00"]
            }
        ],
        routes: [
            {
                id: 1,
                is_regular: true,
                status:true,
                base_tariff: true,
                inter_station:true,
                number_of_km: 10,
                station_depart: { id: 1, name: "Ariana" },
                station_arrival: { id: 2, name: "Tunis" }
            },
            {
                id: 2,
                is_regular: true,
                status:true,
                base_tariff: 2,
                inter_station:true,
                number_of_km: 15,
                station_depart: { id: 2, name: "Tunis" },
                station_arrival: { id: 3, name: "Ben Arous" }
            },
            {
                id: 3,
                is_regular: true,
                status:true,
                base_tariff: 1,
                inter_station:true,
                number_of_km: 20,
                station_depart: { id: 3, name: "Ben Arous" },
                station_arrival: { id: 4, name: "Nabeul" },
            }
        ]
    }
];

export const routesData = [
    {
        id: 1,
        name: "Trajet 1",
        number_of_km: 25.0,
        status: "ACTIF",
        line : {
            id: 1,
            code_line: "L001",
            name: "Ariana-Nabeul",
            service_type: "NORMAL",
            status: "ACTIF",
            created_at: "2024-02-10T12:34:56Z",
            commercial_speed: 60,
            stations: [
                {
                    id: 1,
                    name: "Ariana",
                    order_in_line: 1,
                    departure_time: ["08:00","16:00"]
                },
                {
                    id: 2,
                    name: "Tunis",
                    order_in_line: 2,
                    departure_time: []
                },
                {
                    id: 3,
                    name: "Ben Arous",
                    order_in_line: 3,
                    departure_time: []
                },
                {
                    id: 4,
                    name: "Nabeul",
                    order_in_line: 4,
                    departure_time: ["12:00","14:00"]
                }
            ],
            routes: [
                {
                    id: 1,
                    is_regular: true,
                    status:true,
                    base_tariff: true,
                    inter_station:true,
                    number_of_km: 10,
                    station_depart: { id: 1, name: "Ariana" },
                    station_arrival: { id: 2, name: "Tunis" }
                },
                {
                    id: 2,
                    is_regular: true,
                    status:true,
                    base_tariff: 2,
                    inter_station:true,
                    number_of_km: 15,
                    station_depart: { id: 2, name: "Tunis" },
                    station_arrival: { id: 3, name: "Ben Arous" }
                },
                {
                    id: 3,
                    is_regular: true,
                    status:true,
                    base_tariff: 1,
                    inter_station:true,
                    number_of_km: 20,
                    station_depart: { id: 3, name: "Ben Arous" },
                    station_arrival: { id: 4, name: "Nabeul" },
                }
            ]
        },
        station_depart: {
            id: 1,
            name: "Station Alpha",
            station_type: "TERMINUS",
            latitude: 36.898556,
            longitude: 10.189567,
            delegation: {
                id: 1,
                name: "Delegation Nord",
                governorate: {
                    id: 1,
                    name: "Tunis"
                }
            }
        },
        station_arrival: {
            id: 2,
            name: "Station Beta",
            station_type: "INTER",
            latitude: 36.912345,
            longitude: 10.195432,
            delegation: {
                id: 2,
                name: "Delegation Sud",
                governorate: {
                    id: 1,
                    name: "Tunis"
                }
            }
        },
        tariff_options: [
            {
                abonnement_type: {
                    id: 1,
                    name: "Type 1",
                    color: "#3b82f6"
                },
                is_regular: true,
                base_tariff: {
                    id: 1,
                    name: "Tarif Standard",
                    tariff_per_km: 0.50,
                    date: "2024-01-01"
                }
            },
            {
                abonnement_type: {
                    id: 2,
                    name: "Type 2",
                    color: "#10b981"
                },
                is_regular: false,
                manual_tariff: 15.0
            }
        ],
        created_at: "2024-03-01 09:00:00",
        updated_at: "2024-03-05 14:30:00"
    },
    {
        id: 2,
        name: "Trajet 2",
        number_of_km: 30.5,
        status: "INACTIF",
        line : {
            id: 1,
            code_line: "L001",
            name: "Ariana-Nabeul",
            service_type: "NORMAL",
            status: "ACTIF",
            created_at: "2024-02-10T12:34:56Z",
            commercial_speed: 60,
            stations: [
                {
                    id: 1,
                    name: "Ariana",
                    order_in_line: 1,
                    departure_time: ["08:00","16:00"]
                },
                {
                    id: 2,
                    name: "Tunis",
                    order_in_line: 2,
                    departure_time: []
                },
                {
                    id: 3,
                    name: "Ben Arous",
                    order_in_line: 3,
                    departure_time: []
                },
                {
                    id: 4,
                    name: "Nabeul",
                    order_in_line: 4,
                    departure_time: ["12:00","14:00"]
                }
            ],
            routes: [
                {
                    id: 1,
                    is_regular: true,
                    status:true,
                    base_tariff: true,
                    inter_station:true,
                    number_of_km: 10,
                    station_depart: { id: 1, name: "Ariana" },
                    station_arrival: { id: 2, name: "Tunis" }
                },
                {
                    id: 2,
                    is_regular: true,
                    status:true,
                    base_tariff: 2,
                    inter_station:true,
                    number_of_km: 15,
                    station_depart: { id: 2, name: "Tunis" },
                    station_arrival: { id: 3, name: "Ben Arous" }
                },
                {
                    id: 3,
                    is_regular: true,
                    status:true,
                    base_tariff: 1,
                    inter_station:true,
                    number_of_km: 20,
                    station_depart: { id: 3, name: "Ben Arous" },
                    station_arrival: { id: 4, name: "Nabeul" },
                }
            ]
        },
        station_depart: {
            id: 1,
            name: "Station Gamma",
            station_type: "HIDDEN",
            latitude: 35.678912,
            longitude: 10.096543,
            delegation: {
                id: 3,
                name: "Delegation Est",
                governorate: {
                    id: 2,
                    name: "Sousse"
                }
            }
        },
        station_arrival: {
            id: 4,
            name: "Station Delta",
            station_type: "TERMINUS",
            latitude: 35.701234,
            longitude: 10.112345,
            delegation: {
                id: 4,
                name: "Delegation Ouest",
                governorate: {
                    id: 2,
                    name: "Sousse"
                }
            }
        },
        tariff_options: [
            {
                abonnement_type: {
                    id: 1,
                    name: "Abonnement Civil",
                    color: "#f59e0b"
                },
                is_regular: true,
                base_tariff: {
                    id: 1,
                    name: "Tarif VIP",
                    tariff_per_km: 0.80,
                    date: "2024-02-01"
                }
            },
            {
                abonnement_type: {
                    id: 33,
                    name: "Abonnement scolaire",
                    color: "#64748b"
                },
                is_regular: false,
                manual_tariff: 18.0
            }
        ],
        created_at: "2024-03-10 10:15:00",
        updated_at: "2024-03-12 16:45:00"
    }
];

export const baseTariffData = [
    {
        id: 1,
        name: "base 1",
        tariffPerKm: "0.8",
        abn_type: {
          id:1,
          name: "Abonnement scolaire"
        },
        date: "2023-07-20",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "base 2",
        tariffPerKm: "0.5",
        abn_type: {
            id:2,
            name: "Abonnement civil"
        },
        date: "2023-07-20",
        created_at: "2023-07-20",
    },
];

export const establishmentsData = [
    {
        id: 1,
        name: "FAC DES SCIENCES MATHS",
        short_name: "F.S.M.P.N.TUNIS",
        establishment_type: {
            id: 2,
            name: "Ecole secondaire"
        },
        delegation: {
            id: 1,
            name: "Tunis Centre"
        },
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "FACULTE DE MEDECINE DE TUNIS",
        short_name: "F.MED.TUNIS",
        establishment_type: {
            id: 3,
            name: "Enseignement de base"
        },
        delegation: {
            id: 1,
            name: "Tunis Centre"
        },
        created_at: "2023-07-21",
    },
    {
        id: 3,
        name: "FAC DES SCIENCES DE BIZERTE",
        short_name: "F.S.BIZERTE",
        establishment_type: {
            id: 1,
            name: "Ecole primaire"
        },
        delegation: {
            id: 2,
            name: "Bizerte"
        },
        created_at: "2023-07-22",
    },
];

export const establishmentTypesData = [
    {
        id: 1,
        name: "Ecole primaire",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "Ecole secondaire",
        created_at: "2023-07-21",
    },
    {
        id: 3,
        name: "Enseignement de base",
        created_at: "2023-07-22",
    },
    {
        id: 4,
        name: "Enseignement universitaire",
        created_at: "2023-07-22",
    },
];

export  const  schoolDegreesData = [
    {
        id : 1,
        name: "1ère année primaire",
        establishment_type : {
            id: 1,
            name:"Ecole primaire"
        },
        age_max:"25",
        created_at: "2024-07-20",
    },
    {
        id : 2,
        name: "2ème année primaire",
        establishment_type : {
            id: 1,
            name:"Ecole primaire"
        },
        age_max: 18,
        created_at: "2023-07-20",
    }
]

export const campaignTypesData = [
    {
        id: 1,
        name: "Campagne scolaire",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "Campagne universitaire",
        created_at: "2023-07-21",
    },
    {
        id: 3,
        name: "Campagne civil",
        created_at: "2023-07-22",
    },
];

export const campaignsData = [
    {
        id: 555,
        name: "2025 - 1er trimestre",
        campaign_type:{
            id:3,
            name: "Campagne civil"
        },
        status: true,
    },
    {
        id: 2,
        name: "2025 - 2ème trimestre",
        campaign_type:{
            id:3,
            name: "Campagne civil"
        },
        status: false,
    },
    {
        id: 3,
        name: "2025 - 3ème trimestre",
        campaign_type:{
            id:3,
            name: "Campagne civil"
        },
        status: true,
    },
    {
        id: 4,
        name: "2025 - 4ème trimestre",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: false,
    },
    {
        id: 5,
        name: "2025 - Vacances d'été",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
    {
        id: 6,
        name: "2025 - Vacances d'hiver",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
    {
        id: 7,
        name: "2025 - Vacances de printemps",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
    {
        id: 8,
        name: "2025 - Vacances d'automne",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
    {
        id: 9,
        name: "2025 - Vacances de Noël",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
    {
        id: 10,
        name: "2025 - Vacances de Pâques",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
    {
        id: 11,
        name: "2025 - Vacances de Toussaint",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
];

export const SalesPeriods = [
    {
        id: 1,
        name: "Période d'inscription anticipée",
        start_date: "2023-07-15",
        end_date: "2023-08-15",
        status: false,
        abn_type:{
            id:2,
            name: "Abonnement scolaire",
            color: "#c5bb85",
        },
        campaign: {
            id: 1,
            name: "2025 - 1er trimestre",
            status: true,
            campaign_type: {
                id: 3,
                name: "Campagne civil"
            }
        }
    },
    {
        id: 2,
        name: "Période d'inscription principale",
        start_date: "2023-08-16",
        end_date: "2026-09-15",
        status: true,
        abn_type:{
            id:1,
            name: "Abonnement civil",
            color: "#cf7689",
        },
        campaign: {
            id: 1,
            name: "2025 - 1er trimestre",
            status: true,
            campaign_type: {
                id: 3,
                name: "Campagne civil"
            }
        }
    }
];

export const governoratesData = [
    {
        id: 1,
        name: "Tunis",
        created_at: "2024-02-05",
        hasPO: true,
    },
    {
        id: 2,
        name: "Ariana",
        created_at: "2024-02-05",
        hasPO: false,
    },
    {
        id: 3,
        name: "Ben Arous",
        created_at: "2024-02-05",
        hasPO: true,
    },
    {
        id: 4,
        name: "Manouba",
        created_at: "2024-02-05",
        hasPO: false,
    },
    {
        id: 5,
        name: "Nabeul",
        created_at: "2024-02-05",
        hasPO: false,
    },
];

export const purchaseOrdersData = [
    { id: 1, reference: "PO001", amount: 1000, status: true, date: "2024-02-10" },
    { id: 2, reference: "PO002", amount: 0, status: false, date: "2024-02-12" },
]

export const delegationsData = [
    { id: 1, name: "Ariana Ville", governorate: { id: 1, name: "Ariana" }, created_at: "2024-02-05" },
    { id: 2, name: "Ettadhamen", governorate: { id: 1, name: "Ariana" }, created_at: "2024-02-05" },
    { id: 3, name: "Ben Arous", governorate: { id: 2, name: "Ben Arous" }, created_at: "2024-02-05" },
    { id: 4, name: "Radès", governorate: { id: 2, name: "Ben Arous" }, created_at: "2024-02-05" },
    { id: 5, name: "Tunis Centre", governorate: { id: 3, name: "Tunis" }, created_at: "2024-02-05" },
];

export const agenciesData = [
    {
        id: 1,
        code: "23F45",
        name: "Agence 1",
        address: "ALnozha, Ariana",
        contact: "+216-93456574",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        code: "87F45",
        name: "Agence 2",
        address: "ALnozha, Ariana",
        contact: "+216-93498875",
        created_at: "2023-07-21",
    },
    {
        id: 3,
        code: "12F45",
        name: "Agence 3",
        address: "ALnozha, Ariana",
        contact: "+216-93400004",
        created_at: "2023-07-22",
    },
];

export const salesPointsData = [
    {
        id: 1,
        name: "Point vente 1",
        address: "10 Rue de Paris",
        phone: "0142030405",
        isActive: true,
        created_at: "2023-07-15",
        sales_period: {
            id: 1,
            name: "Période d'inscription anticipée",
            start_date: "2023-07-15",
            end_date: "2023-08-15",
            isOpen: false,
            campaign: {
                id: 1,
                name: "2025 - 1er trimestre",
                start_date: "2025-03-01",
                end_date: "2025-05-31",
                isOpen: true,
                campaign_type: {
                    id: 3,
                    name: "Campagne civil"
                }
            }
        },
        delegation: {
            id: 1,
            name: "Ariana Ville",
            governorate: {
                id: 1,
                name: "Ariana"
            }
        },
        agents:[
            {
                id: 1,
                lastname: "Dupont",
                firstname: "Jean",
                phone: "0601020304",
                address: "123 Rue Principale",
                cin: "23455422",
                cards_number: 155,
                email: "<EMAIL>",
                created_at: "2023-07-20",
            },
            {
                id: 4,
                lastname: "Dekhil",
                firstname: "Omran",
                phone: "0623456789",
                address: "789 Boulevard Saint-Michel",
                cin: "23455434",
                cards_number: 40,
                email: "<EMAIL>",
                created_at: "2023-07-22",
                sales_point_id: 1,
            },
        ],
        agency:{
            id: 2,
            name: "Agence 2",
            created_at: "2023-07-21",
        },
    },
    {
        id: 2,
        name: "Point vente 2",
        address: "25 Avenue de la République",
        phone: "0472123456",
        isActive: true,
        created_at: "2023-08-01",
        sales_period: {
            id: 2,
            name: "Période d'inscription principale",
            start_date: "2023-08-16",
            end_date: "2023-09-15",
            isOpen: false,
            campaign: {
                id: 1,
                name: "2025 - 1er trimestre",
                start_date: "2025-03-01",
                end_date: "2025-05-31",
                isOpen: true,
                campaign_type: {
                    id: 3,
                    name: "Campagne civil"
                }
            }
        },
        delegation: {
            id: 2,
            name: "Ettadhamen",
            governorate: {
                id: 1,
                name: "Ariana"
            }
        },
        agents: [

        ],
        agency:{
            id: 1,
            name: "Agence 1",
            created_at: "2023-07-21",
        },
    }
]

export const adminsData = [
    {
        id: 1,
        lastname: "Dupont",
        firstname: "Jean",
        phone: "0601020304",
        address: "123 Rue Principale",
        cin: "23455422",
        email: "<EMAIL>",
        created_at: "2023-07-20",
        sales_point_id: 1,
    },
    {
        id: 2,
        lastname: "Durand",
        firstname: "Marie",
        phone: "0612345678",
        address: "456 Avenue République",
        cin: "23455433",
        email: "<EMAIL>",
        created_at: "2023-07-21",
        sales_point_id: 2,
    },
    {
        id: 3,
        lastname: "Martin",
        firstname: "Paul",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "23455411",
        email: "<EMAIL>",
        created_at: "2023-07-22",
        sales_point_id: 1,
    },
    {
        id: 4,
        lastname: "Dekhil",
        firstname: "Omran",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "23455400",
        email: "<EMAIL>",
        created_at: "2023-07-22",
        sales_point_id: 1,
    },
];

export const assignAgentsData = [
    {
        id: 1,
        salesPoint: "point de vente 1",
        agent: `${adminsData[0].firstname} ${adminsData[0].lastname}`,
        salesPeriod: SalesPeriods[0].name,
        cards_number: 55,
        created_at: "2024-01-15",
    },
    {
        id: 2,
        salesPoint: "point de vente 2",
        agent: `${adminsData[1].firstname} ${adminsData[1].lastname}`,
        salesPeriod: SalesPeriods[1].name,
        cards_number: 44,
        created_at: "2024-04-10",
    },
    {
        id: 3,
        salesPoint: "point de vente 3",
        agent: `${adminsData[2].firstname} ${adminsData[2].lastname}`,
        salesPeriod: SalesPeriods[1].name,
        cards_number: 55,
        created_at: "2024-07-05",
    },
];


export const abnPeriodsData = [
    {
        id: 1,
        label: "Semaine",
        periodicity: "WEEKLY",
        max_rest_days: 1,
        created_at: "2024-03-01",
    },
    {
        id: 2,
        label: "Mensuelle",
        periodicity: "MONTHLY",
        max_rest_days: 2,
        created_at: "2024-03-05",
    },
    {
        id: 3,
        label: "Annuelle",
        periodicity: "YEARLY",
        max_rest_days: 2,
        created_at: "2024-03-10",
    },
    {
        id: 4,
        label: "Semestrielle",
        periodicity: "BIANNUAL",
        max_rest_days: 2,
        created_at: "2024-03-15",
    }
];

export const subscriptionsData = [
    {
        id: 1,
        created_at: "2024-07-02",
        abn_type: {
            id: 1,
            name: "Abonnement civil",
            color: "#cf7689",
            hasCIN: true,
            is_student:false,
            is_impersonal: false,
            created_at: "2023-07-20"
        },
        client:{
            id: 3,
            lastname: "Martin",
            firstname: "Paul",
            phone: "0623456789",
            address: "789 Boulevard Saint-Michel",
            cin: "23455444",
            email: "<EMAIL>",
            created_at: "2023-07-22"
        },
        station_depart: {
            id: 1,
            name: "Ariana",
            order_in_line: 1,
            departure_time: ["08:00","16:00"]
        },
        station_arrival: {
            id: 3,
            name: "Ben Arous",
            order_in_line: 3,
            departure_time: []
        },
        line: {
            id: 1,
            code_line: "L001",
            name: "Ariana-Nabeul",
            service_type: "NORMAL",
            status: "ACTIF",
            created_at: "2024-02-10T12:34:56Z",
            commercial_speed: 60,
            stations: [
                {
                    id: 1,
                    name: "Ariana",
                    order_in_line: 1,
                    departure_time: ["08:00","16:00"]
                },
                {
                    id: 2,
                    name: "Tunis",
                    order_in_line: 2,
                    departure_time: []
                },
                {
                    id: 3,
                    name: "Ben Arous",
                    order_in_line: 3,
                    departure_time: []
                },
                {
                    id: 4,
                    name: "Nabeul",
                    order_in_line: 4,
                    departure_time: ["12:00","14:00"]
                }
            ],
            routes: [
                {
                    id: 1,
                    is_regular: true,
                    base_tariff: 20,
                    number_of_km: 10,
                    station_depart: { id: 1, name: "Ariana" },
                    station_arrival: { id: 2, name: "Tunis" }
                },
                {
                    id: 2,
                    is_regular: true,
                    base_tariff: 20,
                    number_of_km: 15,
                    station_depart: { id: 2, name: "Tunis" },
                    station_arrival: { id: 3, name: "Ben Arous" }
                },
                {
                    id: 3,
                    is_regular: true,
                    base_tariff: 20,
                    number_of_km: 20,
                    station_depart: { id: 3, name: "Ben Arous" },
                    station_arrival: { id: 4, name: "Nabeul" },
                }
            ]
        },
        periodicity: {
            id: 2,
            name: "Mensuelle",
            days_number: 30,
            created_at: "2024-01-15"
        },
        rest_days:  {
            id: 1,
        },
        hasVacation: true,
        is_social_affair: false,
        total_amount : 50,
        status: false
    },
];

export const discountsData = [
    {
        id: 1,
        name: "Remise CIVIL",
        client_type:  {
            id: 3,
            name: "Civil",
            hasCIN: true,
            color:"#FF223344",
            isStudent:false,
            abn_type:{
                id:1,
                name: "Abonnement civil",
                color: "#cf7689",
            },
            created_at: "2024-01-18"
        },
        abn_period: [
            { id: 2, name: "Mensuelle", days_number: 30, created_at: "2024-01-15" },
            { id: 3, name: "Semestrielle", days_number: 180, created_at: "2024-01-15" },
            { id: 4, name: "Annuelle", days_number: 365, created_at: "2024-01-15" },
        ],
        discount_percentage: 15,
        created_at: "2024-02-01",
    },
];

export const stockCards = [
    {
        id: 1,
        name: "Stock 2022",
        initial_quantity: 10000000,
        remaining_quantity: 0,
        status: false,
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "Stock 2023",
        initial_quantity: 43210000,
        remaining_quantity: 0,
        status: false,
        created_at: "2023-07-21",
    },
    {
        id: 3,
        name: "Stock 2024",
        initial_quantity: 60000000,
        remaining_quantity: 55000000,
        status: true,
        created_at: "2023-07-22",
    },
    {
        id: 4,
        name: "Stock 2025",
        initial_quantity: 12300000,
        remaining_quantity: 12000000,
        status: true,
        created_at: "2023-07-22",
    },
];

