import { useEffect, useRef, useState, useCallback } from "react";
import {
    <PERSON><PERSON>,
    Modal,
    Form,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
    DatePicker,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined} from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import { useDispatch } from "react-redux";
import { getAdminsAll } from "../../../features/admin/adminSlice.ts";
import { getSalePointAll } from "../../../features/admin/salePointSlice.ts";
import { getSalesPeriodsAll } from "../../../features/admin/salesPeriodsSlice.ts";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import { getStockCardsAll } from "../../../features/admin/stockCardSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";
import { getPaymentMethodsAll } from "../../../features/admin/paymentMethodsSlice.ts";
import { deleteAgentAffectation, getAgentAffectations, storeAgentAffectation, updateAgentAffectation } from "../../../features/admin/agentAffectationSlice.ts";
import dayjs from "dayjs";
import moment from "moment";

function ManageAssignAgents() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch:any = useDispatch<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingAssignAgents, setEditingAssignAgents] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    const agents = useSelector((state: any) => state.admin.items.data);
    const salesPoints = useSelector((state: any) => state.salePoint.items.data);
    const salesPeriods = useSelector((state: any) => state.salesPeriod.items.data);
    const paymentMethods = useSelector((state: any) => state.paymentMethod.items.data);

    /*|--------------------------------------------------------------------------
    |  - FETCH AFFECTATION WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetAgentAffectations = (params: any, sort: any, filter: any) => {
            return dispatch(getAgentAffectations({
                pageNumber,
                perPage: pageSize,
                params, sort, filter
            }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
        }

       const fetchStoreData = async () => {
            try {
                setLoading(true);
                const promises = [];
                if(!agents?.length){
                    promises.push(dispatch(getAdminsAll()).unwrap());
                }
                if(!salesPoints?.length){
                    promises.push(dispatch(getSalePointAll()).unwrap());
                }
                if(!salesPeriods?.length){
                    promises.push(dispatch(getSalesPeriodsAll()).unwrap());
                }
                if(!paymentMethods?.length){
                    promises.push(dispatch(getPaymentMethodsAll()).unwrap());
                }

                promises.push(dispatch(getStockCardsAll()).unwrap());

                await Promise.all(promises);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        }

    useEffect(() => {
        setLoading(true);
        fetchStoreData();
    }, []);



    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_assignAgents.labels.agent")}`,
            dataIndex: "agent",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => `${data.agent.firstname} ${data.agent.lastname}`,
        },
        {
            title: `${t("manage_assignAgents.labels.salesPoint")}`,
            dataIndex: "sale_point",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.sale_point[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_assignAgents.labels.salesPeriod")}`,
            dataIndex: "sale_period",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.sale_period[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_assignAgents.labels.paymentMethods")}`,
            dataIndex: "payment_methods",
            render: (_: any, data: any) => {
                return data.payment_methods.map((method:any) => method[`nom_${currentLang}`]).join(", ");
            },
            responsive: ["xs", "sm", "md", "lg"],
            search: false,
        },
        {
            title: `${t("manage_assignAgents.labels.startDate")}`,
            dataIndex: "date_start",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_assignAgents.labels.endDate")}`,
            dataIndex: "date_end",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_assignAgents.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 100,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_assignAgents.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_assign_agents") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_assign_agents") && (
                            <Popconfirm
                                title={t("manage_assignAgents.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_assignAgents.yes")}
                                cancelText={t("manage_assignAgents.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.sales")}</Link>,
        },
        {
            title: t("manage_assignAgents.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingAssignAgents(record);

        const formValues = {
            id_sale_point: record.sale_point.id,
            id_agent: record.agent.id,
            id_sale_period: record.sale_period.id,
            payment_methods: record.payment_methods.map((method:any) => method.id),
            date_start: record.date_start ? dayjs(record.date_start) : null,
            date_end: record.date_end ? dayjs(record.date_end) : null,
        };
        form.setFieldsValue(formValues);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingAssignAgents(record);
        const formValues = {
            id_sale_point: record.sale_point.id,
            id_agent: record.agent.id,
            id_sale_period: record.sale_period.id,
            payment_methods: record.payment_methods.map((method:any) => method.id),
            date_start: record.date_start ? dayjs(record.date_start) : null,
            date_end: record.date_end ? dayjs(record.date_end) : null,
        };

        form.setFieldsValue(formValues);
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingAssignAgents(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE AGENT AFFECTATION
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);


        const formattedValues = {
            ...values,  
            date_start: dayjs(values.date_start).format("YYYY-MM-DD"),
            date_end: dayjs(values.date_end).format("YYYY-MM-DD"),
        };

        const payload = editingAssignAgents
            ? { id: editingAssignAgents.id, ...formattedValues }
            : formattedValues;

        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingAssignAgents) {
                await dispatch(updateAgentAffectation(payload)).unwrap();
            } else {
                await dispatch(storeAgentAffectation(formattedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            setLoading(false);
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_assignAgents.confirmAction"),
            content: editingAssignAgents
                ? t("manage_assignAgents.confirmUpdate")
                : t("manage_assignAgents.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE AGENT AFFECTATION
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteAgentAffectation(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

   /*|--------------------------------------------------------------------------
   |  - HANDLE RESET
   |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_assignAgents.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetAgentAffectations(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={'id'}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_assignAgents.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1100}
                title={
                    viewMode
                        ? t("manage_assignAgents.details")
                        : editingAssignAgents
                            ? t("manage_assignAgents.edit")
                            : t("manage_assignAgents.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_assignAgents.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    {
                        !viewMode ? (
                            <>
                                <Row gutter={[16,16]}>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.salesPoint")}
                                            name="id_sale_point"
                                            rules={[{ required: true, message: t("manage_assignAgents.errors.salesPointRequired") }]}
                                        >
                                            <Select placeholder={t("manage_assignAgents.placeholders.salesPoint")} disabled={viewMode}>
                                                {salesPoints?.map((el:any) => (
                                                    <Select.Option key={el.id} value={el.id}>
                                                        {el[`nom_${currentLang}`]}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.agent")}
                                            name="id_agent"
                                            rules={[{ required: true, message: t("manage_assignAgents.errors.agentRequired") }]}
                                        >
                                            <Select 
                                                showSearch
                                                optionFilterProp="children"
                                                filterOption={(input, option:any) =>
                                                    (option?.children as string)?.includes(input.toLowerCase())
                                                }
                                                placeholder={t("manage_assignAgents.placeholders.agent")} 
                                                disabled={viewMode}
                                                >
                                                {agents?.map((el:any) => (
                                                    <Select.Option key={el.id} value={el.id}>
                                                        {el.cin} | {el.firstname} {el.lastname}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        </Form.Item>

                                    </Col>
                                </Row>
                                <Row gutter={[16,16]}>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.salesPeriod")}
                                            name="id_sale_period"
                                            rules={[{ required: true, message: t("manage_assignAgents.errors.salesPeriodRequired") }]}
                                        >
                                            <Select placeholder={t("manage_assignAgents.placeholders.salesPeriod")} disabled={viewMode}>
                                                {salesPeriods?.map((el:any) => (
                                                    <Select.Option key={el.id} value={el.id}>
                                                        {el[`nom_${currentLang}`]}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.paymentMethods")}
                                            name="payment_methods"
                                            rules={[{ required: true, message: t("manage_assignAgents.errors.paymentMethodsRequired") }]}
                                        >
                                            <Select mode="multiple" placeholder={t("manage_assignAgents.placeholders.paymentMethods")} disabled={viewMode}>
                                                {paymentMethods?.map((el:any) => (
                                                    <Select.Option key={el.id} value={el.id}>
                                                        {el[`nom_${currentLang}`]}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        </Form.Item>    
                                    </Col>
                                </Row>

                                <Row gutter={16}>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.startDate")}
                                            name="date_start"
                                            rules={[{ required: true, message: t("manage_assignAgents.errors.startDateRequired") }]}
                                        >
                                            <DatePicker
                                                className="w-full"
                                                format="YYYY-MM-DD"
                                                disabled={viewMode}
                                                placeholder={t("manage_assignAgents.placeholders.startDate")}
                                                onChange={(date) => {
                                                    if (moment.isMoment(date)) {
                                                        form.setFieldsValue({ end_date: date.format("YYYY-MM-DD") });
                                                    }
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.endDate")}
                                            name="date_end"
                                            rules={[{ required: true, message: t("manage_assignAgents.errors.endDateRequired") }]}
                                        >
                                            <DatePicker
                                                className="w-full"
                                                format="YYYY-MM-DD"
                                                disabled={viewMode}
                                                placeholder={t("manage_assignAgents.placeholders.endDate")}
                                                onChange={(date) => {
                                                    if (moment.isMoment(date)) {
                                                        form.setFieldsValue({ end_date: date.format("YYYY-MM-DD") });
                                                    }
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </>
                        ): (
                            <>
                                 <div className="view-mode-container bg-white rounded p-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 pb-6 border-b border-gray-100">
                                        <div>
                                            <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                                                {t("manage_assignAgents.labels.salesPoint")}
                                            </h4>
                                            <div className="inline-flex items-center bg-blue-50 text-blue-700 px-3 py-1.5 rounded-full text-sm font-medium">
                                                {editingAssignAgents?.sale_point[`nom_${currentLang}`]}
                                            </div>
                                        </div>

                                        <div>
                                            <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                                                {t("manage_assignAgents.labels.agent")}
                                            </h4>
                                            <div className="flex flex-col">
                                                <div className="inline-flex items-center bg-emerald-50 text-emerald-700 px-3 py-1.5 rounded-full text-sm font-medium mb-1">
                                                {editingAssignAgents?.agent.firstname} {editingAssignAgents?.agent.lastname}
                                                </div>
                                                <div className="text-gray-400 text-xs font-medium mt-1">
                                                 {t("manage_users.labels.cin")} : {editingAssignAgents?.agent.cin}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 pb-6 border-b border-gray-100">
                                        <div>
                                            <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                                                {t("manage_assignAgents.labels.salesPeriod")}
                                            </h4>
                                            <div className="inline-flex items-center bg-purple-50 text-purple-700 px-3 py-1.5 rounded-full text-sm font-medium">
                                                {editingAssignAgents?.sale_period[`nom_${currentLang}`]}
                                            </div>
                                        </div>

                                        <div>
                                            <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                                                {t("manage_assignAgents.labels.paymentMethods")}
                                            </h4>
                                            <div className="flex flex-wrap gap-2">
                                                {editingAssignAgents?.payment_methods?.map((method: any) => (
                                                <div 
                                                    key={method.id} 
                                                    className="bg-amber-50 text-amber-700 px-3 py-1 rounded-full font-medium"
                                                >
                                                    {method[`nom_${currentLang}`]}
                                                </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mb-6">
                                        <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                                        {t("manage_assignAgents.labels.assignmentPeriod")}
                                        </h4>
                                        <div className="flex flex-col sm:flex-row items-center gap-3">
                                        <div className="bg-cyan-50 text-cyan-700 px-3 py-1.5 rounded-full text-sm font-medium">
                                            {dayjs(editingAssignAgents?.date_start).format("DD MMM YYYY")}
                                        </div>
                                        <div className="text-gray-300 hidden sm:block">→</div>
                                        <div className="bg-cyan-50 text-cyan-700 px-3 py-1.5 rounded-full text-sm font-medium">
                                            {dayjs(editingAssignAgents?.date_end).format("DD MMM YYYY")}
                                        </div>
                                        </div>
                                    </div>

                                    {editingAssignAgents?.created_at && (
                                        <div className="pt-4 border-t border-gray-100">
                                        <div className="text-gray-400 text-xs font-medium">
                                            {t("manage_assignAgents.labels.createdAt")}: 
                                            <span className="ml-1 text-gray-500">
                                            {dayjs(editingAssignAgents.created_at).format("DD MMM YYYY HH:mm")}
                                            </span>
                                        </div>
                                        </div>
                                    )}
                                    </div>

                            </>
                        )
                    }
                </Form>
            </Modal>
        </>
    );
}

export default ManageAssignAgents;