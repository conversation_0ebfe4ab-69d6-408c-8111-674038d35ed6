import { useEffect, useState } from "react";
import {
    Modal,
    Descriptions,
    Tag,
    Spin
} from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { getSocialAffair } from "../../features/admin/socialAffairSlice.ts";

interface SocialAffairDetailsProps {
    modalVisible: boolean;
    onCancel: () => void;
    socialAffair: any;
}

function SocialAffairDetails({
    modalVisible,
    onCancel,
    socialAffair
}: SocialAffairDetailsProps) {
    const { t } = useTranslation();
    const dispatch: any = useDispatch();

    const [loading, setLoading] = useState(false);
    const [socialAffairDetails, setSocialAffairDetails] = useState<any>(null);

    useEffect(() => {
        const fetchSocialAffairDetails = async () => {
            if (socialAffair?.id) {
                setLoading(true);
                try {
                    const result = await dispatch(getSocialAffair(socialAffair.id)).unwrap();
                    setSocialAffairDetails(result);
                } catch (error) {
                    console.error("Failed to fetch social affair details:", error);
                } finally {
                    setLoading(false);
                }
            } else {
                setSocialAffairDetails(socialAffair);
            }
        };

        fetchSocialAffairDetails();
    }, [dispatch, socialAffair]);

    return (
        <Modal
            width={900}
            title={t("social_affairs.details")}
            open={modalVisible}
            onCancel={onCancel}
            footer={null}
        >
            {loading || !socialAffairDetails ? (
                <div className="flex justify-center items-center py-10">
                    <Spin />
                </div>
            ) : (
                <Descriptions bordered column={{ xs: 1, sm: 2, md: 2, lg: 2 }}>
                    <Descriptions.Item label={t("social_affairs.labels.governorate")}>
                        {socialAffairDetails.governorate?.nom_fr || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.delegation")}>
                        {socialAffairDetails.delegation || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.nom_parent")}>
                        {socialAffairDetails.nom_parent || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.cin_parent")}>
                        {socialAffairDetails.cin_parent || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.telephone")}>
                        {socialAffairDetails.telephone || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.nom_complet")}>
                        {socialAffairDetails.nom_complet || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.niveau_etude")}>
                        {socialAffairDetails.niveau_etude || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.trajet_requise")}>
                        {socialAffairDetails.trajet_requise || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.eleve_etudiant")}>
                        {socialAffairDetails.eleve_etudiant ? (
                            <Tag color="success">{t("common.yes")}</Tag>
                        ) : (
                            <Tag color="error">{t("common.no")}</Tag>
                        )}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.societe")}>
                        {socialAffairDetails.societe ? (
                            <Tag color="success">{t("common.yes")}</Tag>
                        ) : (
                            <Tag color="error">{t("common.no")}</Tag>
                        )}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.academic_year")}>
                        {socialAffairDetails.academic_year ? (
                            socialAffairDetails.academic_year.code
                        ) : (
                            <span className="text-gray-400">{t("common.na")}</span>
                        )}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.createdAt")}>
                        {socialAffairDetails.created_at || "-"}
                    </Descriptions.Item>

                    <Descriptions.Item label={t("social_affairs.labels.updatedAt")}>
                        {socialAffairDetails.updated_at || "-"}
                    </Descriptions.Item>
                </Descriptions>
            )}
        </Modal>
    );
}

export default SocialAffairDetails;
