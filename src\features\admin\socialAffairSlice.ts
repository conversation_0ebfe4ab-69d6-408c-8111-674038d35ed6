import { createAsyncThunk } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/social-affairs';

export const getSocialAffairsAll: any = createAsyncThunk(
    "getSocialAffairsAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSocialAffairs: any = createAsyncThunk(
    "getSocialAffairs",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params:any,
            sort:any,
            filter:any
        },
        thunkAPI:any
    ) => {
        try {
            const { governorate_id, delegation, nom_parent, cin_parent, telephone, nom_complet, niveau_etude, trajet_requise } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (governorate_id) {
                searchParams.push(`governorate_id:${governorate_id}`);
            }
            if (delegation) {
                searchParams.push(`delegation:${delegation}`);
            }
            if (nom_parent) {
                searchParams.push(`nom_parent:${nom_parent}`);
            }
            if (cin_parent) {
                searchParams.push(`cin_parent:${cin_parent}`);
            }
            if (telephone) {
                searchParams.push(`telephone:${telephone}`);
            }
            if (nom_complet) {
                searchParams.push(`nom_complet:${nom_complet}`);
            }
            if (niveau_etude) {
                searchParams.push(`niveau_etude:${niveau_etude}`);
            }
            if (trajet_requise) {
                searchParams.push(`trajet_requise:${trajet_requise}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSocialAffairsByGovernorate: any = createAsyncThunk(
    "getSocialAffairsByGovernorate",
    async (
        data: {
            governorate_id: number;
            pageNumber: number;
            perPage: number;
            params?: any;
            sort?: any;
        },
        thunkAPI:any
    ) => {
        try {
            const { governorate_id, pageNumber, perPage, params = {}, sort = {} } = data;

            let url = `${URL}/governorate/${governorate_id}?page=${pageNumber}&perPage=${perPage}`;

            const searchParams = [];
            for (const [key, value] of Object.entries(params)) {
                if (value) {
                    searchParams.push(`${key}:${value}`);
                }
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSocialAffair: any = createAsyncThunk(
    "getSocialAffair",
    async (id: number, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeSocialAffair: any = createAsyncThunk(
    "storeSocialAffair",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        }  catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateSocialAffair: any = createAsyncThunk(
    "updateSocialAffair",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;

            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteSocialAffair: any = createAsyncThunk(
    "deleteSocialAffair",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const importSocialAffairs: any = createAsyncThunk(
    "importSocialAffairs",
    async (data: FormData, thunkAPI:any) => {
        try {
            const url:string = `${URL}/import`;
            const resp:any = await api.post(url, data, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const verifySocialAffair = createAsyncThunk(
    "verifySocialAffair",
    async (data: {
        identifier: string,
        dob: string,
        eleve_etudiant: "eleve" | "etudiant" 
    }, thunkAPI: any) => {
        try {
            const url: string = `${URL}/verify`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const verifySocialAffairCinParent = createAsyncThunk(
    "verifySocialAffair",
    async (data: {
        final_amount: number | null,
        cin_parent: string,
    }, thunkAPI: any) => {
        try {
            const url: string = `${URL}/verify`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

