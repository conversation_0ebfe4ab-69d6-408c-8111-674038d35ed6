import React, {ReactNode, useState} from 'react';
import {Layout, Avatar, Dropdown, theme, Spin, Badge} from 'antd';
import {useTranslation} from "react-i18next";
import {LanguageSelector} from "../index.ts";
import {useDispatch, useSelector} from "react-redux";
import {AppDispatch} from "../../features/store.ts";
import {logout} from "../../features/auth/authSlice.ts";
import {useNavigate} from "react-router-dom";
import { toast } from 'react-toastify';

const { Header } = Layout;

interface DashboardHeaderProps {
    children?: ReactNode;
}

const AuthHeader: React.FC<DashboardHeaderProps> = ({ children }) => {
    const { token } = theme.useToken();
    const {t, i18n} = useTranslation();

    const dispatch = useDispatch<AppDispatch>();
    const [loading, setLoading] = useState(false);

    const navigate = useNavigate();
    const { user } = useSelector((state: any) => state.auth);

    const userMenuItems = [
        { key: 'profile', label: user?.firstname + ' ' + user?.lastname },
        { key: 'logout', label: t("auth_header.logout") },
    ];

    const handleMenuClick = (e: any) => {
        if (e.key === 'logout') {
            setLoading(true);
            dispatch(logout())
                .then(() => {
                    navigate('/');
                })
                .catch(() => {
                    toast.error(t("messages.error"));
                })
                .finally(() => {
                    setLoading(false);
                });
        }
        if(e.key === 'profile') {
            navigate('/auth/profile');
        }
    };

    return (
        <Header
            style={{
                padding: `0 ${token.paddingLG}px`,
                background: token.colorBgContainer,
                borderBottom: `1px solid ${token.colorBorderSecondary}`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
            }}
        >
            <div className="flex items-center">
                {children}
            </div>
            <div className="flex items-center">
                <LanguageSelector />
               
                <Dropdown menu={{ items: userMenuItems, onClick: handleMenuClick }} placement="bottomRight">
                    <Badge dot color={"green"}
                           style={i18n.language !== "ar" ? { top: "25px", right: "3px" } : { top: "25px", left: "3px" }}
                    >
                        <Avatar
                            src={`https://ui-avatars.com/api/?name=${user?.firstname}+${user?.lastname}&background=555555&color=fff`}
                            className="shadow-md cursor-pointer"
                        />
                    </Badge>
                </Dropdown>
                {loading && <Spin style={{ marginLeft: 10 }} />}
            </div>
        </Header>
    );
};

export default AuthHeader;