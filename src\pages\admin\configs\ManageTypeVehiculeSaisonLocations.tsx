import { useRef, useState, useEffect } from "react";
import {
  Button,
  Modal,
  Form,
  Input,
  Popconfirm,
  Breadcrumb,
  Row,
  Col,
  Select,
  InputNumber,
  Switch,
  Table,
  Tooltip,
  Space,
  Card,
} from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
  getTypeVehiculeSaisonLocations,
  storeTypeVehiculeSaisonLocation,
  updateTypeVehiculeSaisonLocation,
  deleteTypeVehiculeSaisonLocation,
  bulkStoreTypeVehiculeSaisonLocation,
} from "../../../features/admin/typeVehiculeSaisonLocationSlice";
import { getTypeVehiculesAll } from "../../../features/admin/typeVehiculeSlice";
import { getLocationSeasonsAll } from "../../../features/admin/locationSeasonSlice";
import { hasPermission } from "../../../helpers/permissions";

function ManageTypeVehiculeSaisonLocations() {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  const dispatch: any = useDispatch();
  const actionRef = useRef<any>();

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editingPricing, setEditingPricing] = useState<any>(null);
  const [form] = Form.useForm();

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState(1);

  const [vehicleTypes, setVehicleTypes] = useState<any[]>([]);
  const [locationSeasons, setLocationSeasons] = useState<any[]>([]);

  /*|--------------------------------------------------------------------------
    | FETCH ALL PRICING WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
  const handleGetPricing = (params: any, sort: any, filter: any) => {
    return dispatch(
      getTypeVehiculeSaisonLocations({
        pageNumber,
        perPage: pageSize,
        params,
        sort,
        filter,
      })
    )
      .unwrap()
      .then((originalPromiseResult: any) => {
        setTotal(originalPromiseResult.meta.total);
        originalPromiseResult.data.forEach((item: any) => {
          item.key = item.id;
        });
        return originalPromiseResult.data;
      })
      .catch((rejectedValueOrSerializedError: any) => {
        console.log(rejectedValueOrSerializedError);
      });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const vehicleTypesResult = await dispatch(
          getTypeVehiculesAll()
        ).unwrap();
        setVehicleTypes(vehicleTypesResult.data || []);

        const seasonsResult = await dispatch(getLocationSeasonsAll()).unwrap();
        console.log(seasonsResult);

        setLocationSeasons(seasonsResult.data || []);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [dispatch]);

  /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
  const columns: any = [
    {
      title: `${t("manage_typeVehiculeSaisonLocations.labels.vehicleType")}`,
      dataIndex: "id_type_vehicule",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) =>
        record.type_vehicule[`nom_${currentLang}`] || "-",
      valueEnum: vehicleTypes.reduce((acc: any, type: any) => {
        acc[type.id] = { text: type[`nom_${currentLang}`] };
        return acc;
      }, {}),
    },
    {
      title: `${t("manage_typeVehiculeSaisonLocations.labels.season")}`,
      dataIndex: "id_saison_location",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) =>
        record.season?.[`nom_${currentLang}`] || "-",
      valueEnum: locationSeasons.reduce((acc: any, season: any) => {
        acc[season.id] = { text: season[`nom_${currentLang}`] };
        return acc;
      }, {}),
    },
    {
      title: `${t("manage_typeVehiculeSaisonLocations.labels.pricePerKm")}`,
      dataIndex: "prix_km",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => record.prix_km,
    },
    {
      title: `${t("manage_typeVehiculeSaisonLocations.labels.status")}`,
      dataIndex: "status",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => (
        <Switch checked={record.status} disabled size="small" />
      ),
      valueType: "select",
      valueEnum: {
        "1": {
          text: t("common.active"),
          status: "Success",
        },
        "0": {
          text: t("common.inactive"),
          status: "Error",
        },
      },
    },
    {
      title: `${t("manage_typeVehiculeSaisonLocations.labels.actions")}`,
      fixed: "right",
      width: 170,
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => (
        <div className="flex gap-1">
          <Button
            className="btn-view"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          {
            hasPermission("edit_vehicle_season_pricing") && (
              <Button
                className="btn-edit"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            )
          }
          {
            hasPermission("delete_vehicle_season_pricing") && (
              <Popconfirm
                title={t("manage_typeVehiculeSaisonLocations.confirmDelete")}
                onConfirm={() => handleDelete(record.id)}
                okText={t("manage_typeVehiculeSaisonLocations.yes")}
                cancelText={t("manage_typeVehiculeSaisonLocations.no")}
              >
                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
              </Popconfirm>
            )
          }

        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    {
      title: (
        <Link className="!bg-white" to="/auth/admin-dashboard">
          {t("auth_sidebar.categories.busLocations")}
        </Link>
      ),
    },

    {
      title: t("manage_typeVehiculeSaisonLocations.title"),
    },
  ];

  /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
  const handleView = (record: any) => {
    setEditingPricing(record);
    form.setFieldsValue({
      id: record.id,
      id_type_vehicule: record.id_type_vehicule,
      id_saison_location: record.id_saison_location,
      prix_km: record.prix_km,
      status: record.status,
    });
    setViewMode(true);
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingPricing(record);
    form.setFieldsValue({
      id: record.id,
      id_type_vehicule: record.id_type_vehicule,
      id_saison_location: record.id_saison_location,
      prix_km: record.prix_km,
      status: record.status,
    });
    setViewMode(false);
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingPricing(null);
    form.resetFields();
    setViewMode(false);
    setModalVisible(true);
  };

  /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE PRICING
    |-------------------------------------------------------------------------- */
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    const payload = editingPricing
      ? { id: editingPricing.id, ...values }
      : values;
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      if (editingPricing) {
        await dispatch(updateTypeVehiculeSaisonLocation(payload)).unwrap();
      } else {
        await dispatch(storeTypeVehiculeSaisonLocation(values)).unwrap();
      }
      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
      handleReset();
    } catch (error: any) {
      const fieldErrors: any = Object.keys(error.errors || {}).map(
        (field: any) => ({
          name: field,
          errors: [error.errors[field][0]],
        })
      );
      form.setFields(fieldErrors);
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  const confirmSubmit = (values: any) => {
    const modal = Modal.confirm({
      title: t("manage_typeVehiculeSaisonLocations.confirmAction"),
      content: editingPricing
        ? t("manage_typeVehiculeSaisonLocations.confirmUpdate")
        : t("manage_typeVehiculeSaisonLocations.confirmAdd"),
      okText: t("common.yes"),
      cancelText: t("common.no"),
      onOk: async () => {
        modal.destroy();
        await handleFormSubmit(values);
      },
      centered: true,
    });
  };

  /*|--------------------------------------------------------------------------
    |  - DELETE PRICING
    |-------------------------------------------------------------------------- */
  const handleDelete = async (id: number) => {
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      await dispatch(deleteTypeVehiculeSaisonLocation(id)).unwrap();
      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
    } catch (error: any) {
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    }
  };

  /*|--------------------------------------------------------------------------
     |  - HANDLE RESET
     |-------------------------------------------------------------------------- */
  const handleReset = () => {
    setModalVisible(false);
    form.resetFields();
  };

  return (
    <>
      <Breadcrumb className="mb-5" items={breadcrumbItems} />

      <Row>
        <Col span={24}>
          <ProTable
            headerTitle={t("manage_typeVehiculeSaisonLocations.title")}
            columns={columns}
            actionRef={actionRef}
            cardBordered
            request={async (params: any, sort: any, filter: any) => {
              setLoading(true);
              const dataFilter: any = await handleGetPricing(
                params,
                sort,
                filter
              );
              setLoading(false);
              return {
                data: dataFilter,
                success: true,
              };
            }}
            pagination={{
              pageSize,
              total,
              onChange: (page) => setPageNumber(page),
              onShowSizeChange: (_, size) => setPageSize(size),
            }}
            sortDirections={["ascend", "descend"]}
            scroll={{
              x: 800,
            }}
            search={{
              labelWidth: "auto",
              className: "bg-[#FAFAFA]",
            }}
            loading={loading}
            toolBarRender={() => [
              <Button key="add" onClick={handleAdd} className="btn-add">
                {t("manage_typeVehiculeSaisonLocations.add")}
              </Button>,
            ]}
          />
        </Col>
      </Row>

      <Modal
        width={900}
        open={modalVisible}
        title={
          viewMode
            ? t("manage_typeVehiculeSaisonLocations.details")
            : editingPricing
            ? t("manage_typeVehiculeSaisonLocations.edit")
            : t("manage_typeVehiculeSaisonLocations.add")
        }
        onCancel={handleReset}
        footer={
          viewMode
            ? [
                <Button key="close" onClick={handleReset}>
                  {t("common.close")}
                </Button>,
              ]
            : [
                <Button key="cancel" onClick={handleReset}>
                  {t("common.cancel")}
                </Button>,
                <Button
                  key="submit"
                  type="primary"
                  loading={loading}
                  onClick={() => form.submit()}
                >
                  {t("common.save")}
                </Button>,
              ]
        }
      >
        <Form
          className="form-inputs"
          form={form}
          layout="vertical"
          onFinish={confirmSubmit}
          disabled={viewMode}
        >
          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t(
                  "manage_typeVehiculeSaisonLocations.labels.vehicleType"
                )}
                name="id_type_vehicule"
                rules={[
                  {
                    required: true,
                    message: t(
                      "manage_typeVehiculeSaisonLocations.errors.vehicleTypeRequired"
                    ),
                  },
                ]}
              >
                <Select
                  placeholder={t(
                    "manage_typeVehiculeSaisonLocations.placeholders.selectVehicleType"
                  )}
                  disabled={viewMode}
                  showSearch
                  optionFilterProp="children"
                >
                  {vehicleTypes.map((type) => (
                    <Select.Option key={type.id} value={type.id}>
                      {type[`nom_${currentLang}`]}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t("manage_typeVehiculeSaisonLocations.labels.season")}
                name="id_saison_location"
                rules={[
                  {
                    required: true,
                    message: t(
                      "manage_typeVehiculeSaisonLocations.errors.seasonRequired"
                    ),
                  },
                ]}
              >
                <Select
                  placeholder={t(
                    "manage_typeVehiculeSaisonLocations.placeholders.selectSeason"
                  )}
                  disabled={viewMode}
                  showSearch
                  optionFilterProp="children"
                >
                  {locationSeasons.map((season) => (
                    <Select.Option key={season.id} value={season.id}>
                      {season[`nom_${currentLang}`]}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t(
                  "manage_typeVehiculeSaisonLocations.labels.pricePerKm"
                )}
                name="prix_km"
                rules={[
                  {
                    required: true,
                    message: t(
                      "manage_typeVehiculeSaisonLocations.errors.priceRequired"
                    ),
                  },
                 
                  () => ({
                    validator(_, value) {
                      console.log(value > 0);
                      console.log(value > 0);
                      if (!value) {
                        return Promise.reject();
                      }
                      if (value < 0) {
                        return Promise.reject(
                          t(
                            "manage_typeVehiculeSaisonLocations.errors.pricePositive"
                          )
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <Input
                  style={{ width: "100%" }}
                  min={0}
                  step={0.01}
                  disabled={viewMode}
                  placeholder={t(
                    "manage_typeVehiculeSaisonLocations.placeholders.enterPrice"
                  )}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t("manage_typeVehiculeSaisonLocations.labels.status")}
                name="status"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch disabled={viewMode} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
}

export default ManageTypeVehiculeSaisonLocations;
