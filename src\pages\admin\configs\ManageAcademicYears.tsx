import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    DatePicker
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
    getAcademicYears,
    storeAcademicYear,
    updateAcademicYear,
    deleteAcademicYear
} from "../../../features/admin/academicYearSlice";
import dayjs from "dayjs";

function ManageAcademicYears() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;
    const dispatch: any = useDispatch();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingAcademicYear, setEditingAcademicYear] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
    | FETCH ALL ACADEMIC YEARS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetAcademicYears = (params: any, sort: any, filter: any) => {
        return dispatch(getAcademicYears({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
                return [];
            });
    }

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t(`manage_academicYears.labels.code`),
            dataIndex: "code",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t(`manage_academicYears.labels.start_date`),
            dataIndex: "start_date",
            valueType: "date",
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t(`manage_academicYears.labels.end_date`),
            dataIndex: "end_date",
            valueType: "date",
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_academicYears.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_academicYears.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_academicYears.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_academicYears.yes")}
                        cancelText={t("manage_academicYears.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/admin-dashboard">{t("auth_sidebar.dashboard")}</Link>,
        },
        {
            title: t("manage_academicYears.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingAcademicYear(record);
        form.setFieldsValue({
            ...record,
            start_date: record.start_date ? dayjs(record.start_date) : null,
            end_date: record.end_date ? dayjs(record.end_date) : null,
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingAcademicYear(record);
        form.setFieldsValue({
            ...record,
            start_date: record.start_date ? dayjs(record.start_date) : null,
            end_date: record.end_date ? dayjs(record.end_date) : null,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingAcademicYear(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE ACADEMIC YEAR
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        
        // Format dates for API
        const formattedValues = {
            ...values,
            start_date: values.start_date ? values.start_date.format('YYYY-MM-DD') : null,
            end_date: values.end_date ? values.end_date.format('YYYY-MM-DD') : null,
        };
        
        const payload = editingAcademicYear 
            ? { id: editingAcademicYear.id, ...formattedValues } 
            : formattedValues;
            
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingAcademicYear) {
                await dispatch(updateAcademicYear(payload)).unwrap();
            } else {
                await dispatch(storeAcademicYear(formattedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_academicYears.confirmAction"),
            content: editingAcademicYear
                ? t("manage_academicYears.confirmUpdate")
                : t("manage_academicYears.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE ACADEMIC YEAR
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteAcademicYear(id));
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
     |  - HANDLE RESET
     |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_academicYears.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetAcademicYears(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_academicYears.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                open={modalVisible}
                title={
                    viewMode
                        ? t("manage_academicYears.details")
                        : editingAcademicYear
                            ? t("manage_academicYears.edit")
                            : t("manage_academicYears.add")
                }
                onCancel={handleReset}
                footer={
                    viewMode
                        ? [
                            <Button key="close" onClick={handleReset}>
                                {t("common.close")}
                            </Button>,
                        ]
                        : [
                            <Button key="cancel" onClick={handleReset}>
                                {t("common.cancel")}
                            </Button>,
                            <Button
                                key="submit"
                                type="primary"
                                loading={loading}
                                onClick={() => form.submit()}
                            >
                                {t("common.save")}
                            </Button>,
                        ]
                }
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={viewMode}
                >
                    {editingAcademicYear && (
                        <Form.Item
                            label={t("manage_academicYears.labels.id")}
                            name="id"
                        >
                            <Input disabled />
                        </Form.Item>
                    )}
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_academicYears.labels.code")}
                                name="code"
                                rules={[{ required: true, message: t("manage_academicYears.errors.codeRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_academicYears.placeholders.code")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_academicYears.labels.start_date")}
                                name="start_date"
                                rules={[{ required: true, message: t("manage_academicYears.errors.startDateRequired") }]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    placeholder={t("manage_academicYears.placeholders.start_date")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_academicYears.labels.end_date")}
                                name="end_date"
                                rules={[
                                    { required: true, message: t("manage_academicYears.errors.endDateRequired") },
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (!value || !getFieldValue('start_date') || value.isAfter(getFieldValue('start_date'))) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject(new Error(t("manage_academicYears.errors.endDateAfterStartDate")));
                                        },
                                    }),
                                ]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    placeholder={t("manage_academicYears.placeholders.end_date")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageAcademicYears;
