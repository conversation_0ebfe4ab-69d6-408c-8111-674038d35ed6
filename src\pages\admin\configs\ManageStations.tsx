import {useEffect, useRef, useState} from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {toast} from "react-toastify";
import {getGovernorateAll} from "../../../features/admin/governorateSlice.ts";
import {getDelegationsByGovernorate} from "../../../features/admin/delegationSlice.ts";
import {deleteStation, getStations, storeStation, updateStation} from "../../../features/admin/stationSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageStations() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;

    const dispatch: any = useDispatch();
    const actionRef = useRef<any>();
    const [form] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingStation, setEditingStation] = useState<any>(null);
    const [governorates, setGovernorates] = useState([]);
    const [filteredDelegations, setFilteredDelegations] = useState([]);
    const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);
    const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<number | null>(null);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_stations.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_stations.labels.latitude")}`,
            dataIndex: "latitude",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_stations.labels.longitude")}`,
            dataIndex: "longitude",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_stations.labels.governorate")}`,
            dataIndex: "id_governorate",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.governorate?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    placeholder={t("manage_stations.filters.governorate")}
                    options={governorates?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    onChange={handleGovernorateChange}
                />
            ),
        },
        {
            title: `${t("manage_stations.labels.delegation")}`,
            dataIndex: "id_delegation",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.delegation?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    loading={isDelegationsLoading}
                    placeholder={t("manage_stations.filters.delegation")}
                    options={filteredDelegations?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    disabled={!selectedFilterGovernorate}
                    className="w-full"
                    notFoundContent={
                        isDelegationsLoading ? (
                            <div className="flex items-center justify-center py-2">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                            </div>
                        ) : (
                            <div className="text-center py-2 text-gray-500">
                                {!selectedFilterGovernorate
                                    ? t("manage_stations.selectGovernorate")
                                    : t("common.noData")}
                            </div>
                        )
                    }
                />
            ),
        },
        {
            title: `${t("manage_stations.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_stations.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_stations") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_stations") && (
                            <Popconfirm
                                title={t("manage_stations.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_stations.yes")}
                                cancelText={t("manage_stations.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.transport")}</Link>,
        },
        {
            title: t("manage_stations.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    | FETCH STATIONS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetStations = (params: any, sort: any, filter: any) => {
        return dispatch(getStations({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
                toast.error(t("common.errors.unexpected"));
            });
    }

    useEffect(() => {
        const fetchData = async () => {
            try {
                const govResponse = await dispatch(getGovernorateAll()).unwrap();
                setGovernorates(govResponse?.data || []);
            } catch (error) {
                console.error('Error fetching data:', error);
                toast.error(t("common.errors.unexpected"));
            }
        };

        fetchData();
    }, [dispatch]);

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleAdd = () => {
        setEditingStation(null);
        setViewMode(false);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEdit = async (record: any) => {
        setEditingStation(record);
        
        // First set the governorate and fetch its delegations if it exists
        if (record.governorate) {
            await handleGovernorateChange(record.governorate.id);
        }
        
        // Then set all form values with null checks
        form.setFieldsValue({
            nom_fr: record.nom_fr,
            nom_en: record.nom_en,
            nom_ar: record.nom_ar,
            longitude: record.longitude,
            latitude: record.latitude,
            id_governorate: record.governorate?.id || null,
            id_delegation: record.delegation?.id || null,
        });

        setViewMode(false);
        setModalVisible(true);
    };

    const handleView = (record: any) => {
        setEditingStation(record);
          
        form.setFieldsValue({
            ...record,
            id_governorate: record.id_governorate,
            id_delegation: record.id_delegation,
        });
        handleGovernorateChange(record.delegation?.id_governorate);
        setViewMode(true);
        setModalVisible(true);
    };

    const handleGovernorateChange = async (governorateId: number | null) => {
        if (!governorateId) {
            setFilteredDelegations([]);
            setIsDelegationsLoading(false);
            form.setFieldValue('id_delegation', null);
            return;
        }

        setSelectedFilterGovernorate(governorateId);
        setIsDelegationsLoading(true);

        try {
            const response: any = await dispatch(getDelegationsByGovernorate(governorateId)).unwrap();
            setFilteredDelegations(response.data || []);
        } catch (error) {
            console.error('Error fetching delegations:', error);
            toast.error(t("common.errors.unexpected"));
            setFilteredDelegations([]);
        } finally {
            setIsDelegationsLoading(false);
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE FORM SUBMIT
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        const payload = editingStation ? { id: editingStation.id, ...values } : values;

        try {
            if (editingStation) {
                await dispatch(updateStation(payload)).unwrap();
            } else {
                await dispatch(storeStation(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };

    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_stations.confirmAction"),
            content: editingStation
                ? t("manage_stations.confirmUpdate")
                : t("manage_stations.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE DELETE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteStation(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setViewMode(false);
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_stations.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetStations(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{ x: 800 }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_stations.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_stations.view")
                        : editingStation
                            ? t("manage_stations.edit")
                            : t("manage_stations.add")
                }
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={
                    viewMode ? [
                        <Button key="close" onClick={() => setModalVisible(false)}>
                            {t("common.close")}
                        </Button>
                    ] : [
                        <Button key="cancel" onClick={() => setModalVisible(false)}>
                            {t("common.cancel")}
                        </Button>,
                        <Button key="submit" type="primary" onClick={form.submit}>
                            {t("common.save")}
                        </Button>
                    ]
                }
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_governorate"
                                label={t("manage_stations.labels.governorate")}
                                rules={[{ required: true, message: t("manage_stations.errors.governorateRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_stations.placeholders.governorate")}
                                    onChange={handleGovernorateChange}
                                    options={governorates.map((gov: any) => ({
                                        label: gov[`nom_${currentLang}`],
                                        value: gov.id,
                                    }))}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_delegation"
                                label={t("manage_stations.labels.delegation")}
                                rules={[{ required: true, message: t("manage_stations.errors.delegationRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_stations.placeholders.delegation")}
                                    options={filteredDelegations.map((del: any) => ({
                                        label: del[`nom_${currentLang}`],
                                        value: del.id,
                                    }))}
                                    notFoundContent={
                                        isDelegationsLoading ? (
                                            <div className="flex items-center justify-center py-2">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                            </div>
                                        ) : (
                                            <div className="text-center py-2 text-gray-500">
                                                {!selectedFilterGovernorate
                                                    ? t("manage_stations.selectGovernorate")
                                                    : t("common.noData")}
                                            </div>
                                        )
                                    }
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="latitude"
                                label={t("manage_stations.labels.latitude")}
                                rules={[{ required: true, message: t("manage_stations.errors.latitudeRequired") }]}
                            >
                                <Input disabled={viewMode} placeholder={t("manage_stations.placeholders.latitude")} type="number" step="0.000001" />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="longitude"
                                label={t("manage_stations.labels.longitude")}
                                rules={[{ required: true, message: t("manage_stations.errors.longitudeRequired") }]}
                            >
                                <Input disabled={viewMode} placeholder={t("manage_stations.placeholders.longitude")} type="number" step="0.000001" />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageStations;
