import { Lock, AlertTriangle, Calendar, Info } from "lucide-react";
import { useTranslation } from "react-i18next";
import "./SellsPeriodClosedInfo.css";

interface SellsPeriodClosedInfoProps {
  nextOpeningDate?: string;
  message?: string;
  isOpen?: boolean;
}

const SellsPeriodClosedInfo: React.FC<SellsPeriodClosedInfoProps> = ({
  nextOpeningDate,
  message,
  isOpen = false
}) => {
    const { t } = useTranslation();

    return (
        <div className={`sells-period-closed-container ${isOpen ? 'sells-period-closed-container-open' : ''}`}>
            {/* Left accent border with animation */}
            <div className="left-accent-border" />

            <div className="content-wrapper">
                {/* Icon container with animation */}
                <div className="icon-container">
                    <Lock size={24} className="lock-icon" />
                </div>

                <div className="text-content">
                    <div className="header-row">
                        <AlertTriangle size={18} className="alert-icon" />
                        <h3 className="title">
                            {t("components.sellsPeriodClosed.noOpenPeriod")}
                        </h3>
                    </div>

                    <p className="description">
                        {message || t("components.sellsPeriodClosed.description")}
                    </p>

                    {nextOpeningDate && (
                        <div className="next-opening-date">
                            <Calendar size={16} className="calendar-icon" />
                            <span>
                                {t("components.sellsPeriodClosed.nextOpening")}: {nextOpeningDate}
                            </span>
                        </div>
                    )}

                    <div className="info-note">
                        <Info size={14} />
                        <span>{t("components.sellsPeriodClosed.contactAdmin")}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SellsPeriodClosedInfo;