import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    DatePicker,
    InputNumber
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import moment from "moment";
import {
    getSeasons,
    storeSeason,
    updateSeason,
    deleteSeason
} from "../../../features/admin/seasonSlice";
import { hasPermission } from "../../../helpers/permissions";

function ManageSeasons() {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const dispatch: any = useDispatch();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingSeason, setEditingSeason] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    {/*|--------------------------------------------------------------------------
    | FETCH ALL GOVERNORATES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */}
    const handleGetSeasons = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getSeasons({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    /*|--------------------------------------------------------------------------
    |  - VIEWS
     |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t(`manage_seasons.labels.name`),
            dataIndex: `nom_${currentLang}`,
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: t("manage_seasons.labels.period"),
            responsive: ["xs", "sm", "md", "lg"],
            sorter: true,
            render: (_: any, record: any) => (
                `${moment(record.start_date).format("DD/MM/YYYY")} - ${moment(record.end_date).format("DD/MM/YYYY")}`
            ),
            search: false,
        },
        {
            title: t("manage_seasons.labels.priority"),
            dataIndex: "priority",
            sorter: true,
            search: false,
        },
        {
            title: t("manage_seasons.labels.actions"),
            fixed: "right",
            width: 170,
            search: false,
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                   {
                        hasPermission("edit_seasons") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                   }
                    {/*<Popconfirm
                        title={t("manage_seasons.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("common.yes")}
                        cancelText={t("common.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>*/}
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.transport")}</Link>,
        },
        {
            title: t("manage_seasons.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingSeason(record);
        form.setFieldsValue({
            ...record,
            start_date: moment(record.start_date),
            end_date: moment(record.end_date),
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingSeason(record);
        form.setFieldsValue({
            ...record,
            start_date: moment(record.start_date),
            end_date: moment(record.end_date),
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingSeason(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE SEASON
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = {
            ...values,
            start_date: values.start_date.format('YYYY-MM-DD'),
            end_date: values.end_date.format('YYYY-MM-DD'),
        };
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        try {
            if (editingSeason) {
                await dispatch(updateSeason({ id: editingSeason.id, ...payload })).unwrap();
            } else {
                await dispatch(storeSeason(payload)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_seasons.confirmAction"),
            content: editingSeason
                ? t("manage_seasons.confirmUpdate")
                : t("manage_seasons.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };
    /*|--------------------------------------------------------------------------
    |  - DELETE SEASON
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteSeason(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        form.resetFields();
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <ProTable
                headerTitle={t("manage_seasons.title")}
                columns={columns}
                actionRef={actionRef}
                request={async (params:any, sort:any, filter:any) => {
                    setLoading(true)
                    const dataFilter:any = await handleGetSeasons(params,sort,filter);
                    setLoading(false)
                    return {
                        data: dataFilter,
                        success: true,
                    };
                }}    
                rowKey={"id"}
                sortDirections={["ascend", "descend"]}
                pagination={{
                    pageSize: pageSize,
                    total: total,
                    showSizeChanger: true,
                    onChange: (page) => setPageNumber(page),
                    onShowSizeChange: (_, pageSize) => {
                        setPageSize(pageSize);
                    },
                }}
                search={{
                    labelWidth: "auto",
                }}
                loading={loading}
                /*toolBarRender={() => [
                    <Button key="button" onClick={handleAdd} className="btn-add">
                        {t("manage_seasons.add")}
                    </Button>,
                ]}*/
            />

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_seasons.details")
                        : editingSeason
                            ? t("manage_seasons.edit")
                            : t("manage_seasons.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_seasons.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={viewMode}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_seasons.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_seasons.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_seasons.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_seasons.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_seasons.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_seasons.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_seasons.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_seasons.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_seasons.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_seasons.labels.start_date")}
                                name="start_date"
                                rules={[{ required: true, message: t("manage_seasons.errors.startDateRequired") }]}
                            >
                                <DatePicker style={{ width: '100%' }} placeholder={t("manage_seasons.placeholders.start_date")} />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_seasons.labels.end_date")}
                                name="end_date"
                                rules={[{ required: true, message: t("manage_seasons.errors.endDateRequired") }]}
                            >
                                <DatePicker style={{ width: '100%' }} placeholder={t("manage_seasons.placeholders.end_date")} />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_seasons.labels.priority")}
                                name="priority"
                                rules={[{ required: true, message: t("manage_seasons.errors.priorityRequired") }]}
                            >
                                <InputNumber disabled={true} min={1} style={{ width: '100%' }} placeholder={t("manage_seasons.placeholders.priority")} />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageSeasons;