import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/delegations';

interface DelegationState {
    items: any[];
    paginatedItems: any | null;
    loading: boolean;
    error: string | null;
    currentItem: any | null;
}

const initialState: DelegationState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getDelegationAll: any = createAsyncThunk(
    "getDelegationAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getDelegations: any = createAsyncThunk(
    "getDelegations",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any,
            sort: any,
            filter: any
        },
        thunkAPI:any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar, governorate } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (governorate) {
                searchParams.push(`id_governorate:${governorate}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;

            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeDelegation: any = createAsyncThunk(
    "storeDelegation",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateDelegation: any = createAsyncThunk(
    "updateDelegation",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteDelegation: any = createAsyncThunk(
    "deleteDelegation",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getDelegationsByGovernorate: any = createAsyncThunk(
    "getDelegationsByGovernorate",
    async (governorateId: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/governorate/${governorateId}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const delegationSlice = createSlice({
    name: 'delegation',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getDelegationAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getDelegationAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getDelegationAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || "An error occurred";
            })
            .addCase(getDelegations.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getDelegations.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getDelegations.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || "An error occurred";
            })
            .addCase(storeDelegation.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeDelegation.fulfilled, (state, action) => {
                state.loading = false;
                state.items.push(action.payload);
            })
            .addCase(storeDelegation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || "An error occurred";
            })
            .addCase(updateDelegation.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateDelegation.fulfilled, (state, action) => {
                state.loading = false;
                const index = state.items.findIndex(item => item.id === action.payload.id);
                if (index !== -1) {
                    state.items[index] = action.payload;
                }
            })
            .addCase(updateDelegation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || "An error occurred";
            })
            .addCase(deleteDelegation.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteDelegation.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deleteDelegation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || "An error occurred";
            })
            .addCase(getDelegationsByGovernorate.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getDelegationsByGovernorate.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getDelegationsByGovernorate.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = delegationSlice.actions;
export default delegationSlice.reducer;