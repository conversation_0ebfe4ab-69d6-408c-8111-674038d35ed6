import { useState, useEffect } from "react";
import { Modal, Form, Input, Row, Col, Select, Switch, Spin } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
  storeSocialAffair,
  updateSocialAffair,
} from "../../features/admin/socialAffairSlice.ts";
import { getAcademicYearsAll } from "../../features/admin/academicYearSlice.ts";

type StudentType = 'eleve' | 'etudiant';

interface SocialAffairFormProps {
  modalVisible: boolean;
  onCancel: () => void;
  editingSocialAffair?: any;
  governorateId: number;
  onSuccess: () => void;
}

function SocialAffairForm({
  modalVisible,
  onCancel,
  editingSocialAffair,
  governorateId,
  onSuccess,
}: SocialAffairFormProps) {
  const { t } = useTranslation();
  const dispatch: any = useDispatch();
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [academicYears, setAcademicYears] = useState<any[]>([]);
  const [loadingAcademicYears, setLoadingAcademicYears] = useState(false);

  // Fetch academic years
  useEffect(() => {
    const fetchAcademicYears = async () => {
      setLoadingAcademicYears(true);
      try {
        const result = await dispatch(getAcademicYearsAll()).unwrap();
        // Ensure we're setting the data array from the response
        setAcademicYears(result.data || []);
      } catch (error) {
        console.error("Failed to fetch academic years:", error);
        setAcademicYears([]); // Set empty array on error
      } finally {
        setLoadingAcademicYears(false);
      }
    };

    fetchAcademicYears();
  }, [dispatch]);


  useEffect(() => {
    if (editingSocialAffair) {
      form.setFieldsValue({
        ...editingSocialAffair,
      });
    } else {
      form.setFieldsValue({
        governorate_id: governorateId,
        eleve_etudiant: 'eleve',
      });
    }
  }, [editingSocialAffair, form, governorateId]);

  /*|--------------------------------------------------------------------------
    |  - HANDLE FORM SUBMIT
    |-------------------------------------------------------------------------- */
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    const payload = editingSocialAffair
      ? { id: editingSocialAffair.id, ...values }
      : { ...values, governorate_id: governorateId };

    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });

    try {
      if (editingSocialAffair) {
        await dispatch(updateSocialAffair(payload)).unwrap();
      } else {
        await dispatch(storeSocialAffair(payload)).unwrap();
      }

      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });

      onSuccess();
      onCancel();
    } catch (error: any) {
      const fieldErrors = Object.keys(error.errors || {}).map((field) => ({
        name: field,
        errors: [error.errors[field][0]],
      }));

      form.setFields(fieldErrors);

      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      width={900}
      title={
        editingSocialAffair ? t("social_affairs.edit") : t("social_affairs.add")
      }
      open={modalVisible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okText={t("social_affairs.save")}
      confirmLoading={loading}
    >
      <Form
        className="form-inputs"
        form={form}
        layout="vertical"
        onFinish={handleFormSubmit}
        disabled={loading}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.delegation")}
              name="delegation"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.delegationRequired"),
                },
              ]}
            >
              <Input
                placeholder={t("social_affairs.placeholders.delegation")}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.nom_parent")}
              name="nom_parent"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.nomParentRequired"),
                },
              ]}
            >
              <Input
                placeholder={t("social_affairs.placeholders.nom_parent")}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.cin_parent")}
              name="cin_parent"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.cinParentRequired"),
                },
                {
                  pattern: /^\d{8}$/,
                  message: t("social_affairs.errors.cinParentInvalid"),
                },
              ]}
            >
              <Input
                placeholder={t("social_affairs.placeholders.cin_parent")}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.telephone")}
              name="telephone"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.telephoneRequired"),
                },
                {
                  pattern: /^\d{8}$/,
                  message: t("social_affairs.errors.telephoneInvalid"),
                },
              ]}
            >
              <Input placeholder={t("social_affairs.placeholders.telephone")} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.nom_complet")}
              name="nom_complet"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.nomCompletRequired"),
                },
              ]}
            >
              <Input
                placeholder={t("social_affairs.placeholders.nom_complet")}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.niveau_etude")}
              name="niveau_etude"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.niveauEtudeRequired"),
                },
              ]}
            >
              <Input
                placeholder={t("social_affairs.placeholders.niveau_etude")}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.trajet_requise")}
              name="trajet_requise"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.trajetRequiseRequired"),
                },
              ]}
            >
              <Input
                placeholder={t("social_affairs.placeholders.trajet_requise")}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.academic_year")}
              name="academic_year_id"
            >
              <Select
                placeholder={t("social_affairs.placeholders.academic_year")}
                loading={loadingAcademicYears}
                allowClear
              >
                {academicYears.map((year) => (
                  <Select.Option key={year.id} value={year.id}>
                    {year.code}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.eleve_etudiant")}
              name="eleve_etudiant"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.eleveEtudiantRequired"),
                },
              ]}
            >
              <Select placeholder={t("social_affairs.placeholders.eleve_etudiant")}>
                <Select.Option value="eleve">{t("social_affairs.types.eleve")}</Select.Option>
                <Select.Option value="etudiant">{t("social_affairs.types.etudiant")}</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label={t("social_affairs.labels.societe")}
              name="societe"
              rules={[
                {
                  required: true,
                  message: t("social_affairs.errors.societeRequired"),
                },
              ]}
            >
               <Input
                placeholder={t("social_affairs.placeholders.societe")}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="governorate_id" hidden>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default SocialAffairForm;
