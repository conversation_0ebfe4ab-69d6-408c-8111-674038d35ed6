import { Form, Input, But<PERSON>, Row, Col, Avatar, Collapse } from "antd";
import { UserOutlined, MailOutlined, PhoneOutlined, IdcardOutlined, EnvironmentOutlined, LockOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { AppDispatch, RootState } from "../../../features/store.ts";
import { updateUserInfos } from "../../../features/auth/userSlice.ts";
import { Loading } from "../../../components";
import {assets} from "../../../assets/assets.ts";

const { Panel } = Collapse;

const ManageProfile = () => {
    const dispatch = useDispatch<AppDispatch>();
    const { user }:any = useSelector((state: RootState) => state.auth);
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [activeKey, setActiveKey] = useState<string | string[]>(['1']); // Suivi de la clé active
   
    const onFinish = async (values: any) => {
        setLoading(true);
        try {
            await dispatch(updateUserInfos(values)).unwrap();
            toast.success("Profil mis à jour avec succès !", { position: "top-center" });
        } catch (error) {
            toast.error("Échec de la mise à jour du profil", { position: "top-center" });
        } finally {
            setLoading(false);
        }
    };

    const onChangePassword = async (values: any) => {
        console.log(values)
        toast.success("Mot de passe mis à jour avec succès !", { position: "top-center" });
    };

    if (!user) {
        return <Loading />;
    }


    const handleChange = (keys: string | string[]) => {
        const newKeys = Array.isArray(keys) ? keys : [keys];
        if (newKeys.length > 0) {
            setActiveKey(newKeys[0]);
        }
    };

    return (
        <div className="h-[80vh] flex items-center justify-center">
            <div className="w-full max-w-4xl px-4">
                <div className="px-4 rounded-2xl backdrop-blur-sm border-0">
                    <div
                        className="relative shadow p-6 bg-white rounded mb-3"
                        style={{
                            backgroundImage: `url(${assets.bg})`,
                            backgroundPosition: "center",
                            backgroundSize: "auto",
                            backgroundRepeat: "no-repeat",
                        }}
                    >
                        <div className="relative z-10 flex justify-between items-center">
                            <div className="flex items-center space-x-4">
                                <div className="relative group">
                                    <Avatar
                                        src={`https://ui-avatars.com/api/?name=${user?.firstname}+${user?.lastname}&background=333333&color=fff&bold=true`}
                                        size={64}
                                        className="relative transform transition duration-300 hover:scale-50 border-2 border-white"
                                        style={{
                                            borderRadius: "12px",
                                            clipPath: "polygon(0 0, 100% 0, 100% 80%, 95% 100%, 0 100%)",
                                        }}
                                    />
                                </div>

                                <div className="relative">
                                <h2 className="text-3xl font-extrabold">
                                        Bonjour,{" "}
                                        <span className="inline-block">
                                    {user?.lastname}!
                                </span>
                                    </h2>
                                    <p className="text-sm text-gray-900/50 mt-1 flex items-center">
                                        <span
                                            className="mr-2">Gérez vos informations personnelles en toute sécurité</span>
                                        <svg
                                            style={{color: "var(--primary-color)"}}
                                            className="animate-bounce-slow w-4 h-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                d="M11 14.414V3a1 1 0 10-2 0v11.414l-3.293-3.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0l5-5a1 1 0 00-1.414-1.414L11 14.414z"/>
                                        </svg>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <Collapse
                        activeKey={activeKey}
                        onChange={handleChange}
                        accordion
                    >
                        <Panel header="Profil" key="1" className="rounded-xl">
                            <Form
                                form={form}
                                layout="vertical"
                                initialValues={user}
                                onFinish={onFinish}
                                className="space-y-4"
                            >
                                <Row gutter={[24, 8]}>
                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="firstname"
                                            rules={[{required: true, message: "Le prénom est requis"}]}>
                                            <Input
                                                prefix={<UserOutlined className="text-gray-400"/>}
                                                placeholder="Prénom"
                                                size="large"
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="lastname"
                                            rules={[{required: true, message: "Le nom de famille est requis"}]}>
                                            <Input
                                                prefix={<UserOutlined className="text-gray-400"/>}
                                                placeholder="Nom de famille"
                                                size="large"
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="email"
                                            rules={[{required: true}, {type: "email", message: "Email invalide"}]}>
                                            <Input
                                                prefix={<MailOutlined className="text-gray-400"/>}
                                                placeholder="Adresse email"
                                                size="large"
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="phone"
                                            rules={[{
                                                required: true,
                                                message: "Le numéro de téléphone est requis"
                                            }, {
                                                pattern: /^\d{8}$/,
                                                message: "Le numéro de téléphone doit être composé de 8 chiffres"
                                            }]}>
                                            <Input
                                                prefix={<PhoneOutlined className="text-gray-400"/>}
                                                placeholder="Téléphone"
                                                size="large"
                                                maxLength={8}
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="cin"
                                            rules={[{
                                                required: true,
                                                message: "Le numéro CIN est requis"
                                            }, {
                                                pattern: /^\d{8}$/,
                                                message: "Le CIN doit être composé de 8 chiffres"
                                            }]}>
                                            <Input
                                                prefix={<IdcardOutlined className="text-gray-400"/>}
                                                placeholder="Numéro CIN"
                                                size="large"
                                                maxLength={8}
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="address"
                                            rules={[{required: true, message: "L'adresse complète est requise"}]}>
                                            <Input
                                                prefix={<EnvironmentOutlined className="text-gray-400"/>}
                                                placeholder="Adresse complète"
                                                size="large"
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>

                                <Form.Item className="text-center mt-8">
                                    <Button
                                        className="btn-delete"
                                        htmlType="submit"
                                        loading={loading}
                                    >
                                        Mettre à jour le profil
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Panel>

                        <Panel header="Sécurité" key="2" className="rounded-xl">
                            <Form
                                layout="vertical"
                                onFinish={onChangePassword}
                                className="space-y-2"
                            >
                                <Form.Item
                                    name="oldPassword"
                                    rules={[{required: true, message: "Le mot de passe actuel est requis"}]}>
                                    <Input.Password
                                        prefix={<LockOutlined className="text-gray-400"/>}
                                        placeholder="Mot de passe actuel"
                                        size="large"
                                        className="rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="newPassword"
                                    rules={[{required: true, message: "Le nouveau mot de passe est requis"}]}>
                                    <Input.Password
                                        prefix={<LockOutlined className="text-gray-400"/>}
                                        placeholder="Nouveau mot de passe"
                                        size="large"
                                        className="rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="confirmPassword"
                                    dependencies={['newPassword']}
                                    rules={[{
                                        required: true,
                                        message: "La confirmation du mot de passe est requise"
                                    }, ({getFieldValue}) => ({
                                        validator(_, value) {
                                            if (!value || getFieldValue('newPassword') === value) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject('Les mots de passe ne correspondent pas');
                                        },
                                    })]}>
                                    <Input.Password
                                        prefix={<LockOutlined className="text-gray-400"/>}
                                        placeholder="Confirmer le mot de passe"
                                        size="large"
                                        className="rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item className="text-center !mt-8">
                                    <Button
                                        htmlType="submit"
                                        loading={loading}
                                        className="btn-delete"
                                    >
                                        Modifier le mot de passe
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Panel>
                    </Collapse>
                </div>
            </div>
        </div>
    );
};

export default ManageProfile;
