import {AlertCircle, ArrowDown} from "lucide-react";
import {Empty, Popover, Spin, Steps, Tooltip, Typography} from "antd";
import {EnvironmentOutlined, LoadingOutlined} from "@ant-design/icons";
import {checkForMissedTrips} from "../../tools/helpers.ts";
import {useState} from "react";
import {useTranslation} from "react-i18next";

const LineDisplay: any = ({ record, isReversed, selectedStationDepart = "", selectedStationArrival = "", loading = false }: any) => {

    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    if (!record) {
        return (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
                <Empty
                    description={
                        <Typography.Text type="secondary" className="text-sm">
                            No line data available
                        </Typography.Text>
                    }
                    className="py-8"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </div>
        );
    }

    const commercialSpeed = record.commercial_speed || 60;
    let stations = record.stations || [];
    let routes = record.routes || [];

    const [selectedStationIndex, setSelectedStationIndex] = useState<number | null>(null);

    if (isReversed) {
        stations = [...stations].reverse();
        routes = [...routes].reverse().map((route) => ({
            ...route,
            station_depart: route.station_arrival,
            station_arrival: route.station_depart,
        }));
    }

    const accumulatedDistances = routes.reduce((acc: number[], route: any, index: number) => {
        const accumulated = acc[index] || 0;
        return [...acc, accumulated + parseFloat(route.number_of_km)];
    }, [0]);

    const calculateArrivalTime = (departureTime: string, distance: number) => {
        const [hours, minutes] = departureTime.split(":").map(Number);
        const departureDate = new Date();
        departureDate.setHours(hours, minutes, 0);

        const travelTimeMinutes = (distance / commercialSpeed) * 60;
        departureDate.setMinutes(departureDate.getMinutes() + travelTimeMinutes);

        const adjustedHours = departureDate.getHours();
        const adjustedMinutes = departureDate.getMinutes();

        return `${String(adjustedHours).padStart(2, "0")}:${String(adjustedMinutes).padStart(2, "0")}`;
    };

    const missedTrips = checkForMissedTrips(stations, routes);
    const hideTimeAndDistance = missedTrips.length > 0;

    const getTimeColor = (fromStationId: number) => {
        const colors = ['blue', 'purple', 'orange', 'green', 'teal'];
        const stationIndex = stations.findIndex((s: any) => s.id === fromStationId);
        return colors[stationIndex % colors.length];
    };

    const calculateStationTimes = () => {
        const allStationTimes: any[] = Array(stations.length).fill(null).map(() => []);

        try {
            stations.forEach((station: any, stationIndex: number) => {
                if (!station.has_departures || !Array.isArray(station.departure_configs)) {
                    return;
                }

                station.departure_configs.forEach((config: any) => {
                    // Filtrer par direction
                    if (isReversed) {
                        if (config.direction !== 2) return;
                    } else {
                        if (config.direction !== 1) return;
                    }

                    const option = config.option_details;
                    if (!option || !Array.isArray(option.seasons)) return;

                    const advancementSteps = config.advancement_position || 0;
                    const departureTime = config.time;

                    option.seasons.forEach((season: any) => {
                        const seasonId = season.id;
                        const seasonName = season[`nom_${currentLang}`] || 'Unknown Season';

                        // Ajouter à la station courante
                        let seasonGroup = allStationTimes[stationIndex].find((g: any) => g.id_season === seasonId);
                        if (!seasonGroup) {
                            seasonGroup = { id_season: seasonId, seasonName, times: [] };
                            allStationTimes[stationIndex].push(seasonGroup);
                        }
                        seasonGroup.times.push({ time: departureTime, fromStationId: station.id });

                        // Calculer pour les stations suivantes
                        for (let step = 1; step <= advancementSteps; step++) {
                            const targetIndex = stationIndex + step;
                            if (targetIndex >= stations.length) break;

                            const distance = accumulatedDistances[targetIndex] - accumulatedDistances[stationIndex];
                            const arrivalTime = calculateArrivalTime(departureTime, distance);

                            let targetSeasonGroup = allStationTimes[targetIndex].find((g: any) => g.id_season === seasonId);
                            if (!targetSeasonGroup) {
                                targetSeasonGroup = { id_season: seasonId, seasonName, times: [] };
                                allStationTimes[targetIndex].push(targetSeasonGroup);
                            }
                            targetSeasonGroup.times.push({ time: arrivalTime, fromStationId: station.id });
                        }
                    });
                });
            });

            return allStationTimes;
        } catch (error) {
            console.error('Error calculating station times:', error);
            return [];
        }
    };

    const renderSteps = () => {
        try {
            const allStationTimes = calculateStationTimes();

            if (stations.length === 0) {
                return (
                    <Empty
                        description={
                            <Typography.Text type="secondary" className="text-sm">
                                {t("manage_lines.noStations")}
                            </Typography.Text>
                        }
                        className="py-8"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                );
            }

            return (
                <Steps
                    progressDot={(dot) => (
                        <div className="relative">
                            {dot}
                            <div className="absolute top-1 left-1 h-2 w-0.5 bg-red-400 transform -translate-y-1/2" />
                        </div>
                    )}
                    current={stations.length}
                    className="py-2"
                >
                    {stations.map((station: any, index: number) => {
                        const isLast = index === stations.length - 1;
                        const missedTripForSegment = !isLast
                            ? missedTrips.find(
                                (trip: any) =>
                                    trip.from === station[`nom_${currentLang}`] &&
                                    trip.to === stations[index + 1]?.[`nom_${currentLang}`]
                            )
                            : null;
                        const isMissedTrip = !!missedTripForSegment;

                        const stationTimes = allStationTimes[index];
                        const accumulated = hideTimeAndDistance ? "" : accumulatedDistances[index];

                        const timesPopoverContent = (
                            <div className="max-w-[220px]">
                                {stationTimes && stationTimes.length > 0 && stationTimes.map((season: any, sIndex: number) => (
                                    <div key={sIndex} className="mb-3 last:mb-0">
                                        <div className="font-medium text-gray-700 border-b border-gray-100 pb-1 mb-2">{season.seasonName}</div>
                                        <div className="flex flex-wrap gap-1">
                                            {season.times.map((timeObj: any, timeIndex: number) => {
                                                const color = getTimeColor(timeObj.fromStationId);
                                                return (
                                                    <span
                                                        key={timeIndex}
                                                        className={`inline-block px-2 py-0.5 rounded text-xs font-mono border bg-${color}-50 text-${color}-700 border-${color}-200`}
                                                    >
                                                        {timeObj.time}
                                                    </span>
                                                );
                                            })}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        );

                        return (
                            <Steps.Step
                                key={index}
                                title={
                                    <div className="flex flex-col items-center group cursor-pointer relative">
                                        <Popover
                                            content={timesPopoverContent}
                                            title={
                                                <div className="flex items-center gap-2 text-gray-700">
                                                    <EnvironmentOutlined className="text-red-500" />
                                                    <span className="font-medium">{`${t("common.schedules")} - ${station[`nom_${currentLang}`]}`}</span>
                                                </div>
                                            }
                                            trigger="click"
                                            open={selectedStationIndex === index}
                                            onOpenChange={(visible) => {
                                                setSelectedStationIndex(visible ? index : null);
                                            }}
                                        >
                                            <div className="relative">
                                                <EnvironmentOutlined
                                                    style={{
                                                        color: isMissedTrip ? "#ff4d4f" : "var(--primary-color)",
                                                    }}
                                                    className="text-2xl transform transition-all duration-300 group-hover:scale-125 group-hover:text-red-700"
                                                />
                                                {isMissedTrip && (
                                                    <Tooltip
                                                        title={`Trajet manqué entre ${station[`nom_${currentLang}`]} et ${stations[index + 1][`nom_${currentLang}`]}`}
                                                    >
                                                        <AlertCircle
                                                            className="absolute -top-10 right-0.5 text-red-500 animate-pulse"
                                                            size={16}
                                                        />
                                                    </Tooltip>
                                                )}
                                            </div>
                                            <span
                                                className={`
                                                    text-sm font-medium mt-1 px-3 py-1.5
                                                    rounded-md transition-all duration-200
                                                    shadow-sm
                                                    flex items-center gap-2
                                                    ${station.id === selectedStationDepart || station.id === selectedStationArrival
                                                        ? "bg-red-600 text-white"
                                                        : isMissedTrip
                                                            ? "bg-red-50 text-red-700 border border-red-100"
                                                            : "bg-white text-gray-700 border border-gray-100"
                                                }
                                                ${station.id === selectedStationDepart ? "animate-pulse" : ""}
                                            `}
                                            >
                                                {station.id === selectedStationDepart && (
                                                    <span className="w-1.5 h-1.5 rounded-full bg-white"/>
                                                )}
                                                {station[`nom_${currentLang}`] ? (
                                                    <span className="max-w-[110px] overflow-hidden text-ellipsis whitespace-nowrap inline-block">
                                                        {station[`nom_${currentLang}`]}
                                                    </span>
                                                ) : (
                                                    <span className="italic flex items-center gap-1">
                                                        <AlertCircle size={12} />
                                                        {t("common.noName")}
                                                    </span>
                                                )}
                                                {station.id === selectedStationArrival && (
                                                    <span className="w-1.5 h-1.5 rounded-full bg-white"/>
                                                )}
                                            </span>
                                        </Popover>
                                        {!hideTimeAndDistance && (
                                            <span className="text-xs text-gray-500 mt-1 bg-gray-50 px-2 py-0.5 rounded border border-gray-100">
                                                {`${accumulated} ${t("common.km")}`}
                                            </span>
                                        )}
                                    </div>
                                }
                                description={
                                    !isLast &&
                                    !hideTimeAndDistance && (
                                        <div className="text-center flex flex-col items-center justify-center mt-1">
                                            <div className="h-6 w-px bg-red-200"></div>
                                            <div className="flex items-center gap-1 mt-1">
                                                <ArrowDown className="text-red-500 w-3" />
                                                <div className="text-xs text-red-500 font-medium">
                                                    {`+${routes[index]?.number_of_km} km`}
                                                </div>
                                            </div>
                                        </div>
                                    )
                                }
                            />
                        );
                    })}
                </Steps>
            );
        } catch (error) {
            return (
                <Empty
                    description={
                        <Typography.Text type="secondary" className="text-sm">
                            {t("common.errors.unexpected")}
                        </Typography.Text>
                    }
                    className="py-8"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            );
        }
    };

    return (
        <Spin
            spinning={loading}
            indicator={
                <LoadingOutlined
                    style={{
                        fontSize: 24,
                        color: 'var(--primary-color)'
                    }}
                    spin
                />
            }
        >
            <div className="min-h-[200px] bg-white rounded-lg overflow-hidden">
                <div className="mb-4 text-center py-3 bg-gray-50 border-b border-gray-100">
                    <Typography.Title level={5} className="m-0 text-red-600 font-medium">
                        {
                            loading
                                ? t("common.line")
                                : (record[`nom_${currentLang}`] 
                                    ? record[`nom_${currentLang}`]
                                    : t("common.noName"))
                        }
                    </Typography.Title>
                    {record.code_line && (
                        <div className="mt-1 inline-block px-3 py-0.5 bg-white rounded-full text-gray-500 text-xs border border-gray-200">
                            {record.code_line}
                        </div>
                    )}
                </div>

                {stations.length > 0 ? (
                    <div className="scrollable-steps-container px-2">
                        {renderSteps()}
                    </div>
                ) : (
                    <Empty
                        description={
                            <Typography.Text type="secondary" className="text-sm">
                                {t("manage_lines.noStations")}
                            </Typography.Text>
                        }
                        className="py-8"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                )}
            </div>
        </Spin>
    );
};

export default LineDisplay;