import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/stock-cards";

const initialState = {
  items: [],
  paginatedItems: null,
  loading: false,
  error: null,
  currentItem: null,
};

export const getStockCardsAll: any = createAsyncThunk(
  "getStockCardsAll",
  async (_: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getStockCards: any = createAsyncThunk(
  "getStockCards",
  async (
    data: {
      pageNumber: number;
      perPage: number;
      params: any;
      sort: any;
      filter: any;
    },
    thunkAPI: any
  ) => {
    try {
      const {
        id_card_type,
        id_agent,
        sequence_start,
        sequence_end,
        mouvement,
      } = data.params;
      const sort = data.sort;
      let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
      const searchParams = [];
      if (id_card_type) {
        searchParams.push(`id_card_type:${id_card_type}`);
      }
      if (id_agent) {
        searchParams.push(`id_agent:${id_agent}`);
      }
      if (mouvement) {
        searchParams.push(`mouvement:${mouvement}`);
      }
      if (sequence_start) {
        searchParams.push(`sequence_start:${sequence_start}`);
      }
      if (sequence_end) {
        searchParams.push(`sequence_end:${sequence_end}`);
      }
      if (searchParams.length > 0) {
        url += `&search=${searchParams.join(";")}`;
      }
      const orderBy = [];
      const sortedBy = [];
      for (const [field, order] of Object.entries(sort)) {
        orderBy.push(field);
        sortedBy.push(order === "ascend" ? "asc" : "desc");
      }
      if (orderBy.length > 0) {
        url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
      }
      const joint = "&searchJoin=and";

      url += joint;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storeStockCard: any = createAsyncThunk(
  "storeStockCard",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const resp: any = await api.post(url, data);
      return resp.data;
    } catch (error: any) {
        console.log(error);
        if (error.response) {
          const { status, data } = error.response;
          if (status === 403) {
            window.location.href = "/unauthorized";
          }
          return thunkAPI.rejectWithValue(data);
        } else {
          return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
      }
  }
);

export const updateStockCard: any = createAsyncThunk(
  "updateStockCard",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, ...payload } = data;
      const url: string = `${URL}/${id}`;
      const resp: any = await api.put(url, payload);
      return resp.data;
    } catch (error: any) {
        console.log(error);
        if (error.response) {
          const { status, data } = error.response;
          if (status === 403) {
            window.location.href = "/unauthorized";
          }
          return thunkAPI.rejectWithValue(data);
        } else {
          return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
      }
  }
);

export const deleteStockCard: any = createAsyncThunk(
  "deleteStockCard",
  async (id: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}`;
      const resp: any = await api.delete(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getLatestStockCard: any = createAsyncThunk(
  "getLatestStockCard",
  async (id: number, thunkAPI: any) => {
    try {
      const url: string = `${URL}/latest/${id}`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);
export const getIntervalleSequence: any = createAsyncThunk(
  "getIntervalleSequence",
  async ({id, debSeq}: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/between/${id}/${debSeq}`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);
const stockSlice = createSlice({
  name: "stockCard",
  initialState,
  reducers: {
    setCurrentItem: (state, action) => {
      state.currentItem = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getStockCardsAll.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStockCardsAll.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
      })
      .addCase(getStockCardsAll.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
      });
  },
});

export const { setCurrentItem, clearError } = stockSlice.actions;
export default stockSlice.reducer;
