import { useEffect, useState, useRef } from 'react';

const TAB_KEY = 'app_main_tab_id';

export const useSingleTabGuard = () => {
  const [isBlocked, setIsBlocked] = useState(false);
  const tabId = useRef(Date.now().toString()).current;

  useEffect(() => {
    const existingTab = localStorage.getItem(TAB_KEY);

    // Si aucun onglet n'existe, prendre le contrôle
    if (!existingTab) {
      localStorage.setItem(TAB_KEY, tabId);
      setIsBlocked(false);
    } else {
      // Un onglet existe déjà
      // Pour une actualisation : prendre le contrôle (débloquer)
      // Pour un nouvel onglet : bloquer
      localStorage.setItem(TAB_KEY, tabId);
      setIsBlocked(false);
    }

    const handleStorage = (e: StorageEvent) => {
      if (e.key === TAB_KEY && e.newValue && e.newValue !== tabId) {
        // Un autre onglet a pris le contrôle, bloquer cet onglet
        setIsBlocked(true);
      }
    };

    const handleBeforeUnload = () => {
      if (localStorage.getItem(TAB_KEY) === tabId) {
        localStorage.removeItem(TAB_KEY);
      }
    };

    window.addEventListener('storage', handleStorage);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('storage', handleStorage);
      window.removeEventListener('beforeunload', handleBeforeUnload);

      if (localStorage.getItem(TAB_KEY) === tabId) {
        localStorage.removeItem(TAB_KEY);
      }
    };
  }, [tabId]);

  return isBlocked;
};