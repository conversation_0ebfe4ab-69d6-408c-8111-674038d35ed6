import {useState, useEffect} from "react";
import {Minus<PERSON>ir<PERSON>, RefreshCcw, RouteIcon} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
    Button,
    Modal,
    Form,
    Row,
    Col,
    Select,
    InputNumber,
    TimePicker,
    Card,
    Spin,
    Empty,
    Typography,
    Checkbox,
} from "antd";
import { PlusOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';

import moment from "moment";
import { RouteNameField } from "../index.ts";
import LineDisplay from "./LineDisplay.tsx";
import { assignLineStations, getLineStationsAndRoutes, updateAssignLineStation } from "../../features/admin/lineSlice.ts";
import { toast } from "react-toastify";
import {getStationsAll} from "../../features/admin/stationSlice.ts";
import {getOptionsAll} from "../../features/admin/optionSlice.ts";

const LineStationsManager: any = ({ record }: any) => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const [stationForm] = Form.useForm();
    const [stationModalVisible, setStationModalVisible] = useState(false);
    const [selectedLine, setSelectedLine] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [isReversed, setIsReversed] = useState(false);

    const dispatch = useDispatch();
    const [stationsData, setStationsData] = useState([]);
    const [optionsData, setOptionsData] = useState([]);
    const [lineAllData, setLineAllData] = useState<any>([]);

    /*|--------------------------------------------------------------------------
    |  - FETCH ALL DATA
    |-------------------------------------------------------------------------- */
    useEffect(() => {
        const fetchAllData = async () => {
            setLoading(true);
            try {
                const [stationsResult, lineDataResult, optionsResult] = await Promise.all([
                    dispatch(getStationsAll()),
                    dispatch(getLineStationsAndRoutes(record.id)),
                    dispatch(getOptionsAll())
                ]);

                setStationsData(stationsResult.payload.data);
                setLineAllData(lineDataResult.payload);
                setOptionsData(optionsResult.payload.data);
            } catch (error) {
                console.error('Failed to fetch data:', error);
                toast.error(t("messages.error_loading"));
            } finally {
                setLoading(false);
            }
        };

        fetchAllData();
    }, [dispatch, record.id]);
    
    /*|--------------------------------------------------------------------------
    |  - ASSIGN OR UPDATE STATION OF A SELECTED LINE
    |-------------------------------------------------------------------------- */
    const handleStationFormSubmit = async (values: any) => {
        setLoading(true);
        const toastId: any = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        try {
            const { stations } = values;

            // Format stations data
            const separatedStations = stations.map(({ id, station_id, departure_configs, type, has_departures, ...stationData }: any) => {
                return {
                    ...(id ? { id } : {}),
                    position: stationData.position,
                    id_station: station_id || stationData.id_station,
                    type: type,
                    has_departures: has_departures || false,
                    departure_configs: has_departures ? departure_configs?.map((config: any) => ({
                        option: config.option,
                        advancement_position: config.advancement,
                        direction: config.direction,
                        time: config.time?.format('HH:mm')
                    })) : []
                };
            });

            // Format routes data
            const separatedRoutes = [];
            for (let i = 1; i < stations.length; i++) {
                const prevStation = stations[i - 1];
                const currentStation = stations[i];

                if (currentStation?.route?.number_of_km) {
                    const routePayload = {
                        ...(currentStation.route.id ? { id: currentStation.route.id } : {}),
                        number_of_km: currentStation.route.number_of_km,
                        id_station_start: prevStation.station_id || prevStation.id_station,
                        id_station_end: currentStation.station_id || currentStation.id_station,
                        inter_station: true,
                        status: true
                    };
                    separatedRoutes.push(routePayload);
                }
            }

            const payload = {
                id_line: selectedLine?.id,
                stations: separatedStations,
                routes: separatedRoutes
            };

            // Check if we're updating or creating
            const isUpdate = lineAllData?.stations?.some((station: any) => station.id);

            if (isUpdate) {
                await dispatch(updateAssignLineStation(payload)).unwrap();
            } else {
                await dispatch(assignLineStations(payload)).unwrap();
            }

            const updatedLineData = await dispatch(getLineStationsAndRoutes(selectedLine.id)).unwrap();
            setLineAllData(updatedLineData);

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });

            handleReset();
        } catch (error: any) {
            console.error("Form submission error:", error);
            toast.update(toastId, {
                render: error.message || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_lines.confirmAction"),
            content: t("manage_lines.confirmUpdate"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleStationFormSubmit(values);
            },
            centered: true,
        });
    }

    /*|--------------------------------------------------------------------------
    |  - INITIALISE STATION ROUTES DATA TO UPDATE
    |-------------------------------------------------------------------------- */
    const handleUpdateStations = (line: any) => {
        setStationModalVisible(true);
        setSelectedLine(line);

        // Si pas de stations ou routes, initializer aver une station vide
        if (!lineAllData?.stations?.length && !lineAllData?.routes?.length) {
            stationForm.setFieldsValue({
                stations: [
                    {
                        id_station: null,
                        position: 1,
                        type: "TERMINUS",
                        has_departures: false,
                        departure_configs: []
                    },
                ],
            });
            return;
        }

        // Format existing stations data for the form
        const formattedStations = lineAllData.stations.map((station: any, index: number) => {
            // Determine if this station has departures and format departure configs
            const hasDepartures = station.has_departures;
            
            const formStation: any = {
                id: station.id,
                id_station: station.id,
                position: station.position,
                type: station.type,
                has_departures: hasDepartures,
                departure_configs: station.departure_configs?.map((config: any) => ({
                    option: config.option,
                    advancement: config.advancement_position,
                    direction: config.direction,
                    time: config.time ? moment(config.time, "HH:mm") : null
                })) || []
            };

            // Find corresponding route for stations after the first one
            if (index > 0 && lineAllData.routes) {
                const route = lineAllData.routes.find((r: any) => 
                    r.station_depart.id === lineAllData.stations[index - 1].id &&
                    r.station_arrival.id === station.id
                );
                if (route) {
                    formStation.route = {
                        id: route.id,
                        number_of_km: parseFloat(route.number_of_km),
                        inter_station: route.inter_station,
                        status: true
                    };
                }
            }
            return formStation;
        });

        stationForm.setFieldsValue({
            stations: formattedStations
        });
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setStationModalVisible(false);
    };

    return (
        <div className="p-4">
            <div
                className="mb-8 flex flex-col sm:flex-row justify-between items-center gap-4 border-b border-red-100 pb-4">
                <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <span className="bg-red-100 p-2 rounded-lg">
                        <RouteIcon style={{color: "var(--primary-color)"}}/>
                    </span>
                    {t("manage_lines.stations")}
                </h4>
                <div className="flex gap-1">
                    <Button
                        icon={<RefreshCcw className="mr-1" size={12}/>}
                        onClick={() => setIsReversed((prev:any) => !prev)}
                    >
                        {isReversed ? t("manage_lines.direction.normal") : t("manage_lines.direction.reverse")}
                    </Button>
                    <Button
                        className="btn-edit"
                        onClick={() => handleUpdateStations(record)}
                    >
                        {t("manage_lines.editStation")}
                    </Button>
                </div>
            </div>

            <LineDisplay loading={loading} record={lineAllData} isReversed={isReversed}/>

            <Modal
                width={1200}
                style={{maxWidth: "100%"}}
                title={t("manage_lines.addStationAssignment")}
                open={stationModalVisible}
                onCancel={() => handleReset()}
                onOk={() => stationForm.submit()}
                okText={t("manage_lines.save")}
                destroyOnClose
            >
                <Form 
                    form={stationForm} 
                    layout="vertical" 
                    onFinish={confirmSubmit}
                    validateMessages={{
                        required: t("common.fieldRequired"),
                    }}
                >
                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item>
                                <Form.List name="stations" rules={[
                                    {
                                        validator: async (_:any, stations:any) => {
                                            if (!stations || stations.length < 2) {
                                                return Promise.reject(new Error(t("manage_lines.errors.minimumStations")));
                                            }
                                        },
                                    },
                                ]}>
                                    {(fields: any, {add, remove}: any) => (
                                        <>
                                            <Row gutter={[16, 16]} style={{marginBottom: 8}}>
                                                <Col xs={10}>{t("manage_lines.labels.stations")}</Col>
                                                <Col xs={4}>{t("manage_lines.labels.position")}</Col>
                                                <Col xs={8}>{t("manage_lines.labels.stationType")}</Col>
                                            </Row>

                                            {fields.map(({key, name, ...restField}: any, index: number) => {
                                                return (
                                                    <div key={key}>
                                                        {index > 0 && (
                                                            <>
                                                                <div
                                                                    style={{
                                                                        marginBottom: 16,
                                                                        backgroundColor: '#fafafa',
                                                                        padding: 10,
                                                                        borderRadius: 8
                                                                    }}
                                                                >
                                                                    <h2 className="mb-6 border-b pb-3">
                                                                        {t("manage_lines.title_inter_station")}
                                                                    </h2>

                                                                    <Row gutter={[16, 16]}>
                                                                        <Col xs={24} sm={10}>
                                                                            <RouteNameField
                                                                                form={stationForm}
                                                                                name={name}
                                                                                stationsData={stationsData}
                                                                            />
                                                                        </Col>


                                                                        <Col xs={24} sm={10}>
                                                                            <Form.Item
                                                                                name={[name, 'route', 'number_of_km']}
                                                                                label={t("manage_lines.labels.numberOfKm")}
                                                                                rules={[{
                                                                                    required: true,
                                                                                    message: t("manage_lines.errors.numberOfKmRequired")
                                                                                }]}
                                                                            >
                                                                                <InputNumber
                                                                                    min={0}
                                                                                    className="w-full"
                                                                                    placeholder="km"
                                                                                />
                                                                            </Form.Item>
                                                                        </Col>
                                                                    </Row>
                                                                </div>
                                                            </>
                                                        )}

                                                        {/* Section Station */}
                                                        <Row gutter={[16, 16]} align="stretch">
                                                            <Col xs={24} sm={10}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, "id_station"]}
                                                                    rules={[{ required: true, message: t("manage_lines.errors.stationRequired") }]}
                                                                >
                                                                    <Select
                                                                        placeholder={t("manage_lines.placeholders.selectStation")}
                                                                        options={stationsData.map((type: any) => ({
                                                                            label: type[`nom_${currentLang}`],
                                                                            value: type.id,
                                                                        }))}
                                                                    />
                                                                </Form.Item>
                                                            </Col>

                                                            <Col xs={4} sm={4}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, "position"]}
                                                                    rules={[{
                                                                        required: true,
                                                                        message: t("manage_lines.errors.positionRequired")
                                                                    }]}
                                                                >
                                                                    <InputNumber
                                                                        min={1}
                                                                        className="w-full"
                                                                        disabled
                                                                        value={index + 1}
                                                                    />
                                                                </Form.Item>
                                                            </Col>

                                                            <Col xs={4} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, "type"]}
                                                                    rules={[{
                                                                        required: true,
                                                                        message: t("manage_lines.errors.typeRequired")
                                                                    }]}
                                                                >
                                                                    <Select
                                                                        placeholder={t("manage_lines.placeholders.selectStationType")}
                                                                        options={[
                                                                            { label: t("manage_stations.stationTypes.intermediate"), value: "INTER" },
                                                                            { label: t("manage_stations.stationTypes.terminus"), value: "TERMINUS" },
                                                                            { label: t("manage_stations.stationTypes.hidden"), value: "HIDDEN" }
                                                                        ]}
                                                                    />
                                                                </Form.Item>
                                                            </Col>

                                                            <Col xs={4} sm={2}>
                                                                <MinusCircle
                                                                    className="cursor-pointer mt-1"
                                                                    onClick={() => remove(name)}
                                                                    style={{color: "var(--primary-color)"}}
                                                                />
                                                            </Col>

                                                            <Col className="mb-4" xs={24}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, "has_departures"]}
                                                                    valuePropName="checked"
                                                                    initialValue={index === 0 || index === fields.length - 1}
                                                                >
                                                                    <Checkbox
                                                                        onChange={(e) => {
                                                                            if (!e.target.checked) {
                                                                                const currentValues = stationForm.getFieldsValue();
                                                                                if (currentValues.stations && currentValues.stations[name]) {
                                                                                    stationForm.setFieldValue(['stations', name, 'direction'], null);
                                                                                    stationForm.setFieldValue(['stations', name, 'advancement_position'], null);
                                                                                    stationForm.setFieldValue(['stations', name, 'departure_configs'], []);
                                                                                }
                                                                            }
                                                                        }}
                                                                    >
                                                                        {t("manage_lines.hasDepartures")}
                                                                    </Checkbox>
                                                                </Form.Item>
                                                                
                                                                <Form.Item
                                                                    noStyle
                                                                    shouldUpdate={(prevValues, currentValues) => {
                                                                        return prevValues?.stations?.[name]?.has_departures !== currentValues?.stations?.[name]?.has_departures;
                                                                    }}
                                                                >
                                                                    {({ getFieldValue }) => {
                                                                        const hasDepartures = getFieldValue(['stations', name, 'has_departures']);
                                                                        const isTerminus = index === 0 || index === fields.length - 1;
                                                                        
                                                                        if (hasDepartures) {
                                                                            return (
                                                                                <Spin spinning={loading}>
                                                                                    <Card title={t("manage_lines.departureConfigs")}>
                                                                                        <Form.List name={[name, "departure_configs"]}>
                                                                                            {(configFields, { add: addConfig, remove: removeConfig }) => (
                                                                                                <>
                                                                                                    {configFields.map(({ key, name: configName, ...restField }) => (
                                                                                                        <Row key={key} gutter={[16, 16]} align="middle">
                                                                                                            <Col xs={24} sm={6}>
                                                                                                                <Form.Item
                                                                                                                    {...restField}
                                                                                                                    name={[configName, "option"]}
                                                                                                                    rules={[{ required: true, message: t("manage_lines.errors.optionRequired") }]}
                                                                                                                >                                                                                                                    <Select
                                                                                                                        placeholder={t("manage_lines.placeholders.selectOption")}
                                                                                                                        options={optionsData.map((option: any) => ({
                                                                                                                            label: option[`nom_${currentLang}`],
                                                                                                                            value: option.id
                                                                                                                        }))}
                                                                                                                    />
                                                                                                                </Form.Item>
                                                                                                            </Col>
                                                                                                            <Col xs={24} sm={6}>
                                                                                                                <Form.Item
                                                                                                                    {...restField}
                                                                                                                    name={[configName, "advancement"]}
                                                                                                                    rules={[{ required: true, message: t("manage_lines.errors.advancementRequired") }]}
                                                                                                                >
                                                                                                                    <InputNumber
                                                                                                                        min={1}
                                                                                                                        className="w-full"
                                                                                                                        placeholder={t("manage_lines.placeholders.advancement")}
                                                                                                                    />
                                                                                                                </Form.Item>
                                                                                                            </Col>
                                                                                                            <Col xs={24} sm={6}>
                                                                                                                <Form.Item
                                                                                                                    {...restField}
                                                                                                                    name={[configName, "direction"]}
                                                                                                                    rules={[{ required: true, message: t("manage_lines.errors.directionRequired") }]}
                                                                                                                >
                                                                                                                    <Select
                                                                                                                        placeholder={t("manage_lines.placeholders.selectDirection")}
                                                                                                                        options={[
                                                                                                                            { label: t("manage_lines.direction.normal"), value: 1 },
                                                                                                                            { label: t("manage_lines.direction.reverse"), value: 2 }
                                                                                                                        ]}
                                                                                                                    />
                                                                                                                </Form.Item>
                                                                                                            </Col>
                                                                                                            <Col xs={24} sm={5}>
                                                                                                                <Form.Item
                                                                                                                    {...restField}
                                                                                                                    name={[configName, "time"]}
                                                                                                                    rules={[{ required: true, message: t("manage_lines.errors.timeRequired") }]}
                                                                                                                >
                                                                                                                    <TimePicker
                                                                                                                        format="HH:mm"
                                                                                                                        minuteStep={5}
                                                                                                                        className="w-full"
                                                                                                                    />
                                                                                                                </Form.Item>
                                                                                                            </Col>
                                                                                                            <Col xs={24} sm={1}>
                                                                                                                <MinusCircle
                                                                                                                    className="cursor-pointer"
                                                                                                                    onClick={() => removeConfig(configName)}
                                                                                                                    style={{color: "var(--primary-color)"}}
                                                                                                                />
                                                                                                            </Col>
                                                                                                        </Row>
                                                                                                    ))}
                                                                                                    <Button
                                                                                                        type="dashed"
                                                                                                        onClick={() => addConfig()}
                                                                                                        block
                                                                                                        icon={<PlusOutlined />}
                                                                                                    >
                                                                                                        {t("manage_lines.addDepartureConfig")}
                                                                                                    </Button>
                                                                                                </>
                                                                                            )}
                                                                                        </Form.List>
                                                                                    </Card>
                                                                                </Spin>
                                                                            );
                                                                        }
                                                                        
                                                                        return null;
                                                                    }}
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                    </div>
                                                );
                                            })}

                                            <Button
                                                type="dashed"
                                                onClick={() => {
                                                    const currentStations = stationForm.getFieldValue("stations") || [];
                                                    if (currentStations.length > 0) {
                                                        const previousStation = currentStations[currentStations.length - 1];
                                                        if (currentStations.length > 1 && previousStation.departure_time && previousStation.departure_time.length > 0) {
                                                            previousStation.departure_time = [];
                                                            stationForm.setFieldsValue({ stations: currentStations });
                                                        }
                                                    }
                                                    add({
                                                        position: currentStations.length + 1,
                                                        has_departures: false,
                                                        advancement_position: null,
                                                        direction: null,
                                                        ...(currentStations.length > 0 && {
                                                            route: {
                                                                is_regular: false,
                                                                status: false,
                                                            },
                                                        }),
                                                    });
                                                }}
                                                block
                                            >
                                                {t("manage_lines.addStation")}
                                            </Button>
                                        </>
                                    )}
                                </Form.List>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </div>
    );
};

export default LineStationsManager;
