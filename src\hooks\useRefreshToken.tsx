import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { refreshToken } from "../features/auth/authSlice";
import {jwtDecode} from "jwt-decode";

const TOKEN_STORAGE_KEY = "TOKEN";
const REFRESH_THRESHOLD = 5 * 60; // 5 minutes in seconds
const CHECK_INTERVAL = 60 * 1000; // Check every minute

const useRefreshToken = () => {
    const dispatch = useDispatch<any>();

    const isTokenExpiringSoon = (token: string) => {
        try {
            const decoded: any = jwtDecode(token);
            const currentTime = Math.floor(Date.now() / 1000);
            const timeUntilExpiry = decoded.exp - currentTime;
            
            // Return true if token will expire within REFRESH_THRESHOLD seconds
            return timeUntilExpiry > 0 && timeUntilExpiry <= REFRESH_THRESHOLD;
        } catch (error) {
            console.error("❌ Invalid token:", error);
            return false;
        }
    };

    useEffect(() => {
        const refreshIfNeeded = async () => {
            const oldToken = localStorage.getItem(TOKEN_STORAGE_KEY);

            if (oldToken && isTokenExpiringSoon(oldToken)) {    
                try {
                    const action = await dispatch(refreshToken());
                    const newToken = action.payload.token;
                    
                    if (newToken) {
                        localStorage.setItem(TOKEN_STORAGE_KEY, newToken);
                        console.log("🔄 Token refreshed successfully");
                    }
                } catch (error) {
                    console.error("❌ Error refreshing token:", error);
                }
            }
        };

        // Initial check
        refreshIfNeeded();

        // Set up periodic checks
        const refreshInterval = setInterval(refreshIfNeeded, CHECK_INTERVAL);

        return () => clearInterval(refreshInterval);
    }, [dispatch]);

    return null;
};

export default useRefreshToken;
