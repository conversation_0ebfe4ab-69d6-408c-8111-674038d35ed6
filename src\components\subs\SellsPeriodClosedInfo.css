/* SellsPeriodClosedInfo.css */

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes pulseRed {
  0% {
    opacity: 0.7;
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4);
  }
  70% {
    opacity: 1;
    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
  }
  100% {
    opacity: 0.7;
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sells-period-closed-container {
  position: relative;
  margin: 20px 0;
  border-radius: 12px;
  background: linear-gradient(145deg, #fff1f0, #ffccc7);
  box-shadow: 0 8px 24px rgba(255, 77, 79, 0.15);
  border: 1px solid #ffa39e;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-out;
}

/* Styles for when isOpen is true */
.sells-period-closed-container-open {
  background: linear-gradient(145deg, #fee2e2, #fecaca);
  box-shadow: 0 8px 24px rgba(220, 38, 38, 0.25);
  border: 1px solid #ef4444;
  animation: pulseRed 2s infinite;
}

.sells-period-closed-container:hover {
  box-shadow: 0 12px 28px rgba(255, 77, 79, 0.2);
  transform: translateY(-2px);
}

.left-accent-border {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6px;
  background: #ff4d4f;
  animation: pulse 2s infinite;
}

.sells-period-closed-container-open .left-accent-border {
  background: #dc2626;
  width: 8px;
}

.content-wrapper {
  display: flex;
  align-items: flex-start;
  padding: 22px 25px;
  gap: 18px;
}

.icon-container {
  flex-shrink: 0;
  position: relative;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.2);
  animation: bounce 2s ease-in-out infinite;
}

.lock-icon {
  color: #ff4d4f;
}

.text-content {
  flex: 1;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.alert-icon {
  color: #ff4d4f;
}

.sells-period-closed-container-open .alert-icon,
.sells-period-closed-container-open .lock-icon {
  color: #dc2626;
}

.title {
  margin: 0;
  color: #ff4d4f;
  font-size: 1.2em;
  font-weight: 600;
}

.sells-period-closed-container-open .title {
  color: #dc2626;
  font-weight: 700;
}

.description {
  margin: 0 0 12px 0;
  color: #5c5c5c;
  font-size: 0.95em;
  line-height: 1.5;
}

.next-opening-date {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  font-size: 0.9em;
  color: #555;
}

.calendar-icon {
  color: #ff7875;
}

.info-note {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85em;
  color: #888;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed rgba(255, 77, 79, 0.3);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .content-wrapper {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 18px 15px;
  }

  .header-row {
    justify-content: center;
  }

  .next-opening-date, .info-note {
    justify-content: center;
  }
}
