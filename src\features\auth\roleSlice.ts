import { createAsyncThunk } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";



export const getRolesAll: any = createAsyncThunk(
    "getRolesAll",
    async (_:any,thunkAPI:any) => {
        try {
            const url:string = `/roles/all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getRoles: any = createAsyncThunk(
    "getRoles",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params:any,
            sort:any,
            filter:any
        },
        thunkAPI:any
    ) => {
        try {
            const { name } = data.params;
            const sort = data.sort;
            let url = `/roles?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];
            if (name) {
                searchParams.push(`name:${name}`);
            }
            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }
            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeRole: any = createAsyncThunk(
    "storeRole",
    async (data, thunkAPI:any) => {
        try {
            const url:string = `/role-store`;
            const resp = await api.post(url, data);
            return resp.data;
        }  catch (error: any) {
            if (error.response) {
                console.log(error.response)
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateRole: any = createAsyncThunk(
    "updateRole",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `/role-update/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteRole: any = createAsyncThunk(
    "deleteRole",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `/role-delete/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAssignedUnassignedPermissionsRole: any = createAsyncThunk(
    "getRolesWithPermissionsAll",
    async (roleId:any,thunkAPI:any) => {
        try {
            const url:string = `/role/${roleId}/permissions`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAssUnassRoles: any = createAsyncThunk(
    "getAssUnassRoles",
    async (adminId: number, thunkAPI:any) => {
        try {
            const url: string = `/admin/${adminId}/roles`;
            const response: any = await api.get(url);
            return response.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "Une erreur est survenue.");
            } else {
                return thunkAPI.rejectWithValue("Une erreur inattendue est survenue.");
            }
        }
    }
);

export const assignPermissions: any = createAsyncThunk(
    "assignPermissions",
    async ({ roleId, permissionIds }: { roleId: number; permissionIds: number[] }, thunkAPI:any) => {
        try {
            const url: string = `/roles/${roleId}/assign-permissions`;
            const resp: any = await api.post(url, { permissionIds });
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "Une erreur est survenue.");
            } else {
                return thunkAPI.rejectWithValue("Une erreur inattendue est survenue.");
            }
        }
    }
);

export const removePermissions: any = createAsyncThunk(
    "removePermissions",
    async ({ roleId, permissionIds }: { roleId: number; permissionIds: number[] }, thunkAPI:any) => {
        try {
            const url: string = `/roles/${roleId}/remove-permissions`;
            const resp: any = await api.post(url, { permissionIds });
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "Une erreur est survenue.");
            } else {
                return thunkAPI.rejectWithValue("Une erreur inattendue est survenue.");
            }
        }
    }
);

export const assignRolesToUser: any = createAsyncThunk(
    "assignRolesToUser",
    async ({ adminId, roleIds }: { adminId: number; roleIds: number[] }, thunkAPI:any) => {
        try {
            const url: string = `/users/${adminId}/assign-roles`;
            const resp: any = await api.post(url, { roleIds });
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "Une erreur est survenue.");
            } else {
                return thunkAPI.rejectWithValue("Une erreur inattendue est survenue.");
            }
        }
    }
);

export const removeRolesFromUser: any = createAsyncThunk(
    "removeRolesFromUser",
    async ({ adminId, roleIds }: { adminId: number; roleIds: number[] }, thunkAPI:any) => {
        try {
            const url: string = `/users/${adminId}/remove-roles`;
            const resp: any = await api.post(url, { roleIds });
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "Une erreur est survenue.");
            } else {
                return thunkAPI.rejectWithValue("Une erreur inattendue est survenue.");
            }
        }
    }
);