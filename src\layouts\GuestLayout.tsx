import {  Layout } from 'antd';
import {Outlet} from "react-router-dom";


const {  Content } = Layout;

const contentStyle: React.CSSProperties = {

};

const layoutStyle = {
    overflow: 'hidden',
};

const GuestLayout = () => {

    return (
        <>
            <Layout style={layoutStyle}>

                <Content style={contentStyle}>
                    <Outlet/>
                </Content>

            </Layout>
        </>
    )
};

export default GuestLayout;