import { useEffect, useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { deleteOption, getOptions, storeOption, updateOption } from "../../../features/admin/optionSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";
import { useSelector } from "react-redux";
import { getSeasonsAll } from "../../../features/admin/seasonSlice.ts";

const ManageOptions = () => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;
    const dispatch = useDispatch<any>();
    const actionRef = useRef<any>();

    const [form] = Form.useForm();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [editingOption, setEditingOption] = useState<any>(null);
    const [viewMode, setViewMode] = useState(false);
    const [loading, setLoading] = useState(false);
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(15);
    const [total, setTotal] = useState(0);


    const seasons = useSelector((state: any) => state.season.items?.data);
    
    const fetchStoreData = async () => { 
        if(!seasons?.length){
            await dispatch(getSeasonsAll()).unwrap()
        }
    }

    useEffect(() => {
        fetchStoreData();
    }, []);


    const breadcrumbItems = [
        { title: <Link to="/admin">{t("admin.dashboard")}</Link> },
        { title: t("manage_options.title") },
    ];

    {/*|--------------------------------------------------------------------------
    | FETCH OPTIONS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */}
    const handleGetOptions: any = (params: any, sort: any, filter: any) => {
        return dispatch(getOptions({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
                toast.error(t("common.errors.unexpected"));
            });
    }

    {/*|--------------------------------------------------------------------------
    | HANDLE ADD OPTION
    |-------------------------------------------------------------------------- */}
    const handleAdd = () => {
        setEditingOption(null);
        setViewMode(false);
        form.resetFields();
        setIsModalVisible(true);
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE EDIT OPTION
    |-------------------------------------------------------------------------- */}
    const handleEdit = (record: any) => {
        setEditingOption(record);
        setViewMode(false);
        form.setFieldsValue({
            nom_fr: record.nom_fr,
            nom_en: record.nom_en,
            nom_ar: record.nom_ar,
            seasons: record.seasons?.map((season: any) => season.id) || []
        });
        setIsModalVisible(true);
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE VIEW OPTION
    |-------------------------------------------------------------------------- */}
    const handleView = (record: any) => {
        setEditingOption(record);
        setViewMode(true);
        form.setFieldsValue({
            nom_fr: record.nom_fr,
            nom_en: record.nom_en,
            nom_ar: record.nom_ar,
            seasons: record.seasons?.map((season: any) => season.id) || []
        });
        setIsModalVisible(true);
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE DELETE OPTION
    |-------------------------------------------------------------------------- */}
    const handleDelete = async (id: number) => {
        try {
            await dispatch(deleteOption(id)).unwrap();
            toast.success(t("messages.success"));
            actionRef.current?.reload();
        } catch (error: any) {
            toast.error(error.message || t("common.errors.unexpected"));
        }
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE SAVE OPTION (CREATE OR UPDATE)
    |-------------------------------------------------------------------------- */}
     const handleFormSubmit:any = async (values: any) => {
           setLoading(true);
           const payload = editingOption ? { id: editingOption.id, ...values } : values;
           const toastId = toast.loading(t("messages.loading"), {
               position: 'top-center',
           });
           try {
               if (editingOption) {
                   await dispatch(updateOption(payload)).unwrap();
               } else {
                   await dispatch(storeOption(values)).unwrap();
               }
               toast.update(toastId, {
                   render: t("messages.success"),
                   type: "success",
                   isLoading: false,
                   autoClose: 3000
               });
               actionRef.current?.reload();
               handleReset();
           } catch (error: any) {
               const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                   name: field,
                   errors: [error.errors[field][0]],
               }));
               form.setFields(fieldErrors);
               toast.update(toastId, {
                   render: t("messages.error"),
                   type: "error",
                   isLoading: false,
                   autoClose: 3000
               });
           } finally {
               setLoading(false);
           }
    };

    const confirmSubmit:any = (values: any) => {
        const modal:any = Modal.confirm({
            title: t("manage_options.confirmAction"),
            content: editingOption
                ? t("manage_options.confirmUpdate")
                : t("manage_options.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    {/*|--------------------------------------------------------------------------
    | COLUMNS DEFINITION
    |-------------------------------------------------------------------------- */}
    const columns :any = [
        {
            title: t("manage_options.labels.nom_fr"),
            dataIndex: "nom_fr",
            key: "nom_fr",
            sorter: true,
            render: (text: any) => text || "-",
        },
        {
            title: t("manage_options.labels.nom_en"),
            dataIndex: "nom_en",
            key: "nom_en",
            sorter: true,
            render: (text: any) => text || "-",
        },
        {
            title: t("manage_options.labels.nom_ar"),
            dataIndex: "nom_ar",
            key: "nom_ar",
            sorter: true,
            render: (text: any) => text || "-",
        },
        {
            title: t("manage_options.labels.seasons"),
            dataIndex: "seasons",
            key: "seasons",
            search: false,
            render: (seasons: any) => {
                if (!seasons || seasons.length === 0) return "-";
                return seasons.map((season: any) => season.nom_fr).join(" | ");
            },
        },
        {
            title: t("manage_options.labels.createdAt"),
            dataIndex: "created_at",
            key: "created_at",
            search: false,
            sorter: true,
            render: (text: any) => {
                if (!text) return "-";
                return new Date(text).toLocaleDateString(currentLang === 'fr' ? 'fr-FR' : 'en-US');
            },
        },
        {
            title: `${t("manage_options.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_options") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_options") && (
                            <Popconfirm
                                title={t("manage_options.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("common.yes")}
                                cancelText={t("common.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];


    const handleReset = () => {
        setLoading(false);
        form.resetFields();
        setIsModalVisible(false);
        setEditingOption(null);
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_options.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetOptions(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={"id"}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{ x: 800 }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        toolBarRender={() => [
                            hasPermission('create_options') && (
                                <Button
                                    key="add"
                                    className="btn-add"
                                    onClick={handleAdd}
                                >
                                    {t("manage_options.add")}
                                </Button>
                            ),
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_options.details")
                        : editingOption
                        ? t("manage_options.edit")
                        : t("manage_options.add")
                }
                open={isModalVisible}
                onCancel={handleReset}
                footer={
                    viewMode ? [
                        <Button key="close" onClick={() => setIsModalVisible(false)}>
                            {t("common.close")}
                        </Button>
                    ] : [
                        <Button key="cancel" onClick={() => setIsModalVisible(false)}>
                            {t("common.cancel")}
                        </Button>,
                        <Button key="submit" type="primary" onClick={form.submit}>
                            {t("common.save")}
                        </Button>
                    ]
                }
            >
                <Form form={form} layout="vertical" disabled={viewMode} 
                    onFinish={confirmSubmit}>
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_options.labels.nom_fr")}
                                name="nom_fr"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_options.errors.nomFrRequired"),
                                    },
                                ]}
                            >
                                <Input placeholder={t("manage_options.placeholders.nom_fr")} />
                            </Form.Item>
                        </Col>

                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_options.labels.nom_en")}
                                name="nom_en"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_options.errors.nomEnRequired"),
                                    },
                                ]}
                            >
                                <Input placeholder={t("manage_options.placeholders.nom_en")} />
                            </Form.Item>
                        </Col>

                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_options.labels.nom_ar")}
                                name="nom_ar"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_options.errors.nomArRequired"),
                                    },
                                ]}
                            >
                                <Input placeholder={t("manage_options.placeholders.nom_ar")} />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Form.Item
                                label={t("manage_options.labels.seasons")}
                                name="seasons"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_options.errors.seasonsRequired"),
                                    },
                                ]}
                            >
                                <Select
                                    mode="multiple"
                                    placeholder={t("manage_options.placeholders.seasons")}
                                    style={{ width: '100%' }}
                                    options={seasons?.map((season: any) => ({
                                        label: season.nom_fr,
                                        value: season.id
                                    }))}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
};

export default ManageOptions;