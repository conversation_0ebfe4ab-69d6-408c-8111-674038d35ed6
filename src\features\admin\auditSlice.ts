import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/audits';

const initialState = {
    items: [],
    paginatedItems: null,
    statistics: null,
    mostActiveUsers: [],
    mostAuditedModels: [],
    recentActivity: [],
    modelTypes: [],
    eventTypes: [],
    currentItem: null,
    loading: false,
    error: null
};



export const getAudits: any = createAsyncThunk(
    "getAudits",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any,
            sort: any,
            filter: any
        },
        thunkAPI: any
    ) => {
        try {
            const { event, model_type, created_at, user_id, ip_address } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (event) {
                searchParams.push(`event:${event}`);
            }
            if (model_type) {
                searchParams.push(`auditable_type:${encodeURIComponent(model_type)}`);
            }
            if (user_id) {
                searchParams.push(`user_id:${user_id}`);
            }
            if (ip_address) {
                searchParams.push(`ip_address:${encodeURIComponent(ip_address)}`);
            }
            if (created_at && Array.isArray(created_at) && created_at.length === 2) {
                const [startDate, endDate] = created_at;
                if (startDate) searchParams.push(`created_at_start:${startDate}`);
                if (endDate) searchParams.push(`created_at_end:${endDate}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


export const getAuditDetails: any = createAsyncThunk(
    "getAuditDetails",
    async (id: number, thunkAPI: any) => {
        try {
            const url = `${URL}/${id}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getModelAudits: any = createAsyncThunk(
    "getModelAudits",
    async ({ modelType, modelId, perPage = 15 }: { modelType: string; modelId: number; perPage?: number }, thunkAPI: any) => {
        try {
            const url = `${URL}/model/${modelType}/${modelId}?perPage=${perPage}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getUserAudits: any = createAsyncThunk(
    "getUserAudits",
    async ({ userId, userType, perPage = 15 }: { userId: number; userType?: string; perPage?: number }, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            if (userType) params.append('user_type', userType);
            params.append('perPage', perPage.toString());

            const url = `${URL}/user/${userId}?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getMyAudits: any = createAsyncThunk(
    "getMyAudits",
    async (perPage: number = 15, thunkAPI: any) => {
        try {
            const url = `${URL}/my-audits?perPage=${perPage}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getEventAudits: any = createAsyncThunk(
    "getEventAudits",
    async ({ event, perPage = 15 }: { event: string; perPage?: number }, thunkAPI: any) => {
        try {
            const url = `${URL}/event/${event}?perPage=${perPage}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAuditStatistics: any = createAsyncThunk(
    "getAuditStatistics",
    async ({ startDate, endDate }: { startDate?: string; endDate?: string } = {}, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            const url = `${URL}/statistics?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getMostActiveUsers: any = createAsyncThunk(
    "getMostActiveUsers",
    async ({ limit = 10, startDate, endDate }: { limit?: number; startDate?: string; endDate?: string } = {}, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            params.append('limit', limit.toString());
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            const url = `${URL}/most-active-users?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getMostAuditedModels: any = createAsyncThunk(
    "getMostAuditedModels",
    async ({ limit = 10, startDate, endDate }: { limit?: number; startDate?: string; endDate?: string } = {}, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            params.append('limit', limit.toString());
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            const url = `${URL}/most-audited-models?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getRecentActivity: any = createAsyncThunk(
    "getRecentActivity",
    async (limit: number = 20, thunkAPI: any) => {
        try {
            const url = `${URL}/recent-activity?limit=${limit}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getModelTypes: any = createAsyncThunk(
    "getModelTypes",
    async (_: any, thunkAPI: any) => {
        try {
            const url = `${URL}/model-types`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getEventTypes: any = createAsyncThunk(
    "getEventTypes",
    async (_: any, thunkAPI: any) => {
        try {
            const url = `${URL}/event-types`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const auditSlice = createSlice({
    name: 'audit',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // Get audits
            .addCase(getAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
                state.items = action.payload.data;
            })
            .addCase(getAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get audit details
            .addCase(getAuditDetails.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAuditDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.currentItem = action.payload;
            })
            .addCase(getAuditDetails.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get model audits
            .addCase(getModelAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getModelAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
                state.items = action.payload.data;
            })
            .addCase(getModelAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get user audits
            .addCase(getUserAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getUserAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
                state.items = action.payload.data;
            })
            .addCase(getUserAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get my audits
            .addCase(getMyAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMyAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
                state.items = action.payload.data;
            })
            .addCase(getMyAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get event audits
            .addCase(getEventAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getEventAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
                state.items = action.payload.data;
            })
            .addCase(getEventAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get audit statistics
            .addCase(getAuditStatistics.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAuditStatistics.fulfilled, (state, action) => {
                state.loading = false;
                state.statistics = action.payload.data;
            })
            .addCase(getAuditStatistics.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get most active users
            .addCase(getMostActiveUsers.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMostActiveUsers.fulfilled, (state, action) => {
                state.loading = false;
                state.mostActiveUsers = action.payload.data;
            })
            .addCase(getMostActiveUsers.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get most audited models
            .addCase(getMostAuditedModels.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMostAuditedModels.fulfilled, (state, action) => {
                state.loading = false;
                state.mostAuditedModels = action.payload.data;
            })
            .addCase(getMostAuditedModels.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get recent activity
            .addCase(getRecentActivity.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getRecentActivity.fulfilled, (state, action) => {
                state.loading = false;
                state.recentActivity = action.payload.data;
            })
            .addCase(getRecentActivity.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get model types
            .addCase(getModelTypes.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getModelTypes.fulfilled, (state, action) => {
                state.loading = false;
                state.modelTypes = action.payload.data;
            })
            .addCase(getModelTypes.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get event types
            .addCase(getEventTypes.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getEventTypes.fulfilled, (state, action) => {
                state.loading = false;
                state.eventTypes = action.payload.data;
            })
            .addCase(getEventTypes.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = auditSlice.actions;
export default auditSlice.reducer;