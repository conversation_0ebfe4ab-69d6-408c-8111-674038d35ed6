import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {jwtDecode} from 'jwt-decode';
import api from "../../config/axiosConfig.tsx";


export const fetchCaptcha:any = createAsyncThunk(
    'auth/fetchCaptcha',
    async (_, thunkAPI) => {
        try {
            const response = await api.get('/captcha');
            localStorage.setItem("hashed_captcha_text",response.data.hashed_captcha_text);
            return response.data.captcha;
        } catch (error: any) {
            console.log(error)
            return thunkAPI.rejectWithValue(error.response?.data || 'Captcha fetch failed');
        }
    }
);

export const login = createAsyncThunk(
    'auth/login',
    async (credentialsData: any, thunkAPI) => {
        try {
            const response = await api.post('/login-admin', credentialsData);
            localStorage.setItem('TOKEN', response.data.token);
            localStorage.setItem('PERMISSIONS', JSON.stringify(response.data.permissions));
            localStorage.removeItem("hashed_captcha_text");
            return response.data;
        } catch (error: any) {
            console.log(error);
            if (error.response) {
              const { status, data } = error.response;
              if (status === 403) {
                window.location.href = "/unauthorized";
              }
              return thunkAPI.rejectWithValue(data);
            } else {
              return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
          }
    }
);



export const refreshToken = createAsyncThunk(
    'auth/refreshToken',
    async (_, thunkAPI) => {
        try {
            const response = await api.post('/refresh-token');
            localStorage.setItem('TOKEN', response.data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Token refresh failed');
        }
    }
);


export const logout = createAsyncThunk(
    'auth/logout',
    async (_, thunkAPI) => {
        try {
            const response = await api.get('/logout-admin');
            localStorage.removeItem('TOKEN');
            localStorage.removeItem('PERMISSIONS');
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Logout failed');
        }
    }
);

export const checkAuthStatus = createAsyncThunk(
    'auth/checkAuthStatus',
    async (_, thunkAPI) => {
        const token = localStorage.getItem('TOKEN');
        if (token) {
            try {
                const decoded: any = jwtDecode(token);
                const currentTime = Date.now();

                // Vérification immédiate de l'expiration sans appeler l'API
                if (decoded.exp * 1000 < currentTime) {
                    localStorage.removeItem('TOKEN');
                    return thunkAPI.rejectWithValue('Token expired');
                }
                return { isAuthenticated: true, auth_data: decoded };

                // Si la validation du token local est OK, appelez l'API
                /*const response = await api.get('/verify-token');
                if (response.status === 200) {
                    return { isAuthenticated: true, auth_data: decoded };
                } else {
                    localStorage.removeItem('TOKEN');
                    return thunkAPI.rejectWithValue('Token is invalid');
                }*/
            } catch (error: any) {
                localStorage.removeItem('TOKEN');
                return thunkAPI.rejectWithValue('Failed to decode token');
            }
        }

        return thunkAPI.rejectWithValue('No token found');
    }
);

const initialState = {
    isAuthenticated: null,
    user: null,
    token: null,
    permissions: [],
    error: null,
    loading: false,
    loadingCaptcha: false,
    captcha: '',
};


const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        resetAuthState: (state:any) => {
            state.isAuthenticated = null;
            state.user = null;
            state.token = null;
            state.permissions = [];
            state.error = null;
            state.loading = false;
            state.loadingCaptcha = false;
            state.captcha = '';
        }
    },
    extraReducers: (builder) => {
        builder

            /*|--------------------------------------------------------------------------
            | CAPTCHA CHANGE STATES
            |-------------------------------------------------------------------------- */
            .addCase(fetchCaptcha.pending, (state) => {
                state.loadingCaptcha = true;
            })
            .addCase(fetchCaptcha.fulfilled, (state, action) => {
                state.loadingCaptcha = false;
                state.captcha = action.payload;
            })
            .addCase(fetchCaptcha.rejected, (state, action) => {
                state.loadingCaptcha = false;
                state.error = action.payload;
            })

            /*|--------------------------------------------------------------------------
            | LOGIN CHANGE STATES
            |-------------------------------------------------------------------------- */
            .addCase(login.pending, (state) => {
                state.loading = true;
            })
            .addCase(login.fulfilled, (state:any, action:any) => {
                state.loading = false;
                state.isAuthenticated = true;

                state.permissions = action.payload.permissions;
                state.token = action.payload.token;

                const decoded: any = jwtDecode(action.payload.token);
                state.user = decoded.user;
                state.roles = decoded.user; 
            })
            .addCase(login.rejected, (state:any, action:any) => {
                state.loading = false;
                state.error = action.payload;
            })

            /*|--------------------------------------------------------------------------
            | LOGOUT CHANGE STATES
            |-------------------------------------------------------------------------- */
            .addCase(logout.pending, (state:any) => {
                state.loading = true;
            })
            .addCase(logout.fulfilled, (state:any) => {
                state.loading = false;
                state.isAuthenticated = false;
                state.permissions = [];
                state.roles = [];
                state.user = null;
                state.token = null;
            })
            .addCase(logout.rejected, (state:any, action:any) => {
                state.loading = false;
                state.error = action.payload;
            })

            /*|--------------------------------------------------------------------------
            | CHECK AUTH STATUS
            |-------------------------------------------------------------------------- */
            .addCase(checkAuthStatus.pending, (state:any) => {
                state.loading = true;
            })
            .addCase(checkAuthStatus.fulfilled, (state:any, action:any) => {
                state.loading = false;
                state.isAuthenticated = action.payload.isAuthenticated;
                state.permissions = action.payload.auth_data.permissions;
                state.roles = action.payload.auth_data.roles;
                state.user = action.payload.auth_data.user;
            })
            .addCase(checkAuthStatus.rejected, (state:any, action:any) => {
                state.loading = false;
                state.isAuthenticated = false;
                state.error = action.payload;
            });
    },
});

export const { resetAuthState } = authSlice.actions;
export default authSlice.reducer;
