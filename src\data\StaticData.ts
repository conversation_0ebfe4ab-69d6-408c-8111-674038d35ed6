import { useTranslation } from "react-i18next";

export const GetRestDaysData = () => {
    const { t } = useTranslation();
    return [
        { id: 1, name: t("manage_newSubs.restDays.1") },
        { id: 2, name: t("manage_newSubs.restDays.2") },
        { id: 3, name: t("manage_newSubs.restDays.3") },
        { id: 4, name: t("manage_newSubs.restDays.4") },
        { id: 5, name: t("manage_newSubs.restDays.5") },
        { id: 6, name: t("manage_newSubs.restDays.6") },
        { id: 7, name: t("manage_newSubs.restDays.7") },
    ];
};

export const periodicityOptions = [
    {id:1, label: "Semaine", value: "WEEKLY" },
    {id:2, label: "Mensuel", value: "MONTHLY" },
    {id:3, label: "<PERSON><PERSON>", value: "YEARLY" },
    {id:4, label: "Biannuel", value: "BIANNUAL" },
];


export const clientTypesData = [
    {
        id: 1,
        name: "Eleve",
        color:"#55555588",
        hasCIN: false,
        isStudent:true,
        is_stagiaire:false,
        isImpersonal: false,
        created_at: "2024-01-15"
    },
    {
        id: 2,
        name: "Etudiant",
        hasCIN: true,
        color:"#11885588",
        isStudent:true,
        is_stagiaire:false,
        isImpersonal: false,
        created_at: "2024-01-15"
    },
    {
        id: 3,
        name: "Stagiaire",
        hasCIN: true,
        color:"#99995588",
        isStudent:true,
        is_stagiaire:true,
        isImpersonal: false,
        created_at: "2024-01-15"
    },
    {
        id: 4,
        name: "Civil",
        hasCIN: true,
        isImpersonal: false,
        color:"#FF223344",
        is_stagiaire:false,
        isStudent:false,
        created_at: "2024-01-18"
    },
    {
        id: 5,
        name: "impersonnelle",
        isImpersonal: true,
        hasCIN: true,
        color:"#4a4a4a",
        is_stagiaire:false,
        isStudent:false,
        created_at: "2024-01-18"
    },
];