import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig";

const URL = "/seasons";

const initialState = {
  items: [],
  paginatedItems: null,
  loading: false,
  error: null,
  currentItem: null,
};

export const getSeasonsAll: any = createAsyncThunk(
  "getSeasonsAll",
  async (_: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getSeasons: any = createAsyncThunk(
  "getSeasons",
  async (
    data: {
      pageNumber: number;
      perPage: number;
      params: any;
      sort: any;
      filter: any;
    },
    thunkAPI: any
  ) => {
    try {
      const { nom_fr, nom_en, nom_ar } = data.params;
      const sort = data.sort;

      let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
      const searchParams = [];

      if (nom_fr) searchParams.push(`nom_fr:${nom_fr}`);
      if (nom_en) searchParams.push(`nom_en:${nom_en}`);
      if (nom_ar) searchParams.push(`nom_ar:${nom_ar}`);

      if (searchParams.length > 0) {
        url += `&search=${searchParams.join(";")}`;
      }

      const orderBy = [];
      const sortedBy = [];

      for (const [field, order] of Object.entries(sort)) {
        orderBy.push(field);
        sortedBy.push(order === "ascend" ? "asc" : "desc");
      }

      if (orderBy.length > 0) {
        url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
      }
      const joint = "&searchJoin=and";
      url += joint;

      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      }
      return thunkAPI.rejectWithValue("An unexpected error occurred.");
    }
  }
);

export const storeSeason: any = createAsyncThunk(
  "storeSeason",
  async (data: any, thunkAPI: any) => {
    try {
      const resp: any = await api.post(URL, data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      }
      return thunkAPI.rejectWithValue("An unexpected error occurred.");
    }
  }
);

export const updateSeason: any = createAsyncThunk(
  "updateSeason",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, ...payload } = data;
      const resp: any = await api.put(`${URL}/${id}`, payload);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      }
      return thunkAPI.rejectWithValue("An unexpected error occurred.");
    }
  }
);

export const deleteSeason: any = createAsyncThunk(
  "deleteSeason",
  async (id: number, thunkAPI: any) => {
    try {
      const resp: any = await api.delete(`${URL}/${id}`);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      }
      return thunkAPI.rejectWithValue("An unexpected error occurred.");
    }
  }
);

const seasonSlice = createSlice({
  name: "season",
  initialState,
  reducers: {
    setCurrentItem: (state, action) => {
      state.currentItem = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSeasonsAll.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSeasonsAll.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
      })
      .addCase(getSeasonsAll.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
      })
      .addCase(getSeasons.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSeasons.fulfilled, (state, action) => {
        state.loading = false;
        state.paginatedItems = action.payload;
      })
      .addCase(getSeasons.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
      })
      .addCase(storeSeason.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(storeSeason.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(storeSeason.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
      })
      .addCase(updateSeason.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSeason.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateSeason.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
      })
      .addCase(deleteSeason.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSeason.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(deleteSeason.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
      });
  },
});

export const { setCurrentItem, clearError } = seasonSlice.actions;
export default seasonSlice.reducer;
