# Bus Location Season Management Frontend Implementation Guide

## API Endpoints

### Location Seasons Management
- **GET /api/location-seasons**: List all location seasons (paginated)
- **GET /api/location-seasons/all**: Get all location seasons
- **GET /api/location-seasons/{id}**: Get specific location season
- **POST /api/location-seasons**: Create location season
- **PUT /api/location-seasons/{id}**: Update location season
- **DELETE /api/location-seasons/{id}**: Delete location season
- **GET /api/location-seasons/active**: Get all active location seasons

### Season-Vehicle Type Pricing Management
- **GET /api/type-vehicule-saison-locations**: List all season-vehicle pricing
- **GET /api/type-vehicule-saison-locations/{id}**: Get specific pricing
- **POST /api/type-vehicule-saison-locations**: Create season-vehicle pricing
- **PUT /api/type-vehicule-saison-locations/{id}**: Update season-vehicle pricing
- **DELETE /api/type-vehicule-saison-locations/{id}**: Delete season-vehicle pricing
- **GET /api/type-vehicule-saison-locations/type-vehicule/{typeVehiculeId}**: Get pricing by vehicle type

## Data Models

### Location Season Model
```typescript
interface LocationSeason {
  id: number;
  nom_fr: string;        // Required
  nom_en: string;        // Optional
  nom_ar: string;        // Optional
  start_date: string;    // Required, format: YYYY-MM-DD
  end_date: string;      // Required, format: YYYY-MM-DD
  status: boolean;       // Required
  created_at: string;
  updated_at: string;
  type_vehicule_saison_locations?: TypeVehiculeSaisonLocation[];
}
```

### Season-Vehicle Type Pricing Model
```typescript
interface TypeVehiculeSaisonLocation {
  id: number;
  id_type_vehicule: number;    // Required
  id_saison_location: number;  // Required
  prix_km: number;            // Required
  status: boolean;            // Required
  type_vehicule?: TypeVehicule;
  season?: LocationSeason;
}
```

## Required Forms/Components

### Location Season Management Form
1. Basic Information Section
   - Season name (FR) - Required
   - Season name (EN) - Optional
   - Season name (AR) - Optional
   - Status toggle

2. Date Range Section
   - Start date picker
   - End date picker
   - Date validation
   - Season overlap checker

3. Associated Pricing Section
   - Vehicle type pricing matrix
   - Bulk pricing tools
   - Price per kilometer inputs

### Season-Vehicle Type Pricing Form
1. Main Information
   - Vehicle type selector
   - Season selector
   - Price per kilometer input
   - Status toggle

2. Batch Operations
   - Bulk price update
   - Multiple vehicle type selection
   - Multiple season selection

## Management Features

### Season Management
1. CRUD Operations
   - Create new seasons
   - Edit existing seasons
   - Delete seasons
   - View season details

2. Season Calendar
   - Visual calendar representation
   - Season overlap detection
   - Date range selection
   - Season status indicator

3. Pricing Management
   - Price matrix by vehicle type
   - Bulk price updates
   - Price history tracking
   - Price comparison tools

4. Status Management
   - Active/Inactive toggle
   - Status change history
   - Scheduled status changes
   - Status dependencies

### Data Display Features
1. Season List View
   - Sortable columns
   - Filterable data
   - Status indicators
   - Quick actions
   - Bulk operations

2. Season Detail View
   - Complete season information
   - Associated vehicle types
   - Pricing breakdown
   - Date range visualization
   - Status history

3. Pricing Matrix
   - Vehicle type vs Season grid
   - Price comparison tools
   - Bulk edit capabilities
   - Price history tracking

## Validation Rules

### Season Validation
1. Date Rules
   - End date must be after start date
   - No date range overlap with existing seasons
   - Valid date format (YYYY-MM-DD)
   - Future dates validation

2. Name Validation
   - Required French name
   - Valid character length
   - Language-specific validation
   - Unique name check

3. Status Rules
   - Status change validation
   - Active season requirements
   - Dependency checks
   - Status change restrictions

### Pricing Validation
1. Price Rules
   - Minimum price validation
   - Maximum price validation
   - Valid number format
   - Currency formatting

2. Association Rules
   - Valid vehicle type
   - Valid season
   - Unique combination check
   - Status dependencies

## UI/UX Considerations

1. Multi-language Support
   - RTL support for Arabic
   - Language-specific input fields
   - Localized date formats
   - Translation management

2. Interactive Features
   - Dynamic price calculator
   - Date range picker
   - Status toggles
   - Bulk action tools

3. Responsive Design
   - Mobile-friendly forms
   - Adaptive tables
   - Touch-friendly controls
   - Flexible layouts

4. User Experience
   - Form validation feedback
   - Success/error notifications
   - Loading states
   - Progress indicators

## State Management Requirements

1. Season Data
   - Current seasons list
   - Selected season details
   - Season status states
   - Date range states

2. Pricing Data
   - Current pricing matrix
   - Price history
   - Selected pricing details
   - Bulk edit states

3. Form States
   - Edit/Create modes
   - Validation states
   - Loading states
   - Error states

4. UI States
   - Filter states
   - Sort states
   - Pagination states
   - Search states