import { useRef, useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    Modal,
    Row,
    Col,
    Popconfirm,
    Tag,
    Spin,
    Typography,
    Select
} from "antd";
import { DeleteOutlined, EditOutlined, EyeOutlined, ImportOutlined, PlusOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import i18n from "../../i18n/index.ts";
import {
    deleteSocialAffair,
    getSocialAffairsByGovernorate
} from "../../features/admin/socialAffairSlice.ts";
import { getAcademicYearsAll } from "../../features/admin/academicYearSlice.ts";
import SocialAffairForm from "./SocialAffairForm";
import SocialAffairDetails from "./SocialAffairDetails";
import SocialAffairImport from "./SocialAffairImport";

const { Title } = Typography;

interface SocialAffairsListProps {
    governorateId: number;
    governorateName?: string;
}

function SocialAffairsList({ governorateId, governorateName }: SocialAffairsListProps) {
    const { t } = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch: any = useDispatch();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [importMode, setImportMode] = useState(false);
    const [editingSocialAffair, setEditingSocialAffair] = useState<any>(null);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(0);

    const [academicYears, setAcademicYears] = useState<any[]>([]);
    const [loadingAcademicYears, setLoadingAcademicYears] = useState(false);

    // Fetch academic years
    useEffect(() => {
        const fetchAcademicYears = async () => {
            setLoadingAcademicYears(true);
            try {
                const result = await dispatch(getAcademicYearsAll()).unwrap();
                setAcademicYears(result);
            } catch (error) {
                console.error("Failed to fetch academic years:", error);
            } finally {
                setLoadingAcademicYears(false);
            }
        };

        fetchAcademicYears();
    }, [dispatch]);

    /*|--------------------------------------------------------------------------
    | FETCH SOCIAL AFFAIRS BY GOVERNORATE WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetSocialAffairs = (params: any, sort: any, filter: any) => {
        return dispatch(getSocialAffairsByGovernorate({
            governorate_id: governorateId,
            pageNumber,
            perPage: pageSize,
            params,
            sort
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
                return [];
            });
    };

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t("social_affairs.labels.delegation"),
            dataIndex: "delegation",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("social_affairs.labels.nom_parent"),
            dataIndex: "nom_parent",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("social_affairs.labels.cin_parent"),
            dataIndex: "cin_parent",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("social_affairs.labels.telephone"),
            dataIndex: "telephone",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("social_affairs.labels.nom_complet"),
            dataIndex: "nom_complet",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("social_affairs.labels.eleve_etudiant"),
            dataIndex: "eleve_etudiant",
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (eleve_etudiant: boolean) => (
                eleve_etudiant ?
                    <Tag color="success">{t("common.yes")}</Tag> :
                    <Tag color="error">{t("common.no")}</Tag>
            ),
        },
        {
            title: t("social_affairs.labels.societe"),
            dataIndex: "societe",
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (societe: boolean) => (
                societe ?
                    <Tag color="success">{t("common.yes")}</Tag> :
                    <Tag color="error">{t("common.no")}</Tag>
            ),
        },
        {
            title: t("social_affairs.labels.academic_year"),
            dataIndex: "academic_year",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                record.academic_year ?
                    record.academic_year.code :
                    <span className="text-gray-400">{t("common.na")}</span>
            ),
        },
        {
            title: t("social_affairs.labels.actions"),
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("social_affairs.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("social_affairs.yes")}
                        cancelText={t("social_affairs.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingSocialAffair(record);
        setViewMode(true);
        setModalVisible(true);
    };

    const handleEdit = (record: any) => {
        setEditingSocialAffair(record);
        setViewMode(false);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingSocialAffair(null);
        setViewMode(false);
        setModalVisible(true);
    };

    const handleImport = () => {
        setImportMode(true);
    };

    const refreshSocialAffairsData = () => {
        actionRef.current?.reload();
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE SOCIAL AFFAIR
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteSocialAffair(id));
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            refreshSocialAffairsData();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setViewMode(false);
        setImportMode(false);
        setEditingSocialAffair(null);
    };

    return (
        <>
            <div className="mb-4">
                <Title level={4}>
                    {governorateName
                        ? t("social_affairs.title_with_governorate", { governorate: governorateName })
                        : t("social_affairs.title")}
                </Title>
            </div>
            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("social_affairs.list_title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetSocialAffairs(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button
                                key="import"
                                onClick={handleImport}
                                className="btn-import"
                                icon={<ImportOutlined />}
                            >
                                {t("social_affairs.import")}
                            </Button>,
                            <Button
                                key="add"
                                onClick={handleAdd}
                                className="btn-add"
                                icon={<PlusOutlined />}
                            >
                                {t("social_affairs.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            {/* Form Modal */}
            {modalVisible && !viewMode && (
                <SocialAffairForm
                    modalVisible={modalVisible}
                    onCancel={handleReset}
                    editingSocialAffair={editingSocialAffair}
                    governorateId={governorateId}
                    onSuccess={refreshSocialAffairsData}
                />
            )}

            {/* View Modal */}
            {modalVisible && viewMode && (
                <SocialAffairDetails
                    modalVisible={modalVisible}
                    onCancel={handleReset}
                    socialAffair={editingSocialAffair}
                />
            )}

            {/* Import Modal */}
            {importMode && (
                <SocialAffairImport
                    modalVisible={importMode}
                    onCancel={handleReset}
                    governorateId={governorateId}
                    onSuccess={refreshSocialAffairsData}
                />
            )}
        </>
    );
}

export default SocialAffairsList;
