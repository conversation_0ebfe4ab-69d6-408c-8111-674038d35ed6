import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/options';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getOptionsAll: any = createAsyncThunk(
    "getOptionsAll",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-all`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getOptions: any = createAsyncThunk(
    "getOptions",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any,
            sort: any,
            filter: any
        },
        thunkAPI: any
    ) => {
        try {
            const { pageNumber, perPage, params, sort, filter } = data;
            let url: string = `${URL}?page=${pageNumber}&perPage=${perPage}`;

            // Add search parameters
            if (params?.nom_fr) {
                url += `&nom_fr=${encodeURIComponent(params.nom_fr)}`;
            }
            if (params?.nom_en) {
                url += `&nom_en=${encodeURIComponent(params.nom_en)}`;
            }
            if (params?.nom_ar) {
                url += `&nom_ar=${encodeURIComponent(params.nom_ar)}`;
            }

            // Add sorting
            if (sort && Object.keys(sort).length > 0) {
                const sortField = Object.keys(sort)[0];
                const sortOrder = sort[sortField] === 'ascend' ? 'asc' : 'desc';
                url += `&sortBy=${sortField}&sortOrder=${sortOrder}`;
            }

            // Add filters
            if (filter && Object.keys(filter).length > 0) {
                Object.keys(filter).forEach(key => {
                    if (filter[key] && filter[key].length > 0) {
                        url += `&${key}=${filter[key].join(',')}`;
                    }
                });
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeOption: any = createAsyncThunk(
    "storeOption",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateOption: any = createAsyncThunk(
    "updateOption",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${data.id}`;
            const resp: any = await api.put(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteOption: any = createAsyncThunk(
    "deleteOption",
    async (id: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const optionSlice = createSlice({
    name: 'option',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getOptionsAll
            .addCase(getOptionsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getOptionsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getOptionsAll.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getOptions
            .addCase(getOptions.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getOptions.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getOptions.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // storeOption
            .addCase(storeOption.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeOption.fulfilled, (state, action) => {
                state.loading = false;
                state.currentItem = action.payload;
            })
            .addCase(storeOption.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // updateOption
            .addCase(updateOption.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateOption.fulfilled, (state, action) => {
                state.loading = false;
                state.currentItem = action.payload;
            })
            .addCase(updateOption.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // deleteOption
            .addCase(deleteOption.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteOption.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deleteOption.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            });
    }
});

export const { setCurrentItem, clearError } = optionSlice.actions;
export default optionSlice.reducer;
