import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Divider,
    Empty,
    Form, Input,
    Modal,
    Popconfirm,
    Row,
    Select, Spin, Tag,
    Typography
} from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import {CalendarOutlined, CreditCardOutlined, DeleteOutlined, EditOutlined, EyeOutlined} from "@ant-design/icons";
import moment from "moment";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import { getAdminsAll } from "../../features/admin/adminSlice.ts";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { storeAgentCardsAffectation, updateAgentCardsAffectation, getAgentsBySalePoint, deleteAgentCardsAffectation } from "../../features/admin/agentCardsAffectationSlice.ts";
import { getCardTypesAll } from "../../features/admin/cardTypeSlice.ts";
import { getSalesPeriodsAll } from "../../features/admin/salesPeriodsSlice.ts";

const AgentsManager:any = ({record}:any) => {

    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const [agentForm] = Form.useForm();
    const dispatch = useDispatch();
    
    const [agentModal, setAgentModal] = useState(false);
    const [viewAgentMode, setViewAgentMode] = useState(false);
    const [editAgentMode, setEditAgentMode] = useState(false);
    const [selectedTypes, setSelectedTypes] = useState([]);
    const [editingAgent, setEditingAgent] = useState<any>(null);
    const [loading, setLoading] = useState(false);

    const [agents, setAgents] = useState([]);
    const [agentsBySalePoint, setAgentsBySalePoint] = useState([]);
    const [salesPeriods, setSalesPeriods] = useState([]);
    const [cardsTypes, setCardsTypes] = useState([]);
    const [selectedPeriod, setSelectedPeriod] = useState<number | null>(null);

    /*|--------------------------------------------------------------------------
    | FETCH ALL ADMINS, SALES PERIODS AND CARDS TYPES
    |-------------------------------------------------------------------------- */
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                const [adminsResponse, salesPeriodsResponse, cardsTypesResponse] = await Promise.all([
                    dispatch(getAdminsAll()).unwrap(),
                    dispatch(getSalesPeriodsAll()).unwrap(),
                    dispatch(getCardTypesAll()).unwrap(),
                ]);

                setAgents(adminsResponse?.data || []);
                setSalesPeriods(salesPeriodsResponse?.data || []);
                setCardsTypes(cardsTypesResponse?.data || []);
            } catch (error) {
                console.error('Error fetching data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const fetchSalePointAgents = async () => {
        if (!selectedPeriod || !record?.id) {
            return;
        }
        setLoading(true);
        try {
            const response = await dispatch(getAgentsBySalePoint({
                salePointId: record.id,
                salePeriodId: selectedPeriod
            })).unwrap();

            if (response?.data) {
                setAgentsBySalePoint(response.data);
            }
        } catch (error) {
            console.error('Error fetching sale point agents:', error);
            toast.error(t("common.errors.unexpected"));
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        fetchSalePointAgents();
    }, [selectedPeriod, record?.id, dispatch]);

    /*|--------------------------------------------------------------------------
    | HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleEdit = (record: any) => {
        setEditingAgent(record);
        setEditAgentMode(true);
        setViewAgentMode(false);
        
        const types = record.card_types.map((ct: any) => ct.card_type.id);
        setSelectedTypes(types);

        const formValues = {
            id_agent: record.id_agent,
            id_sale_period: record.id_sale_period,
            cardTypes: types,
            ranges: record.card_types.reduce((acc: any, ct: any) => ({
                ...acc,
                [ct.card_type.id]: {
                    start_serial_number: ct.start_serial_number,
                    end_serial_number: ct.end_serial_number
                }
            }), {})
        };

        agentForm.setFieldsValue(formValues);
        setAgentModal(true);
    };

    const handleView = (record: any) => {
        setEditingAgent(record);
        setViewAgentMode(true);
        setEditAgentMode(false);

        const types = record.card_types.map((ct: any) => ct.card_type.id);
        setSelectedTypes(types);

        const formValues = {
            id_agent: record.id_agent,
            id_sale_period: record.id_sale_period,
            cardTypes: types,
            ranges: record.card_types.reduce((acc: any, ct: any) => ({
                ...acc,
                [ct.card_type.id]: {
                    start_serial_number: ct.start_serial_number,
                    end_serial_number: ct.end_serial_number
                }
            }), {})
        };

        agentForm.setFieldsValue(formValues);
        setAgentModal(true);
    };
    const handleAddAgent = () => {
        agentForm.resetFields();
        setAgentModal(true);
    };
    const handlePeriodChange = (periodId: number) => {
        setSelectedPeriod(periodId);
    };
    
    /*|--------------------------------------------------------------------------
    | HANDLE SUBMIT
    |-------------------------------------------------------------------------- */
    const handleSubmitForm = async (values: any) => {
        setLoading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            const transformedValues = {
                ...values,
                id_sale_point: record.id,
                ranges: Object.entries(values.ranges || {}).map(([cardTypeId, range]:any) => ({
                    cardType: parseInt(cardTypeId),
                    ...range
                }))
            };

            const payload = editAgentMode 
                ? { id: editingAgent.id, ...transformedValues } 
                : transformedValues;

            if (editAgentMode) {
                await dispatch(updateAgentCardsAffectation(payload)).unwrap();
            } else {
                await dispatch(storeAgentCardsAffectation(transformedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            handleReset();
            await fetchSalePointAgents();
        } catch (error) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmAction = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_salesPoints.confirmAction"),
            content: t("manage_salesPoints.confirmUpdate"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleSubmitForm(values);
            },
            centered: true,
        });
    };


   /*|--------------------------------------------------------------------------
   |  - HANDLE DELETE
   |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteAgentCardsAffectation(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            await fetchSalePointAgents();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setEditingAgent(null);
        setSelectedTypes([]);
        setViewAgentMode(false);
        setEditAgentMode(false);
        agentForm.resetFields();
        setAgentModal(false);
    };

    return (
        <>
            <div className="expanded-row-container">
                <Spin 
                    spinning={loading}
                    indicator={
                        <LoadingOutlined
                            style={{
                                fontSize: 24,
                                color: 'var(--primary-color)'
                            }}
                            spin
                        />
                    }
                >
                    <div className="mb-4">
                        <Row gutter={[16, 16]} align="middle" justify="space-between">
                            <Col>
                            <div className="mb-4 flex items-center justify-between">
                                <Typography.Title level={5} className="!mb-0">
                                    {t("manage_salesPoints.labels.agents")}
                                </Typography.Title>
                                {record?.agents?.length > 0 && (
                                    <Button type="dashed" onClick={() => handleAddAgent()}>
                                        {t("manage_salesPoints.addAgent")}
                                    </Button>
                                )}
                            </div>
                            </Col>
                            <Col>
                                <Select
                                    placeholder={t("manage_salesPoints.selectPeriod")}
                                    style={{ width: 200, marginRight: 16 }}
                                    onChange={handlePeriodChange}
                                    value={selectedPeriod}
                                >
                                    {salesPeriods?.map((period: any) => (
                                        <Select.Option key={period.id} value={period.id}>
                                            {period[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                                <Button 
                                    type="dashed" 
                                    onClick={() => handleAddAgent()}
                                >
                                    {t("manage_salesPoints.addAgent")}
                                </Button>
                            </Col>
                        </Row>
                    </div>

                    {agentsBySalePoint?.length > 0 ? (
                        <Row gutter={[24, 24]} className="responsive-grid">
                            {agentsBySalePoint?.map((agentAffectation: any) => (
                                <Col xs={24} md={12} lg={8} xl={8} key={agentAffectation.id}>
                                    <Card
                                        className="cursor-pointer custom-card hover:shadow-lg transition-all duration-300"
                                        style={{
                                            borderRadius: '12px',
                                            overflow: 'hidden',
                                        }}
                                        actions={[
                                            <Button
                                                onClick={() => handleView(agentAffectation)}
                                                shape="circle"
                                                type="text"
                                                icon={<EyeOutlined/>}
                                                className="hover:opacity-100 transition-opacity"
                                            />,
                                            <Button
                                                onClick={() => handleEdit(agentAffectation)}
                                                shape="circle"
                                                type="text"
                                                icon={<EditOutlined/>}
                                                className="hover:opacity-100 transition-opacity"
                                            />,
                                            <Popconfirm
                                                title={t("manage_salesPoints.confirmDeleteAgent")}
                                                onConfirm={() => handleDelete(agentAffectation.id)}
                                                okText={t("manage_salesPoints.yes")}
                                                cancelText={t("manage_salesPoints.no")}
                                            >
                                                <Button
                                                    shape="circle"
                                                    type="text"
                                                    icon={<DeleteOutlined/>}
                                                    className="hover:opacity-100 transition-opacity"
                                                />
                                            </Popconfirm>,
                                        ]}
                                    >
                                        <Card.Meta
                                            title={
                                                <div className="flex items-center gap-3 pb-3 border-b border-dashed border-gray-200">
                                                    <Badge
                                                        dot
                                                        color={"var(--secondary-color)"}
                                                        style={{
                                                            top: '5px',
                                                            right: '5px',
                                                        }}
                                                    >
                                                        <Avatar
                                                            src={`https://ui-avatars.com/api/?name=${agentAffectation.agent.firstname}+${agentAffectation.agent.lastname}&background=5CB7BA&color=fff`}
                                                            size={48}
                                                        />
                                                    </Badge>
                                                    <div>
                                                        <h4 className="font-bold text-base mb-0">
                                                            {agentAffectation.agent.firstname} {agentAffectation.agent.lastname}
                                                        </h4>
                                                        <span className="text-xs text-gray-500">
                                                            CIN : {agentAffectation.agent.cin}
                                                        </span>
                                                    </div>
                                                </div>
                                            }
                                            description={
                                                <div className="pt-4">
                                                    <div className="mb-3">
                                                        <div className="flex items-center gap-2 text-sm">
                                                            <CalendarOutlined className="text-gray-400"/>
                                                            <span className="text-gray-700">
                                                                {agentAffectation.sale_period[`nom_${currentLang}`]}
                                                            </span>
                                                        </div>
                                                        <div className="text-xs text-gray-500 ml-6 mt-1">
                                                            {moment(agentAffectation.sale_period.date_start).format('DD/MM/YYYY')}
                                                            {" - "}
                                                            {moment(agentAffectation.sale_period.date_end).format('DD/MM/YYYY')}
                                                        </div>
                                                    </div>

                                                    {/* Card Types Section */}
                                                    <div style={{backgroundColor: "var(--secondary-color_light)"}}
                                                         className="flex justify-center items-center gap-2 rounded-full py-2 mt-2">
                                                        <CreditCardOutlined style={{color: "var(--secondary-color)"}}/>
                                                        <span style={{color: "var(--secondary-color)"}}
                                                              className="font-semibold">
                                                            {agentAffectation.card_types.reduce((total: number, ct: any) => 
                                                                total + (ct.end_serial_number - ct.start_serial_number + 1), 0)}
                                                        </span>
                                                        <span style={{color: "var(--secondary-color)"}}
                                                              className="text-xs">
                                                            {t("manage_salesPoints.assignedCards")}
                                                        </span>
                                                    </div>
                                                </div>
                                            }
                                        />
                                    </Card>
                                </Col>
                            ))}
                        </Row>
                    ) : (
                        <Empty
                            description={
                                <Typography.Text type="secondary">
                                    {selectedPeriod 
                                        ? t("manage_salesPoints.noAgents")
                                        : t("manage_salesPoints.selectPeriodFirst")}
                                </Typography.Text>
                            }
                        />
                    )}
                </Spin>
            </div>

            <Modal
                width={800}
                title={
                    viewAgentMode
                        ? t("manage_salesPoints.detailsAgent")
                        : editAgentMode
                            ? t("manage_salesPoints.editAgent")
                            : t("manage_salesPoints.addAgent")
                }
                open={agentModal}
                onCancel={() => handleReset()}
                onOk={() => (viewAgentMode ? setAgentModal(false) : agentForm.submit())}
                okText={viewAgentMode ? null : t("manage_assignAgents.save")}
                footer={viewAgentMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={agentForm}
                    layout="vertical"
                    onFinish={confirmAction}
                >
                    <Row gutter={[16,16]}>
                        <Col xs={24}>
                            <Form.Item
                                label={t("manage_assignAgents.labels.agent")}
                                name="id_agent"
                                rules={[{ required: true, message: t("manage_assignAgents.errors.agentRequired") }]}
                            >
                                <Select 
                                    placeholder={t("manage_assignAgents.placeholders.agent")} 
                                    disabled={viewAgentMode}
                                >
                                    {agents?.map((el:any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el.firstname} {el.lastname}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24}>
                            <Form.Item
                                label={t("manage_assignAgents.labels.salesPeriod")}
                                name="id_sale_period"
                                rules={[{ required: true, message: t("manage_assignAgents.errors.salesPeriodRequired") }]}
                            >
                                <Select 
                                    placeholder={t("manage_assignAgents.placeholders.salesPeriod")} 
                                    disabled={viewAgentMode}
                                >
                                    {salesPeriods?.map((el:any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16, 16]}>
                        <Col xs={24}>
                            <Form.Item
                                label={t("manage_assignAgents.labels.cardTypes")}
                                name="cardTypes"
                                rules={[{ required: true, message: t("manage_assignAgents.errors.cardTypesRequired") }]}
                            >
                                <Select 
                                    placeholder={t("manage_assignAgents.placeholders.cardTypes")} 
                                    disabled={viewAgentMode}
                                    mode="multiple"
                                    onChange={(values) => {
                                        setSelectedTypes(values);
                                        agentForm.setFieldValue('ranges', {});
                                    }}
                                >
                                    {cardsTypes?.map((cardType: any) => (
                                        <Select.Option key={cardType.id} value={cardType.id}>
                                            {cardType[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    {selectedTypes.map((cardTypeId: number) => {
                        const cardType = cardsTypes?.find((ct:any) => ct.id === cardTypeId);
                        return (
                            <div key={cardTypeId}>
                                <Divider>
                                    <Tag className="!border shadow-sm px-12 !text-black font-semibold py-1"
                                         color={"#FFF"}>
                                        {cardType?.[`nom_${currentLang}`]}
                                    </Tag>
                                </Divider>
                                <Row gutter={[16, 16]}>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.startNumber")}
                                            name={["ranges", cardTypeId.toString(), "start_serial_number"]}
                                            rules={[{ required: true, message: t("manage_assignAgents.errors.startNumberRequired") }]}
                                        >
                                            <Input 
                                                disabled={viewAgentMode} 
                                                type="number" 
                                                placeholder={t("manage_assignAgents.placeholders.startNumber")} 
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} sm={12}>
                                        <Form.Item
                                            label={t("manage_assignAgents.labels.endNumber")}
                                            name={["ranges", cardTypeId.toString(), "end_serial_number"]}
                                            //rules={[{ required: true, message: t("manage_assignAgents.errors.endNumberRequired") }]}
                                            rules={[
                                                { required: true, message: t("manage_assignAgents.errors.endNumberRequired") },
                                                ({ getFieldValue }) => ({
                                                    validator(_, value) {
                                                        const startValue = getFieldValue(["ranges", cardTypeId.toString(), "start_serial_number"]);
                                                        if (!value || !startValue) {
                                                            return Promise.resolve();
                                                        }
                                                        if (Number(value) > Number(startValue)) {
                                                            return Promise.resolve();
                                                        }
                                                        return Promise.reject(
                                                            new Error(t("manage_assignAgents.errors.endGreaterThanStart"))
                                                        );
                                                    }
                                                })
                                            ]}
                                        >
                                            <Input 
                                                disabled={viewAgentMode} 
                                                type="number" 
                                                placeholder={t("manage_assignAgents.placeholders.endNumber")} 
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </div>
                        );
                    })}
                </Form>
            </Modal>
        </>
    )
}

export default AgentsManager;
