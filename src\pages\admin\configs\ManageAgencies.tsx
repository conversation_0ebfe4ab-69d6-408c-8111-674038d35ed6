import { useEffect, useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import { useDispatch } from "react-redux";
import { deleteAgency, getAgencies, storeAgency, updateAgency } from "../../../features/admin/agencySlice.ts";
import { toast } from "react-toastify";
import { getGovernorateAll } from "../../../features/admin/governorateSlice.ts";
import { getDelegationsByGovernorate } from "../../../features/admin/delegationSlice.ts";
import { AgencySalesPointsManager } from "../../../components/index.ts";
import { useSelector } from "react-redux";
import { hasPermission } from "../../../helpers/permissions.ts";


function ManageAgencies() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch:any = useDispatch<any>();

    const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<number | null>(null);
    const [filteredDelegations, setFilteredDelegations] = useState<any[]>([]);
    const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingAgency, setEditingAgency] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    const governorates = useSelector((state: any) => state.governorate.items.data);


    /*|--------------------------------------------------------------------------
    |  - FETCH ALL AGENCIES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
   const handleGetAgencies = (params: any, sort: any, filter: any) => {
           return dispatch(getAgencies({
               pageNumber,
               perPage: pageSize,
               params, sort, filter
           }))
               .unwrap()
               .then((originalPromiseResult: any) => {
                   setTotal(originalPromiseResult.meta.total);
                   return originalPromiseResult.data;
               })
               .catch((rejectedValueOrSerializedError: any) => {
                   console.log(rejectedValueOrSerializedError);
               });
       }

    const fetchStoreData = async () => {
            try {
                setLoading
                const promises = [];
                if(!governorates?.length){
                    promises.push(dispatch(getGovernorateAll()).unwrap());
                }
               
                await Promise.all(promises);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        }
    useEffect(() => {
        setLoading(true);
        fetchStoreData();
    }, []);
    
       

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_agencies.labels.id")}`,
            dataIndex: "id",
            search: false,
            width: 60,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_agencies.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_agencies.labels.code")}`,
            dataIndex: "code",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.code,
        },
        {
            title: `${t("manage_agencies.labels.governorate")}`,
            dataIndex: "id_governorate",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.governorate?.[`nom_${currentLang}`] || '-',
            renderFormItem: (schema: any, config: any, form: any) => {
                return (
                    <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    placeholder={t("manage_agencies.filters.governorate")}
                    options={governorates?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    onChange={async (value) => {
                        form.setFieldValue("id_governorate", value);
                        await handleGovernorateChange(value);
                    }}
                />
                )
            }
        },
        {
            title: `${t("manage_agencies.labels.delegation")}`,
            dataIndex: "id_delegation", 
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.delegation?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    loading={isDelegationsLoading}
                    placeholder={t("manage_agencies.filters.delegation")}
                    options={filteredDelegations?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    disabled={!selectedFilterGovernorate}
                    className="w-full"
                    notFoundContent={
                        isDelegationsLoading ? (
                            <div className="flex items-center justify-center py-2">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                            </div>
                        ) : (
                            <div className="text-center py-2 text-gray-500">
                                {!selectedFilterGovernorate 
                                    ? t("manage_agencies.selectGovernorate") 
                                    : t("common.noData")}
                            </div>
                        )
                    }
                />
            ),
        },
        {
            title: `${t("manage_agencies.labels.address")}`,
            dataIndex: "address",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.address,
        },
        {
            title: `${t("manage_agencies.labels.contact")}`,
            dataIndex: "contact",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => (
                <div className="flex items-center gap-1 font-medium">
                    <span className="text-primary-color">+216</span>
                    <span className="text-gray-400">|</span>
                    <span>{data.contact}</span>
                </div>
            ),
        },
        {
            title: `${t("manage_agencies.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 100,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_agencies.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_agencies") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_agencies") && (
                            <Popconfirm
                                title={t("manage_agencies.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_agencies.yes")}
                                cancelText={t("manage_agencies.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.sales")}</Link>,
        },
        {
            title: t("manage_agencies.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = async (record: any) => {
        setEditingAgency(record);
        if (record.governorate) {
            await handleGovernorateChange(record.governorate.id);
        }

        form.setFieldsValue({
            id_governorate: record.governorate?.id,
            id_delegation: record.delegation?.id,
            ...record
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = async (record: any) => {
        setEditingAgency(record);
      
        if (record.governorate) {
            await handleGovernorateChange(record.governorate.id);
        }

        form.setFieldsValue({
            id_governorate: record.governorate?.id,
            id_delegation: record.delegation?.id,
            ...record
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingAgency(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };
    const handleGovernorateChange: any = async (governorateId: number | null) => {
            form.setFieldsValue({ id_delegation: null });
            setSelectedFilterGovernorate(governorateId);
            setIsDelegationsLoading(true);
            setFilteredDelegations([]);
            if (!governorateId) {
                setFilteredDelegations([]);
                setIsDelegationsLoading(false);
                return;
            }
            try {
                const response:any = await dispatch(getDelegationsByGovernorate(governorateId)).unwrap();
                setFilteredDelegations(response.data || []);
            } catch (error) {
                console.error('Error fetching delegations:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setIsDelegationsLoading(false);
            }
        };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE AGENCY
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
            setLoading(true);
            const payload = editingAgency ? { id: editingAgency.id, ...values } : values;
            const toastId = toast.loading(t("messages.loading"), {
                position: 'top-center',
            });
            try {
                if (editingAgency) {
                    await dispatch(updateAgency(payload)).unwrap();
                } else {
                    await dispatch(storeAgency(values)).unwrap();
                }
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
                actionRef.current?.reload();
                handleReset();
            } catch (error: any) {
                const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                    name: field,
                    errors: [error.errors[field][0]],
                }));
                form.setFields(fieldErrors);
                toast.update(toastId, {
                    render: t("messages.error"),
                    type: "error",
                    isLoading: false,
                    autoClose: 3000
                });
            } finally {
                setLoading(false);
            }
        };
        const confirmSubmit = (values: any) => {
            const modal = Modal.confirm({
                title: t("manage_agencies.confirmAction"),
                content: editingAgency
                    ? t("manage_agencies.confirmUpdate")
                    : t("manage_agencies.confirmAdd"),
                okText: t("common.yes"),
                cancelText: t("common.no"),
                onOk: async () => {
                    modal.destroy();
                    await handleFormSubmit(values);
                },
                centered: true,
            });
        };

    /*|--------------------------------------------------------------------------
    |  - DELETE AGENCY
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
            const toastId = toast.loading(t("messages.loading"), {
                position: 'top-center',
            });
            try {
                await dispatch(deleteAgency(id)).unwrap();
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
                actionRef.current?.reload();
            } catch (error: any) {
                toast.update(toastId, {
                    render: t("messages.error"),
                    type: "error",
                    isLoading: false,
                    autoClose: 3000
                });
            }
        };

    /*|--------------------------------------------------------------------------
     |  - HANDLE RESET
     |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_agencies.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        expandable={{
                            expandedRowRender: (record: any) => (
                                <AgencySalesPointsManager agencyId={record.id} />
                            ),
                        }}
                        rowKey={"id"}
                        request={async (params: any, sort: any, filter: any) => {
                            console.log(params);
                            setLoading(true);
                            const dataFilter: any = await handleGetAgencies(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            hasPermission("create_agencies") && (
                                <Button key="button" onClick={handleAdd} className="btn-add">
                                    {t("manage_agencies.add")}
                                </Button>
                            ),
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_agencies.details")
                        : editingAgency
                            ? t("manage_agencies.edit")
                            : t("manage_agencies.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_agencies.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >

                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_agencies.labels.code")}
                                name="code"
                                rules={[{ required: true, message: t("manage_agencies.errors.codeRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_agencies.placeholders.code")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_agencies.labels.contact")}
                                name="contact"
                                rules={[
                                    { required: true, message: t("manage_agencies.errors.contactRequired") },
                                    { pattern: /^\d{8}$/, message: t("manage_agencies.errors.contactInvalid") }
                                ]}
                            >
                                <Input
                                    type="number"
                                    placeholder={t("manage_agencies.placeholders.contact")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_agencies.labels.address")}
                                name="address"
                                rules={[{ required: true, message: t("manage_agencies.errors.addressRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_agencies.placeholders.address")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_governorate"
                                label={t("manage_agencies.labels.governorate")}
                                rules={[{ required: true, message: t("manage_agencies.errors.governorateRequired") }]}
                            >
                                <Select
                                    disabled={viewMode || loading}
                                    placeholder={t("manage_agencies.placeholders.governorate")}
                                    onChange={handleGovernorateChange}
                                    options={governorates?.map((gov: any) => ({
                                        label: gov[`nom_${currentLang}`],
                                        value: gov.id,
                                    }))}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_delegation"
                                label={t("manage_agencies.labels.delegation")}
                                rules={[{ required: true, message: t("manage_agencies.errors.delegationRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_agencies.placeholders.delegation")}
                                    options={filteredDelegations.map((del: any) => ({
                                        label: del[`nom_${currentLang}`],
                                        value: del.id,
                                    }))}
                                    notFoundContent={
                                        isDelegationsLoading ? (
                                            <div className="flex items-center justify-center py-2">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                            </div>
                                        ) : (
                                            <div className="text-center py-2 text-gray-500">
                                                {!selectedFilterGovernorate
                                                    ? t("manage_agencies.selectGovernorate")
                                                    : t("common.noData")}
                                            </div>
                                        )
                                    }
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageAgencies;
