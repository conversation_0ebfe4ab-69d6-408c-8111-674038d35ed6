import {useEffect, useRef, useState} from "react";
import { useDispatch } from "react-redux";

import {
    getSalesPeriods,
    createSalesPeriod,
    updateSalesPeriod,
    deleteSalesPeriod
} from "../../../features/admin/salesPeriodsSlice";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    message,
    Breadcrumb,
    Row,
    Col,
    DatePicker,
    Tag,
    Select,
    Divider,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import moment from "moment";
import {getCampaignsAll} from "../../../features/admin/campaignSlice.ts";
import {toast} from "react-toastify";
import { hasPermission } from "../../../helpers/permissions.ts";
import { useSelector } from "react-redux";
import { getPeriodicityAll } from "../../../features/admin/periodicitySlice.ts";
import dayjs from "dayjs";

function ManageSalesPeriods() {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const dispatch = useDispatch<any>();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingSalesPeriod, setEditingSalesPeriod] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    const periodicities = useSelector((state: any) => state.periodicity.items.data);
    const campaigns = useSelector((state: any) => state.campaign.items.data);


    const fetchStoreData = async () => {
            try {
                setLoading(true);
    
                const promises = [];
                
                if (!periodicities?.length) {
                    promises.push(dispatch(getPeriodicityAll()).unwrap());
                }

                promises.push(dispatch(getCampaignsAll()).unwrap());
                
                await Promise.all(promises);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        }
    
        useEffect(() => {
            fetchStoreData();
        }, []);

    /*|--------------------------------------------------------------------------
   | FETCH ALL SALESPERIODS WITH PAGINATION & FILTER
   |-------------------------------------------------------------------------- */
    const handleGetSalesPeriods = (params: any, sort: any, filter: any) => {
        return dispatch(
            getSalesPeriods({
                pageNumber,
                perPage: pageSize,
                params, sort, filter
            })
        )
            .unwrap()
            .then(async (originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
    };


    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_salesPeriods.labels.id")}`,
            dataIndex: "id",
            search: false,
            width: 40,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_salesPeriods.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_salesPeriods.labels.campaign")}`,
            dataIndex: "campaign",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => {
                return record.campaign ? (
                    <Tag color={record.campaign.color || "default"}>
                        {record.campaign[`nom_${currentLang}`]}
                    </Tag>
                ) : "-";
            },
            valueType: "select",
            fieldProps: {
                showSearch: true,
                placeholder: t("manage_salesPeriods.placeholders.campaign"),
                options: campaigns?.map((campaign: any) => ({
                    label: campaign[`nom_${currentLang}`],
                    value: campaign.id
                })),
                filterOption: (input: string, option?: { label: string; value: number }) =>
                    option?.label.toLowerCase().includes(input.toLowerCase()) ?? false
            }
        },
        {
            title: t("manage_salesPeriods.labels.periodClient"),
            dataIndex: "date_start_client",
            render: (_: any, record: any) => {
                const isOutDate = dayjs(record.date_end_client).isBefore(dayjs(), 'day');
                return (
                    <Tag color={isOutDate ? "error" : "default"}>
                        {dayjs(record.date_start_client).format("DD MMM YYYY")} - {dayjs(record.date_end_client).format("DD MMM YYYY")}
                    </Tag>
                )
            },
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("manage_salesPeriods.labels.periodAgent"),
            dataIndex: "date_start_agent",
            render: (_: any, record: any) => {
                const isOutDate = dayjs(record.date_end_agent).isBefore(dayjs(), 'day');
                return (
                    <Tag color={isOutDate ? "error" : "default"}>
                        {dayjs(record.date_start_agent).format("DD MMM YYYY")} - {dayjs(record.date_end_agent).format("DD MMM YYYY")}
                    </Tag>
                )
            },
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },  
        {
            title: t("manage_salesPeriods.labels.periodValidity"),
            dataIndex: "date_start_validity",
            render: (_: any, record: any) => {
                const isOutDate = dayjs(record.date_end_validity).isBefore(dayjs(), 'day');
                return (
                    <Tag color={isOutDate ? "error" : "default"}>
                        {dayjs(record.date_start_validity).format("DD MMM YYYY")} - {dayjs(record.date_end_validity).format("DD MMM YYYY")}
                    </Tag>
                )
            },
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("manage_salesPeriods.labels.periodicity"),
            dataIndex: "periodicity",
            render: (_: any, record: any) => (
                <span>{record.periodicity ? record.periodicity[`nom_${currentLang}`] : "-"}</span>
            ),
            valueType: "select",
            fieldProps: {
                showSearch: true,
                placeholder: t("manage_salesPeriods.placeholders.periodicity"),
                options: periodicities?.map((periodicity: any) => ({
                    label: periodicity[`nom_${currentLang}`],
                    value: periodicity.id
                })),
                filterOption: (input: string, option?: { label: string; value: number }) =>
                    option?.label.toLowerCase().includes(input.toLowerCase()) ?? false
            }
        },
        {
            title: `${t("manage_salesPeriods.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_sales_periods") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_sales_periods") && (
                            <Popconfirm
                                title={t("manage_salesPeriods.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_salesPeriods.yes")}
                                cancelText={t("manage_salesPeriods.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.sales")}</Link>,
        },
        {
            title: t("manage_salesPeriods.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingSalesPeriod(record);
        console.log(record)
        form.setFieldsValue({
            ...record,
            id_campaign:record.campaign.id,
            date_start_client: record.date_start_client ? dayjs(record.date_start_client) : null,
            date_end_client: record.date_end_client ? dayjs(record.date_end_client) : null, 
            date_start_agent: record.date_start_agent ? dayjs(record.date_start_agent) : null,
            date_end_agent: record.date_end_agent ? dayjs(record.date_end_agent) : null,
            date_start_validity: record.date_start_validity ? dayjs(record.date_start_validity) : null,
            date_end_validity: record.date_end_validity ? dayjs(record.date_end_validity) : null,
            status: record.status,
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        console.log(record);
        
        setEditingSalesPeriod(record);
        form.setFieldsValue({
            ...record,
            id_campaign:record.campaign.id,
            date_start_client: record.date_start_client ? dayjs(record.date_start_client) : null,
            date_end_client: record.date_end_client ? dayjs(record.date_end_client) : null, 
            date_start_agent: record.date_start_agent ? dayjs(record.date_start_agent) : null,
            date_end_agent: record.date_end_agent ? dayjs(record.date_end_agent) : null,
            date_start_validity: record.date_start_validity ? dayjs(record.date_start_validity) : null,
            date_end_validity: record.date_end_validity ? dayjs(record.date_end_validity) : null,
            status: record.status,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingSalesPeriod(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
     | HANDLE SUBMIT
     |-------------------------------------------------------------------------- */
    const handleSalesPeriodSubmit = async (values: any) => {
        setLoading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            const formattedValues = {
                nom_fr: values.nom_fr,
                nom_en: values.nom_en,
                nom_ar: values.nom_ar,
                date_start_client: dayjs(values.date_start_client).format("YYYY-MM-DD"),
                date_end_client: dayjs(values.date_end_client).format("YYYY-MM-DD"),
                date_start_agent: dayjs(values.date_start_agent).format("YYYY-MM-DD"),
                date_end_agent: dayjs(values.date_end_agent).format("YYYY-MM-DD"),
                date_start_validity: dayjs(values.date_start_validity).format("YYYY-MM-DD"),
                date_end_validity: dayjs(values.date_end_validity).format("YYYY-MM-DD"),
                id_periodicity: values.id_periodicity,
                id_campaign: values.id_campaign,
                id_abn_type: values.id_abn_type,
                status: values.status ?? false
            };

            if (editingSalesPeriod) {
                await dispatch(updateSalesPeriod({
                    id: editingSalesPeriod.id,
                    ...formattedValues
                })).unwrap();
            } else {
                await dispatch(createSalesPeriod(formattedValues)).unwrap();
            }

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });

            handleReset();
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages." + error.message) || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_salesPeriods.confirmAction"),
            content: editingSalesPeriod
                ? t("manage_salesPeriods.confirmUpdate")
                : t("manage_salesPeriods.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleSalesPeriodSubmit(values);
            },
            centered: true,
        });
    }

    /*|--------------------------------------------------------------------------
    | - DELETE SALES PERIOD
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        try {
            await dispatch(deleteSalesPeriod(id)).unwrap();
            message.success(t("messages.deleteSuccess"));
            actionRef.current?.reload();
        } catch (error: any) {
            message.error(error.message || t("messages.error"));
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_salesPeriods.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetSalesPeriods(
                                params,
                                sort,
                                filter
                            );
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{ x: 800 }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_salesPeriods.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1200}
                title={
                    viewMode
                        ? t("manage_salesPeriods.details")
                        : editingSalesPeriod
                            ? t("manage_salesPeriods.edit")
                            : t("manage_salesPeriods.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_salesPeriods.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.name_fr")}
                                name="nom_fr"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_salesPeriods.errors.nameFrRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_salesPeriods.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.name_en")}
                                name="nom_en"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_salesPeriods.errors.nameEnRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_salesPeriods.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.name_ar")}
                                name="nom_ar"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_salesPeriods.errors.nameArRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_salesPeriods.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.campaign")}
                                name="id_campaign"
                                rules={[{ required: true, message: t("manage_salesPeriods.errors.campaignRequired") }]}
                            >
                                <Select placeholder={t("manage_salesPeriods.placeholders.campaign")} disabled={viewMode}>
                                    {campaigns?.map((el:any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.periodicity")}
                                name="id_periodicity"
                                rules={[{ required: true, message: t("manage_salesPeriods.errors.periodicityRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_salesPeriods.placeholders.periodicity")}
                                >
                                    {periodicities?.map((el :any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            <div>
                                                {el[`nom_${currentLang}`]}
                                            </div>
                                        </Select.Option>
                                    ))} 
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    {!viewMode ? (
                        <>
                            <Row gutter={16}>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.startDateClient")}
                                        name="date_start_client"
                                        rules={[{ required: true, message: t("manage_salesPeriods.errors.startDateClientRequired") }]}
                                    >
                                        <DatePicker
                                            className="w-full"
                                            format="YYYY-MM-DD"
                                            disabled={viewMode}
                                            placeholder={t("manage_salesPeriods.placeholders.startDateClient")}
                                            onChange={(date) => {
                                                if (moment.isMoment(date)) {
                                                    form.setFieldsValue({ start_date_client: date.format("YYYY-MM-DD") });
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.endDateClient")}
                                        name="date_end_client"
                                        rules={[{ required: true, message: t("manage_salesPeriods.errors.endDateClientRequired") }]}
                                    >
                                        <DatePicker
                                            className="w-full"
                                            format="YYYY-MM-DD"
                                            disabled={viewMode}
                                            placeholder={t("manage_salesPeriods.placeholders.endDateClient")}
                                            onChange={(date) => {
                                                if (moment.isMoment(date)) {
                                                    form.setFieldsValue({ end_date_client: date.format("YYYY-MM-DD") });
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.startDateAgent")}
                                        name="date_start_agent"
                                        rules={[{ required: true, message: t("manage_salesPeriods.errors.startDateAgentRequired") }]}
                                    >
                                        <DatePicker
                                            className="w-full"
                                            format="YYYY-MM-DD"
                                            disabled={viewMode}
                                            placeholder={t("manage_salesPeriods.placeholders.startDateAgent")}
                                            onChange={(date) => {
                                                if (moment.isMoment(date)) {
                                                    form.setFieldsValue({ start_date_agent: date.format("YYYY-MM-DD") });
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.endDateAgent")}
                                        name="date_end_agent"
                                        rules={[{ required: true, message: t("manage_salesPeriods.errors.endDateAgentRequired") }]}
                                    >
                                        <DatePicker
                                            className="w-full"
                                            format="YYYY-MM-DD"
                                            disabled={viewMode}
                                            placeholder={t("manage_salesPeriods.placeholders.endDateAgent")}
                                            onChange={(date) => {
                                                if (moment.isMoment(date)) {
                                                    form.setFieldsValue({ end_date_agent: date.format("YYYY-MM-DD") });
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.startDateValidity")}
                                        name="date_start_validity"
                                        rules={[{ required: true, message: t("manage_salesPeriods.errors.startDateValidityRequired") }]}
                                    >
                                        <DatePicker
                                            className="w-full"
                                            format="YYYY-MM-DD"
                                            disabled={viewMode}
                                            placeholder={t("manage_salesPeriods.placeholders.startDateValidity")}
                                            onChange={(date) => {
                                                if (moment.isMoment(date)) {
                                                    form.setFieldsValue({ start_date: date.format("YYYY-MM-DD") });
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.endDateValidity")}
                                        name="date_end_validity"
                                        rules={[{ required: true, message: t("manage_salesPeriods.errors.endDateValidityRequired") }]}
                                    >
                                        <DatePicker
                                            className="w-full"
                                            format="YYYY-MM-DD"
                                            disabled={viewMode}
                                            placeholder={t("manage_salesPeriods.placeholders.endDateValidity")}
                                            onChange={(date) => {
                                                if (moment.isMoment(date)) {
                                                    form.setFieldsValue({ end_date: date.format("YYYY-MM-DD") });
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </>
                    ) : (
                        <>
                            <Row gutter={16}>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.startDateClient")}
                                        name="date_start_client"
                                    >
                                        <Input
                                            value={dayjs(form.getFieldValue('date_start_client')).format("YYYY-MM-DD")}
                                            disabled
                                        />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.endDateClient")}
                                        name="date_end_client"
                                    >
                                        <Input
                                            value={dayjs(form.getFieldValue('date_end_client')).format("YYYY-MM-DD")}
                                            disabled
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Divider />
                            <Row gutter={16}>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.startDateAgent")}
                                        name="date_start_agent"
                                    >
                                        <Input
                                            value={dayjs(form.getFieldValue('date_start_agent')).format("YYYY-MM-DD")}
                                            disabled
                                        />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.endDateAgent")}
                                        name="date_end_agent"
                                    >
                                        <Input
                                            value={dayjs(form.getFieldValue('date_end_agent')).format("YYYY-MM-DD")}
                                            disabled
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Divider />
                            <Row gutter={16}>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.startDateValidity")}
                                        name="date_start_validity"
                                    >
                                        <Input
                                            value={dayjs(form.getFieldValue('date_start_validity')).format("YYYY-MM-DD")}
                                            disabled
                                        />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        label={t("manage_salesPeriods.labels.endDateValidity")}
                                        name="date_end_validity"
                                    >
                                        <Input
                                            value={dayjs(form.getFieldValue('date_end_validity')).format("YYYY-MM-DD")}
                                            disabled
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </>
                    )}
                </Form>
            </Modal>
        </>
    );
}

export default ManageSalesPeriods;
