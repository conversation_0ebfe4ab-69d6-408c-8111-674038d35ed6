import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {useDispatch} from "react-redux";
import {
    deleteCardType,
    getCardTypes,
    storeCardType,
    updateCardType
} from "../../../features/admin/cardTypeSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageCardTypes() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const dispatch = useDispatch();
    const actionRef = useRef<any>();
    const [form] = Form.useForm();

    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [loading, setLoading] = useState(false);
    const [editingCardType, setEditingCardType] = useState<any>(null);
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [total, setTotal] = useState(0);

    /*|--------------------------------------------------------------------------
    | FETCH ALL card types WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetCardTypes = (params:any,sort:any,filter:any) =>
        dispatch(getCardTypes({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_cardTypes.labels.id")}`,
            dataIndex: "id",
            responsive: ["xs", "sm", "md", "lg"],
            width: 60,
            search: false,
        },
        {
            title: t(`manage_cardTypes.labels.name`),
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: t(`manage_cardTypes.labels.code`),
            sorter: true,
            serach:false,
            dataIndex: `code`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`code`],
        },
        {
            title: `${t("manage_cardTypes.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_cardTypes.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_card_types") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.pricing")}</Link>,
        },
        {
            title: t("manage_cardTypes.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingCardType(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingCardType(record);
        form.setFieldsValue(record);
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingCardType(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE CARD TYPE
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = editingCardType ? { id: editingCardType.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingCardType) {
                await dispatch(updateCardType(payload)).unwrap();
            } else {
                await dispatch(storeCardType(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            handleReset()
        }
    };

    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_cardTypes.confirmAction"),
            content: editingCardType
                ? t("manage_cardTypes.confirmUpdate")
                : t("manage_cardTypes.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE CARD TYPE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            const response = await dispatch(deleteCardType(id)).unwrap();
            if (response) {
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
                actionRef.current?.reload();
            }
        } catch (error: any) {
            console.log(error);
            
            toast.update(toastId, {
                render: error || t("messages.error") ,
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };


    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setEditingCardType(null);
        setViewMode(false);
        form.resetFields();
    };


    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_cardTypes.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetCardTypes(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        // toolBarRender={() => [
                        //     <Button key="button" onClick={handleAdd} className="btn-add">
                        //         {t("manage_cardTypes.add")}
                        //     </Button>,
                        // ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                centered={true}
                title={
                    viewMode
                        ? t("manage_cardTypes.details")
                        : editingCardType
                            ? t("manage_cardTypes.edit")
                            : t("manage_cardTypes.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_cardTypes.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={loading}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                           <Form.Item
                                label={t("manage_cardTypes.labels.nameFr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_cardTypes.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_cardTypes.placeholders.nameFr")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>

                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_cardTypes.labels.nameEn")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_cardTypes.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_cardTypes.placeholders.nameEn")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
    
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_cardTypes.labels.nameAr")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_cardTypes.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_cardTypes.placeholders.nameAr")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_cardTypes.labels.code")}
                                name="code"
                                rules={[{ required: true, message: t("manage_cardTypes.errors.codeRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_cardTypes.placeholders.code")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageCardTypes;
