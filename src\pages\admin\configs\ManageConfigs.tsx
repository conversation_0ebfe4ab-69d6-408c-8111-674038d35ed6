import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
    Switch,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";

import { useDispatch } from "react-redux";
import {
    deleteConfig,
    getConfigs,
    storeConfig,
    updateConfig
} from "../../../features/admin/configSlice.ts";

const { TextArea } = Input;
const { Option } = Select;

function ManageConfigs() {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const actionRef: any = useRef<any>();
    const dispatch: any = useDispatch();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingConfig, setEditingConfig] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    // Config type options
    const configTypes = [
        { value: 'string', label: t("manage_configs.types.string") },
        { value: 'integer', label: t("manage_configs.types.integer") },
        { value: 'boolean', label: t("manage_configs.types.boolean") },
        { value: 'json', label: t("manage_configs.types.json") },
        { value: 'array', label: t("manage_configs.types.array") },
    ];

    // Common groups for configs
    const configGroups = [
        { value: 'system', label: t("manage_configs.groups.system") },
        { value: 'app', label: t("manage_configs.groups.app") },
        { value: 'email', label: t("manage_configs.groups.email") },
        { value: 'payment', label: t("manage_configs.groups.payment") },
        { value: 'notification', label: t("manage_configs.groups.notification") },
    ];

    /*|--------------------------------------------------------------------------
    | FETCH ALL CONFIGS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetConfigs = (params: any, sort: any, filter: any) =>
        dispatch(getConfigs({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });

    const handleReset = () => {
        setModalVisible(false);
        setEditingConfig(null);
        setViewMode(false);
        form.resetFields();
    };

    /*|--------------------------------------------------------------------------
    | TABLE COLUMNS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_configs.labels.key")}`,
            dataIndex: "key",
            sorter: true,
            width: 150,
        },
        {
            title: `${t("manage_configs.labels.value")}`,
            dataIndex: "value",
            sorter: true,
            width: 200,
            ellipsis: true,
        },
        {
            title: `${t("manage_configs.labels.type")}`,
            dataIndex: "type",
            sorter: true,
            width: 100,
            valueEnum: {
                string: { text: t("manage_configs.types.string") },
                integer: { text: t("manage_configs.types.integer") },
                boolean: { text: t("manage_configs.types.boolean") },
                json: { text: t("manage_configs.types.json") },
                array: { text: t("manage_configs.types.array") },
            },
        },
        {
            title: `${t("manage_configs.labels.group")}`,
            dataIndex: "group",
            sorter: true,
            width: 120,
        },
        {
            title: `${t("manage_configs.labels.label")}`,
            dataIndex: currentLang === 'fr' ? "label_fr" : currentLang === 'en' ? "label_en" : "label_ar",
            sorter: true,
            width: 150,
        },
        {
            title: `${t("manage_configs.labels.is_public")}`,
            dataIndex: "is_public",
            sorter: true,
            width: 100,
            render: (is_public: number) => (
                <span>{is_public === 1 ? t("common.yes") : t("common.no")}</span>
            ),
        },
        {
            title: `${t("manage_configs.labels.is_system")}`,
            dataIndex: "is_system",
            sorter: true,
            width: 100,
            render: (is_system: number) => (
                <span>{is_system === 1 ? t("common.yes") : t("common.no")}</span>
            ),
        },
        {
            title: `${t("manage_configs.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_configs.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_configs.yes")}
                        cancelText={t("manage_configs.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.pricing")}</Link>,
        },
        {
            title: t("manage_configs.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView: any = (record: any) => {
        setEditingConfig(record);
        form.setFieldsValue({
            ...record,
            is_public: record.is_public === 1,
            is_system: record.is_system === 1
        });
        setViewMode(true);
        setModalVisible(true);
    };

    const handleEdit: any = (record: any) => {
        setEditingConfig(record);
        form.setFieldsValue({
            ...record,
            is_public: record.is_public === 1,
            is_system: record.is_system === 1
        });
        setViewMode(false);
        setModalVisible(true);
    };

    const handleAdd: any = () => {
        setEditingConfig(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE CONFIG
    |-------------------------------------------------------------------------- */
    const handleFormSubmit: any = async (values: any) => {
        setLoading(true);
        // Convert boolean values to integers for the backend
        const payload = {
            ...values,
            is_public: values.is_public ? 1 : 0,
            is_system: values.is_system ? 1 : 0
        };
        
        const finalPayload = editingConfig ? { id: editingConfig.id, ...payload } : payload;
        
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingConfig) {
                await dispatch(updateConfig(finalPayload)).unwrap();
            } else {
                await dispatch(storeConfig(payload)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors: any = Object.keys(error.errors || {}).map((field: any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };

    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_configs.confirmAction"),
            content: editingConfig
                ? t("manage_configs.confirmUpdate")
                : t("manage_configs.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE CONFIG
    |-------------------------------------------------------------------------- */
    const handleDelete: any = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteConfig(id)).unwrap()
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    return (
        <div className="p-4">
            <Breadcrumb items={breadcrumbItems} className="mb-4" />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_configs.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true)
                            const dataFilter: any = await handleGetConfigs(params, sort, filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 1000,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_configs.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                centered={true}
                title={
                    viewMode
                        ? t("manage_configs.details")
                        : editingConfig
                            ? t("manage_configs.edit")
                            : t("manage_configs.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_configs.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={loading || viewMode}
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_configs.labels.key")}
                                name="key"
                                rules={[{ required: true, message: t("manage_configs.errors.keyRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_configs.placeholders.key")}
                                    disabled={viewMode || (editingConfig && editingConfig.is_system === 1)}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_configs.labels.group")}
                                name="group"
                                rules={[{ required: true, message: t("manage_configs.errors.groupRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_configs.placeholders.group")}
                                    disabled={viewMode}
                                    allowClear
                                >
                                    {configGroups.map(group => (
                                        <Option key={group.value} value={group.value}>{group.label}</Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_configs.labels.type")}
                                name="type"
                                rules={[{ required: true, message: t("manage_configs.errors.typeRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_configs.placeholders.type")}
                                    disabled={viewMode || (editingConfig && editingConfig.is_system === 1)}
                                >
                                    {configTypes.map(type => (
                                        <Option key={type.value} value={type.value}>{type.label}</Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_configs.labels.value")}
                                name="value"
                                rules={[{ required: true, message: t("manage_configs.errors.valueRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_configs.placeholders.value")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={8}>
                            <Form.Item
                                label={t("manage_configs.labels.label_fr")}
                                name="label_fr"
                                rules={[{ required: true, message: t("manage_configs.errors.labelFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_configs.placeholders.label_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label={t("manage_configs.labels.label_en")}
                                name="label_en"
                                rules={[{ required: true, message: t("manage_configs.errors.labelEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_configs.placeholders.label_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label={t("manage_configs.labels.label_ar")}
                                name="label_ar"
                                rules={[{ required: true, message: t("manage_configs.errors.labelArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_configs.placeholders.label_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={8}>
                            <Form.Item
                                label={t("manage_configs.labels.description_fr")}
                                name="description_fr"
                            >
                                <TextArea
                                    placeholder={t("manage_configs.placeholders.description_fr")}
                                    disabled={viewMode}
                                    rows={3}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label={t("manage_configs.labels.description_en")}
                                name="description_en"
                            >
                                <TextArea
                                    placeholder={t("manage_configs.placeholders.description_en")}
                                    disabled={viewMode}
                                    rows={3}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label={t("manage_configs.labels.description_ar")}
                                name="description_ar"
                            >
                                <TextArea
                                    placeholder={t("manage_configs.placeholders.description_ar")}
                                    disabled={viewMode}
                                    rows={3}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_configs.labels.is_public")}
                                name="is_public"
                                valuePropName="checked"
                            >
                                <Switch disabled={viewMode} />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_configs.labels.is_system")}
                                name="is_system"
                                valuePropName="checked"
                            >
                                <Switch disabled={viewMode || editingConfig} />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </div>
    );
}

export default ManageConfigs;
