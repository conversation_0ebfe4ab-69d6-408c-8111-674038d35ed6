-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 14, 2025 at 04:34 PM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `temp_srtgn`
--

-- --------------------------------------------------------

--
-- Table structure for table `type_location`
--

CREATE TABLE `type_location` (
  `ID_TYPE_LOCATION` int(11) NOT NULL,
  `CODE_TYPE_LOCATION` char(50) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- Dumping data for table `type_location`
--

INSERT INTO `type_location` (`ID_TYPE_LOCATION`, `CODE_TYPE_LOCATION`) VALUES
(1, 'Mariage/association Sportive'),
(2, 'Autres');

-- --------------------------------------------------------

--
-- Table structure for table `type_vehicle_type_location`
--

CREATE TABLE `type_vehicle_type_location` (
  `CODE_TYPE` char(50) NOT NULL DEFAULT '',
  `ID_TYPE_LOCATION` decimal(8,0) NOT NULL DEFAULT 0,
  `KM_MIN` int(11) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Table structure for table `type_vehicule`
--

CREATE TABLE `type_vehicule` (
  `ID_TYPE_VEHICULE` int(11) NOT NULL,
  `CODE_TYPE` varchar(50) NOT NULL DEFAULT '',
  `LIBELLE` varchar(50) DEFAULT NULL,
  `NBRE_MAX_PLACE` int(11) DEFAULT NULL,
  `swf` text NOT NULL,
  `photo` text NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Table structure for table `type_vehicule_saison_location`
--

CREATE TABLE `type_vehicule_saison_location` (
  `CODE_TYPE` char(50) NOT NULL DEFAULT '',
  `ID_SAISON_LOCATION` decimal(8,0) NOT NULL DEFAULT 0,
  `PRIX_KM` int(11) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


--
-- Indexes for dumped tables
--

--
-- Indexes for table `type_location`
--
ALTER TABLE `type_location`
  ADD PRIMARY KEY (`ID_TYPE_LOCATION`);

--
-- Indexes for table `type_vehicle_type_location`
--
ALTER TABLE `type_vehicle_type_location`
  ADD PRIMARY KEY (`CODE_TYPE`,`ID_TYPE_LOCATION`),
  ADD KEY `TYPE_VEHICLE_TYPE_LOCATION_FK` (`CODE_TYPE`),
  ADD KEY `TYPE_VEHICLE_TYPE_LOCATION2_FK` (`ID_TYPE_LOCATION`);

--
-- Indexes for table `type_vehicule`
--
ALTER TABLE `type_vehicule`
  ADD PRIMARY KEY (`ID_TYPE_VEHICULE`);

--
-- Indexes for table `type_vehicule_saison_location`
--
ALTER TABLE `type_vehicule_saison_location`
  ADD PRIMARY KEY (`CODE_TYPE`,`ID_SAISON_LOCATION`),
  ADD KEY `TYPE_VEHICULE_SAISON_LOCATION_FK` (`CODE_TYPE`),
  ADD KEY `TYPE_VEHICULE_SAISON_LOCATION2_FK` (`ID_SAISON_LOCATION`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `type_location`
--
ALTER TABLE `type_location`
  MODIFY `ID_TYPE_LOCATION` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `type_vehicule`
--
ALTER TABLE `type_vehicule`
  MODIFY `ID_TYPE_VEHICULE` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
