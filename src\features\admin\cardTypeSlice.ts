import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/card-types';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getCardTypesAll: any = createAsyncThunk(
    "getCardTypesAll",
    async (_:any,thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getCardTypes: any = createAsyncThunk(
    "getCardTypes",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params:any,
            sort:any,
            filter:any
        },
        thunkAPI:any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeCardType: any = createAsyncThunk(
    "storeCardType",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        }  catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateCardType: any = createAsyncThunk(
    "updateCardType",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteCardType: any = createAsyncThunk(
    "deleteCardType",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const cardTypeSlice = createSlice({
    name: 'cardType',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getCardTypesAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCardTypesAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getCardTypesAll.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getCardTypes.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCardTypes.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getCardTypes.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(storeCardType.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeCardType.fulfilled, (state: any, action) => {
                state.loading = false;
                state.items.push(action.payload);
            })
            .addCase(storeCardType.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(updateCardType.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateCardType.fulfilled, (state: any, action) => {
                state.loading = false;
                const index = state.items.findIndex((item: any) => item.id === action.payload.id);
                if (index !== -1) {
                    state.items[index] = action.payload;
                }
            })
            .addCase(updateCardType.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(deleteCardType.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteCardType.fulfilled, (state: any, action) => {
                state.loading = false;
                state.items = state.items.filter((item: any) => item.id !== action.payload);
            })
            .addCase(deleteCardType.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = cardTypeSlice.actions;
export default cardTypeSlice.reducer;