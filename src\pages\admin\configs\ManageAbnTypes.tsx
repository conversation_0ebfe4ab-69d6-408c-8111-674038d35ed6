import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Breadcrumb,
    Row,
    Col,
    ColorPicker,
    Switch,
    Tag,
} from "antd";
import { EditOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {rgbToHex} from "../../../tools/helpers.ts";
import {useDispatch} from "react-redux";
import {toast} from "react-toastify";
import {
    getAbnTypes,
    storeAbnType,
    updateAbnType
} from "../../../features/admin/abnTypeSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageAbnTypes() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;
    const dispatch:any = useDispatch();

    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingAbnTypes, setEditingAbnTypes] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
    | FETCH ALL ABN TYPES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetAbnTypes = (params:any,sort:any,filter:any) =>
        dispatch(getAbnTypes({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_abnTypes.labels.id")}`,
            dataIndex: "id",
            responsive: ["xs", "sm", "md", "lg"],
            width: 60,
            search: false,
        },
        {
            title: t(`manage_cardTypes.labels.name`),
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: t("manage_abnTypes.labels.color"),
            search: false,
            width: 130,
            responsive: ["xs", "sm", "md", "lg"],
            dataIndex: "color", render: (color:any) =>
                (
                    <div style={{ background: color, width: 30, height: 15, borderRadius: "5px" }} className="hover:shadow-sm hover:scale-110 transition-all">
                    </div>
                )
        },
        {
            title: `${t("manage_clientTypes.labels.isStudent")}`,
            dataIndex: "is_student",
            width: 130,
            hidden:true,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_:any,value: any) => {
                return (
                    <Tag color={value.is_student === true ? "success" : "error"}>
                        {value.is_student === true ? t("common.yes") : t("common.no")}
                    </Tag>
                );
            },
            search: false,
            valueType: "select",
            valueEnum: {
                true: { text: t("common.yes"), status: "Success" },
                false: { text: t("common.no"), status: "Error" },
            },
        },
        {
            title: `${t("manage_clientTypes.labels.hasCIN")}`,
            dataIndex: "hasCIN",
            width: 110,
            hidden:true,
            responsive: ["xs", "sm", "md", "lg"],
            search: false,
            render: (_:any,value: any) => {
                return (
                    <Tag color={value.hasCIN === true ? "success" : "error"}>
                        {value.hasCIN === true ? t("common.yes") : t("common.no")}
                    </Tag>
                );
            },
            valueType: "select",
            valueEnum: {
                true: { text: t("common.yes"), status: "Success" },
                false: { text: t("common.no"), status: "Error" },
            },
        },
        {
            title: `${t("manage_clientTypes.labels.isImpersonal")}`,
            dataIndex: "is_impersonal",
            width: 130,
            hidden:true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_:any,value: any) => {
                return (
                    <Tag color={value.is_impersonal === true ? "success" : "error"}>
                        {value.is_impersonal === true ? t("common.yes") : t("common.no")}
                    </Tag>
                );
            },
            valueType: "select",
            valueEnum: {
                true: { text: t("common.yes"), status: "Success" },
                false: { text: t("common.no"), status: "Error" },
            },
        },
        {
            title: `${t("manage_abnTypes.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 130,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_abnTypes.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_abn_types") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {/*<Popconfirm
                        title={t("manage_abnTypes.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_abnTypes.yes")}
                        cancelText={t("manage_abnTypes.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>*/}
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.pricing")}</Link>,
        },
        {
            title: t("manage_abnTypes.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView:any = (record: any) => {
        setEditingAbnTypes(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit:any = (record: any) => {
        setEditingAbnTypes(record);
        form.setFieldsValue(record);
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE ABN TYPE
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        let hexColor = values.color;
        if (typeof values.color === 'object' && values.color !== null) {
            if (values.color.toHexString) {
                hexColor = values.color.toHexString();
            } else if (values.color._color) {
                hexColor = values.color._color.toUpperCase();
            }
        } else if (typeof values.color === 'string') {
            hexColor = values.color.toUpperCase();
        }

        const normalizedValues = {
            nom_fr: values.nom_fr,
            nom_en: values.nom_en,
            nom_ar: values.nom_ar,
            color: hexColor,
            is_student: values.is_student ?? false,
            hasCIN: values.hasCIN ?? false,
            is_impersonal: values.is_impersonal ?? false
        };

        const payload = editingAbnTypes 
            ? { id: editingAbnTypes.id, ...normalizedValues }
            : normalizedValues;

        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        try {
            if (editingAbnTypes) {
                await dispatch(updateAbnType(payload)).unwrap();
            } else {
                await dispatch(storeAbnType(normalizedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_abnTypes.confirmAction"),
            content: editingAbnTypes
                ? t("manage_abnTypes.confirmUpdate")
                : t("manage_abnTypes.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE ABN TYPE
    |-------------------------------------------------------------------------- */
    /*const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteAbnType(id));
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };*/

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        form.resetFields();
        setLoading(false);
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_abnTypes.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true);
                            const dataFilter:any = await handleGetAbnTypes(params,sort,filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={"id"}
                        pagination={{
                            pageSize,
                            total,
                            onChange: setPageNumber,
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                            search: false,
                            reload: true,
                            setting: false,
                        }}
                        loading={loading}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_abnTypes.details")
                        : editingAbnTypes
                            ? t("manage_abnTypes.edit")
                            : t("manage_abnTypes.add")
                }
                open={modalVisible}
                onCancel={handleReset}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_abnTypes.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={loading}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={6}>
                            <Form.Item
                                label={t("manage_abnTypes.labels.color")}
                                name="color"
                                rules={[{ required: true, message: t("manage_abnTypes.errors.colorRequired") }]}
                            >
                                <ColorPicker disabled={viewMode} />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={6}>
                            <Form.Item
                                label={t("manage_abnTypes.labels.isStudent")}
                                name="is_student"
                                valuePropName="checked"
                                initialValue={false}
                            >
                                <Switch disabled={true} />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={6}>
                            <Form.Item
                                label={t("manage_abnTypes.labels.hasCIN")}
                                name="hasCIN"
                                valuePropName="checked"
                                initialValue={false}
                            >
                                <Switch disabled={true} />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={6}>
                            <Form.Item
                                label={t("manage_abnTypes.labels.isImpersonal")}
                                name="is_impersonal"
                                valuePropName="checked"
                                initialValue={false}
                            >
                                <Switch disabled={true} />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageAbnTypes;
