import React from 'react';
import { Table, Typography, Tag } from 'antd';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

interface AuditChangesDiffProps {
    oldValues: Record<string, any> | null;
    newValues: Record<string, any> | null;
    changes: Record<string, any>;
}

const AuditChangesDiff: React.FC<AuditChangesDiffProps> = ({
    oldValues,
    newValues,
    changes
}) => {
    const { t } = useTranslation();

    const formatValue = (value: any): string => {
        if (value === null || value === undefined) {
            return t('audits.null_value') || '(null)';
        }
        if (typeof value === 'boolean') {
            return value ? t('common.yes') || 'Yes' : t('common.no') || 'No';
        }
        if (typeof value === 'object') {
            return JSON.stringify(value, null, 2);
        }
        return String(value);
    };

    const getChangeType = (oldVal: any, newVal: any) => {
        if (oldVal === null || oldVal === undefined) {
            return 'added';
        }
        if (newVal === null || newVal === undefined) {
            return 'removed';
        }
        return 'modified';
    };

    const getTagColor = (changeType: string) => {
        switch (changeType) {
            case 'added': return 'success';
            case 'removed': return 'error';
            case 'modified': return 'warning';
            default: return 'default';
        }
    };

    if (!changes || Object.keys(changes).length === 0) {
        return (
            <Text type="secondary" italic>
                {t('audits.no_changes')}
            </Text>
        );
    }

    const tableData = Object.keys(changes).map((field) => {
        const changeData = changes[field];
        let oldVal, newVal;

        if (changeData && typeof changeData === 'object' && 'old' in changeData && 'new' in changeData) {
            oldVal = changeData.old;
            newVal = changeData.new;
        } else {
            oldVal = oldValues?.[field];
            newVal = newValues?.[field] || changeData;
        }

        const changeType = getChangeType(oldVal, newVal);

        return {
            key: field,
            field: field,
            oldValue: oldVal,
            newValue: newVal,
            changeType: changeType
        };
    });

    const columns = [
        {
            title: t('audits.labels.field') || 'Field',
            dataIndex: 'field',
            key: 'field',
            width: '25%',
            render: (field: string, record: any) => (
                <div>
                    <Text strong style={{ color: '#3e3e3e' }}>{field}</Text>
                    <br />
                    <Tag
                        color={getTagColor(record.changeType)}
                        style={{ fontSize: '11px', marginTop: '4px' }}
                    >
                        {t(`audits.${record.changeType}`) || record.changeType}
                    </Tag>
                </div>
            ),
        },
        {
            title: t('audits.old_value') || 'Old Value',
            dataIndex: 'oldValue',
            key: 'oldValue',
            width: '37.5%',
            render: (value: any, record: any) => {
                if (record.changeType === 'added') {
                    return <Text type="secondary" italic>-</Text>;
                }
                return (
                    <Text
                        code
                        style={{
                            backgroundColor: '#fff2f0',
                            padding: '6px 10px',
                            borderRadius: '6px',
                            border: '1px solid #ffccc7',
                            display: 'block',
                            wordBreak: 'break-word',
                            whiteSpace: 'pre-wrap',
                        }}
                    >
                        {formatValue(value)}
                    </Text>
                );
            },
        },
        {
            title: t('audits.new_value') || 'New Value',
            dataIndex: 'newValue',
            key: 'newValue',
            width: '37.5%',
            render: (value: any, record: any) => {
                if (record.changeType === 'removed') {
                    return <Text type="secondary" italic>-</Text>;
                }
                return (
                    <Text
                        code
                        style={{
                            backgroundColor: '#f6ffed',
                            padding: '6px 10px',
                            borderRadius: '6px',
                            border: '1px solid #b7eb8f',
                            display: 'block',
                            wordBreak: 'break-word',
                            whiteSpace: 'pre-wrap',
                        }}
                    >
                        {formatValue(value)}
                    </Text>
                );
            },
        },
    ];

    return (
        <div className="audit-changes-diff">
            <Table
                columns={columns}
                dataSource={tableData}
                pagination={false}
                size="small"
                bordered
                scroll={{ x: 600 }}
                style={{
                    backgroundColor: '#ffffff',
                    borderRadius: '8px'
                }}
                rowClassName={(record) => {
                    switch (record.changeType) {
                        case 'added': return 'audit-row-added';
                        case 'removed': return 'audit-row-removed';
                        case 'modified': return 'audit-row-modified';
                        default: return '';
                    }
                }}
            />
        </div>
    );
};

export default AuditChangesDiff;
