import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import fr from './locales/fr.json';
import ar from './locales/ar.json';
import en from './locales/en.json';
import socialAffairsTranslations from './locales/social_affairs_translations.json';
import academicYearsTranslations from './locales/academic_years_translations.json';

// Merge translations with main translations
const mergedTranslations = {
  fr: {
    translation: {
      ...fr,
      ...socialAffairsTranslations.fr,
      ...academicYearsTranslations.fr
    }
  },
  ar: {
    translation: {
      ...ar,
      ...socialAffairsTranslations.ar,
      ...academicYearsTranslations.ar
    }
  },
  en: {
    translation: {
      ...en,
      ...socialAffairsTranslations.en,
      ...academicYearsTranslations.en
    }
  },
};

i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources: mergedTranslations,
      fallbackLng: 'fr',
      interpolation: {
        escapeValue: false
      }
    });

export default i18n;