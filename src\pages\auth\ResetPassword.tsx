import React, { useEffect, useState } from "react";
import { Form, Input, Button, Card, Steps, Divider } from "antd";
import { Mail, KeyRound } from "lucide-react";
import { useTranslation } from "react-i18next";
import { assets } from "../../assets/assets.ts";
import { Link } from "react-router-dom";
import { OTPProps } from "antd/es/input/OTP";
import { useMediaQuery } from "react-responsive";
import { LanguageSelector } from "../../components";
import { useDispatch } from "react-redux";
import {
  changePassword,
  forgetPassword,
  verifCode,
} from "../../features/admin/adminSlice.ts";
import { toast } from "react-toastify";

const { Step } = Steps;

const ResetPassword: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [countdownEmail, setCountdownEmail] = useState(0);
  const [isEmailLinkActive, setIsEmailLinkActive] = useState(true);
  const [otpEmailValue, setOtpEmailValue] = useState("");
  const [otpEmailError, setOtpEmailError] = useState(false);
  const [isEmailValidated, setIsEmailValidated] = useState(false);
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  const isSmallScreen: any = useMediaQuery({ query: "(max-width: 575px)" });

  const onChangeOtpEmail: OTPProps["onChange"] = (text) => {
    console.log("onChange:", text);
    setOtpEmailValue(text);
    if (text.length === 8) {
      validateOtpEmail(text);
    }
  };

  const validateOtpEmail = async (token?: string) => {
    console.log("OTP à valider:", otpEmailValue);
    await dispatch(
      verifCode({
        token: token ? token : otpEmailValue,
        email: form.getFieldValue("email"),
      })
    )
      .unwrap()
      .then((res: any) => {
        console.log(res);
        setOtpEmailError(false);
        setIsEmailValidated(true);
      })
      .catch((error: any) => {
        console.log(error);
        toast.error(t("messages.error"));
        if (error.errors) {
          const fieldErrors = Object.entries(error.errors).map(
            ([name, errors]) => ({
              name,
              errors: Array.isArray(errors) ? errors : [errors],
            })
          );
          form.setFields(fieldErrors);
          console.log(fieldErrors);
        }
      });
  };

  const handleResendEmailCode = () => {
    setIsEmailLinkActive(false);
    setCountdownEmail(60);
    const interval = setInterval(() => {
      setCountdownEmail((prev: any) => {
        if (prev === 1) {
          clearInterval(interval);
          setIsEmailLinkActive(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    dispatch(forgetPassword(form.getFieldValue("email")))
      .unwrap()
      .then(() => {
        toast.success(t("messages.success"));
        //clearInterval(interval);
        //setCountdownEmail(60);
        // setIsEmailLinkActive(true);
      })

      .catch((error: any) => {
        console.log(error);
        if (error.errors) {
          const fieldErrors = Object.entries(error.errors).map(
            ([name, errors]) => ({
              name,
              errors: Array.isArray(errors) ? errors : [errors],
            })
          );
          form.setFields(fieldErrors);
        }
        toast.error(t("messages.error"));
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const next = () => {
    form
      .validateFields()
      .then(() => {
        if (currentStep === 1 && !isEmailValidated) {
          return validateOtpEmail();
        } else if (currentStep === 0) {
          setLoading(true);
          console.log("forget password", form.getFieldValue("email"));
          dispatch(forgetPassword(form.getFieldValue("email")))
            .unwrap()
            .then((res: any) => {
              console.log(res);
              toast.success(t("messages.success"));
              setCurrentStep((prev: any) => prev + 1);
            })
            .catch((error: any) => {
              console.log(error);
              toast.error(t("messages.error"));
              console.log(error);
              if (error.errors) {
                const fieldErrors = Object.entries(error.errors).map(
                  ([name, errors]) => ({
                    name,
                    errors: Array.isArray(errors) ? errors : [errors],
                  })
                );
                form.setFields(fieldErrors);
                console.log(fieldErrors);
              }
            })
            .finally(() => {
              setLoading(false);
            });
        } else if (currentStep < steps.length - 1) {
          setCurrentStep((prev: any) => prev + 1);
        } else {
          console.log("last step");
          onFinish();
        }
      })
      .catch((errorInfo: any) => {
        console.log("Validation Failed:", errorInfo);
      });
  };
  const steps = [
    {
      title: t("reset.step1Title"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("reset.requestTitle")}
          </h2>
          <p className="text-gray-600 text-center mb-6">
            {t("reset.requestDescription")}
          </p>
          <Form.Item
            name="email"
            rules={[
              { required: true, message: t("reset.validation.emailRequired") },
              { type: "email", message: t("reset.validation.emailValid") },
            ]}
          >
            <Input
              prefix={<Mail className="text-gray-400" size={18} />}
              placeholder={t("reset.emailPlaceholder")}
              className="text-sm"
              disabled={isEmailValidated}
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: t("reset.step2Title"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("reset.OTPEmailTitle")}
          </h2>
          <p className="text-gray-600 text-center">
            {t("reset.OTPEmailDescription")}
          </p>
          <Form.Item validateStatus={otpEmailError ? "error" : ""}>
            <div className="lg:px-16 sm:px-3 pt-6">
              {isEmailValidated ? (
                <div className="text-sm p-6 border-2 border-gray-100 rounded-lg shadow-sm">
                  <div className="flex justify-center items-center">
                    <img src={assets.validatedGif} alt="GIF" width={70} />
                  </div>
                  <div className="flex items-center justify-center text-red-600">
                    {t("reset.EmailValidated")}
                  </div>
                </div>
              ) : (
                <>
                  <Input.OTP
                    length={8}
                    size="middle"
                    className={`justify-center ${
                      otpEmailError ? "border-red-500" : ""
                    }`}
                    autoFocus
                    onChange={onChangeOtpEmail}
                  />

                  <div className="mt-3 flex text-center justify-center items-center">
                    <span>
                      {isEmailLinkActive ? (
                        <Button
                          type="link"
                          className="w-full h-12 text-sm text-md text-red-500 font-medium transition-all duration-300"
                          onClick={handleResendEmailCode}
                        >
                          {t("reset.resendCode")}
                        </Button>
                      ) : (
                        <span>
                          {t("reset.retryIn")}{" "}
                          <span className="font-semibold">
                            {countdownEmail}
                          </span>{" "}
                          {t("reset.seconds")}
                        </span>
                      )}
                    </span>
                  </div>
                </>
              )}
            </div>
          </Form.Item>
        </>
      ),
    },
    {
      title: t("reset.step3Title"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("reset.newPasswordTitle")}
          </h2>
          <p className="text-gray-600 text-center mb-6">
            {t("reset.newPasswordDescription")}
          </p>
          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: t("reset.validation.passwordRequired"),
              },
              { min: 8, message: t("reset.validation.passwordLength") },
            ]}
          >
            <Input.Password
              prefix={<KeyRound className="text-gray-400" size={18} />}
              placeholder={t("reset.passwordPlaceholder")}
              className="text-sm"
            />
          </Form.Item>
          <Form.Item
            name="password_confirmation"
            dependencies={["password"]}
            rules={[
              {
                required: true,
                message: t("reset.validation.confirmRequired"),
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(t("reset.validation.passwordMatch"))
                  );
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<KeyRound className="text-gray-400" size={18} />}
              placeholder={t("reset.confirmPlaceholder")}
              className="text-sm"
            />
          </Form.Item>
        </>
      ),
    },
  ];

  const onFinish = () => {
    const allValues = form.getFieldsValue(true);
    console.log("Form Submitted:", allValues);
    setLoading(true);
    dispatch(changePassword(allValues))
      .unwrap()
      .then(() => {
        toast.success(t("messages.passwordChanged"));
        setTimeout(() => {
          window.location.href = "/";
        }, 1000);
      })
      .catch((error: any) => {
        console.log(error);
        if (error.errors) {
          const fieldErrors = Object.entries(error.errors).map(
            ([name, errors]) => ({
              name,
              errors: Array.isArray(errors) ? errors : [errors],
            })
          );
          form.setFields(fieldErrors);
        }
        toast.error(t("messages.error"));
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const prev = () => {
    setOtpEmailValue("");
    if (currentStep === 0 || (currentStep === 1 && isEmailValidated)) {
      return;
    }
    setCurrentStep((prev: any) => prev - 1);
  };
  const isPrevDisabled =
    currentStep === 0 || (currentStep === 1 && isEmailValidated);

  return (
    <div className="min-h-screen flex">
      {/* reset Form */}
      <div
        className="
                    w-full
                    md:w-full
                    flex items-center justify-center
                    bg-center bg-no-repeat bg-contain"
        style={{
          backgroundImage: `url(${assets.bg})`,
        }}
      >
        <Card
          className="w-full max-w-full sm:max-w-3xl md:max-w-3xl lg:max-w-3xl m-5 !drop-shadow-sm px-2 md:px-12 py-20"
          bordered={false}
        >
          <div
            className={`absolute top-5 ${
              i18n.language === "ar" ? "right-5" : "left-5"
            } `}
          >
            <LanguageSelector />
          </div>

          <Steps current={currentStep} className="mb-8">
            {steps.map((item, index) => (
              <Step key={index} title={isSmallScreen ? item.title : ""} />
            ))}
          </Steps>

          <Form
            form={form}
            name="reset"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            layout="vertical"
            size="large"
            className="auth-form"
          >
            {steps[currentStep]?.content}

            <div className="flex justify-between">
              {currentStep > 0 && (
                <Button
                  disabled={isPrevDisabled}
                  type="link"
                  onClick={prev}
                  className="h-10 text-black"
                >
                  {t("reset.previous")}
                </Button>
              )}
              {currentStep === 0 ? (
                <Button
                  type="primary"
                  onClick={next}
                  className="w-full h-12 text-sm text-md font-medium shadow-md hover:shadow-lg transition-all duration-300"
                  loading={loading}
                >
                  {t("reset.next")}
                </Button>
              ) : currentStep < steps?.length - 1 ? (
                <Button
                  type="link"
                  onClick={next}
                  className="h-10 text-red-500 !hover:text-red-500"
                >
                  {t("reset.next")}
                </Button>
              ) : (
                <Button
                  type="link"
                  onClick={next}
                  className="h-10 text-red-500 !hover:text-red-500"
                  loading={loading}
                >
                  {t("reset.confirm")}
                </Button>
              )}
            </div>

            <Divider className="border-gray-200">
              <span className="text-gray-400 text-sm">
                {t("login.or") || "OR"}
              </span>
            </Divider>
            <div className="text-gray-600 mt-6 text-center">
              {t("reset.haveAccount")}
              <Link
                to="/"
                className="mx-1 text-red-600 hover:text-red-500 hover:underline transition-colors font-medium"
              >
                {t("reset.signIn")}
              </Link>
            </div>
          </Form>
        </Card>
      </div>

      {/* Right side - Image */}
      {/* <LandingSide/> */}
    </div>
  );
};

export default ResetPassword;
