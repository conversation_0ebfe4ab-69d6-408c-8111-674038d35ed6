import {
    <PERSON><PERSON>,
    Card,
    Col,
    DatePicker,
    Empty,
    Form,
    Input,
    Modal,
    Popconfirm,
    Progress,
    Row,
    Select,
    Space,
    Typography
} from "antd";
import {CalendarOutlined, DeleteOutlined, EditOutlined} from "@ant-design/icons";
import moment from "moment/moment";
import {useTranslation} from "react-i18next";
import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { Spin } from "antd";
import {
    createSalesPeriod,
    deleteSalesPeriod, updateSalesPeriod,
} from "../../features/admin/salesPeriodsSlice";
import { toast } from "react-toastify";
import {getCampaignSalesPeriods} from "../../features/admin/campaignSlice";
import { Switch } from "antd";
import { getAbnTypesAll } from '../../features/admin/abnTypeSlice';

const CampaignPeriodsManager = ({ campaignId }: { campaignId: number })  => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;
    const dispatch = useDispatch<any>();
    const [salesPeriodForm] = Form.useForm();
    const [editingSalesPeriod, setEditingSalesPeriod] = useState<any>(null);
    const [salesPeriodModal, setSalesPeriodModal] = useState(false);
    const [selectedCampaign, setSelectedCampaign] = useState<any>(null);
    const [salesPeriods, setSalesPeriods] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [abnTypes, setAbnTypes] = useState<any[]>([]);

    /*|--------------------------------------------------------------------------
    | FETCH ALL SALES PERIODS FOR THE SELECTED CAMPAIGN
    |-------------------------------------------------------------------------- */
    useEffect(() => {
        fetchSalesPeriodsByCampaign();
    }, [campaignId]);

    const fetchSalesPeriodsByCampaign = async () => {
        try {
            setLoading(true);
            const result = await dispatch(getCampaignSalesPeriods(campaignId)).unwrap();
            setSalesPeriods(result.data || []);
        } catch (error: any) {
            toast.error(`${t("manage_campaigns.error")}: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    // Add this useEffect to fetch subscription types
    useEffect(() => {
        dispatch(getAbnTypesAll())
            .unwrap()
            .then((result: any) => {
                const typesArray = Array.isArray(result) ? result : result.data || [];
                setAbnTypes(typesArray);
            })
            .catch((error: any) => {
                console.error('Error fetching subscription types:', error);
                toast.error(t("messages.error"));
                setAbnTypes([]);
            });
    }, [dispatch]);

    /*|--------------------------------------------------------------------------
    | HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleAddSalesPeriod = (campaign: any) => {
        setSelectedCampaign(campaign);
        setEditingSalesPeriod(null);
        salesPeriodForm.resetFields();
        setSalesPeriodModal(true);
    };
    const handleEditSalesPeriod = (campaign: any, salesPeriod: any) => {
        setSelectedCampaign(campaign);
        setEditingSalesPeriod(salesPeriod);
        
        salesPeriodForm.setFieldsValue({
            nom_fr: salesPeriod.nom_fr,
            nom_en: salesPeriod.nom_en,
            nom_ar: salesPeriod.nom_ar,
            id_abn_type: salesPeriod.id_abn_type,
            date_start: salesPeriod.date_start ? moment(salesPeriod.date_start) : null,
            date_end: salesPeriod.date_end ? moment(salesPeriod.date_end) : null,
            status: salesPeriod.status
        });
        setSalesPeriodModal(true);
    };
    const calculatePeriodProgress = (period: any) => {
        const start = moment(period.date_start);
        const end = moment(period.date_end);
        const now = moment();

        if (now.isBefore(start)) return 0;
        if (now.isAfter(end)) return 100;

        const totalDuration = end.diff(start);
        const elapsed = now.diff(start);
        return Math.round((elapsed / totalDuration) * 100);
    };

    /*|--------------------------------------------------------------------------
    | HANDLE SUBMIT
    |-------------------------------------------------------------------------- */
    const handleSalesPeriodSubmit = async (values: any) => {
        setLoading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            const formattedValues = {
                nom_fr: values.nom_fr,
                nom_en: values.nom_en,
                nom_ar: values.nom_ar,
                date_start: values.date_start ? values.date_start.format('YYYY-MM-DD') : null,
                date_end: values.date_end ? values.date_end.format('YYYY-MM-DD') : null,
                id_campaign: selectedCampaign,
                id_abn_type: values.id_abn_type,
                status: values.status ?? false
            };

            if (editingSalesPeriod) {
                await dispatch(updateSalesPeriod({
                    id: editingSalesPeriod.id,
                    ...formattedValues
                })).unwrap();
            } else {
                await dispatch(createSalesPeriod(formattedValues)).unwrap();
            }

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });

            handleReset();
            await fetchSalesPeriodsByCampaign();
            setSalesPeriodModal(false);

        } catch (error: any) {
            toast.update(toastId, {
                render: error.message || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        } finally {
            setLoading(false);
        }
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE CAMPAIGN
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            await dispatch(deleteSalesPeriod(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            await fetchSalesPeriodsByCampaign();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        }
    }

    /*|--------------------------------------------------------------------------
    | HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setEditingSalesPeriod(null);
        setSelectedCampaign(null);
        setSalesPeriodModal(false);
        salesPeriodForm.resetFields();
    };
   
    return (
        <>
            <div className="p-4">
                <div className="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
                    <h4 className="text-lg font-semibold text-gray-800">
                        {t("manage_campaigns.salesPeriods")}
                    </h4>
                    {!loading && salesPeriods.length > 0 && (
                        <Button
                            type="dashed"
                            onClick={() => handleAddSalesPeriod(campaignId)}
                            className="w-full sm:w-auto"
                        >
                            {t("manage_campaigns.addSalesPeriod")}
                        </Button>
                    )}
                </div>

                {loading ? (
                    <div className="flex justify-center items-center py-8">
                        <Spin />
                    </div>
                ) : salesPeriods.length > 0 ? (
                    <Row gutter={[16, 16]}>
                        {salesPeriods.map((period: any) => (
                            <Col xs={24} sm={12} md={12} lg={8} xl={8} key={period.id}>
                                <Card
                                    className="h-full shadow-sm hover:shadow-md transition-shadow duration-200"
                                    actions={[
                                        <Button
                                            type="text"
                                            icon={<EditOutlined className="btn-add"/>}
                                            onClick={() => handleEditSalesPeriod(campaignId, period)}
                                        />,
                                        <Popconfirm
                                            title={t("manage_campaigns.confirmDeletePeriod")}
                                            onConfirm={() => handleDelete(period.id)}
                                        >
                                            <Button
                                                type="text"
                                                icon={<DeleteOutlined className="btn-delete"/>}
                                            />
                                        </Popconfirm>
                                    ]}
                                >
                                    <Card.Meta
                                        title={
                                            <div className="flex justify-between items-center mb-3">
                                                <span className="font-medium text-sm">{period[`nom_${currentLang}`]}</span>
                                            </div>
                                        }
                                        description={
                                            <Space direction="vertical" size="small" className="w-full">
                                                <div className="flex items-end gap-2">
                                                    <CalendarOutlined className="text-xl"/>
                                                    <small>
                                                        {moment(period.date_start).format("DD MMM YYYY")}
                                                        {' - '}
                                                        {moment(period.date_end).format("DD MMM YYYY")}
                                                    </small>
                                                </div>

                                                <div className="mt-2">
                                                    <Progress
                                                        percent={calculatePeriodProgress(period)}
                                                        size="small"
                                                        status="active"
                                                        strokeColor={
                                                            period.status
                                                                ? 'var(--secondary-color)'
                                                                : 'var(--primary-color)'
                                                        }
                                                    />
                                                </div>
                                            </Space>
                                        }
                                    />
                                </Card>
                            </Col>
                        ))}
                    </Row>
                ) : (
                    <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                            <Typography.Text type="secondary">
                                {t("manage_campaigns.noSalesPeriods")}
                            </Typography.Text>
                        }
                        className="py-8"
                    >
                        <Button
                            type="dashed"
                            onClick={() => handleAddSalesPeriod(campaignId)}
                            className="w-full sm:w-auto"
                        >
                            {t("manage_campaigns.addFirstPeriod")}
                        </Button>
                    </Empty>
                )}
            </div>

            <Modal
                title={t("manage_campaigns.salesPeriodModal")}
                open={salesPeriodModal}
                onCancel={() => setSalesPeriodModal(false)}
                onOk={() => salesPeriodForm.submit()}
                width={900}
            >
                <Form
                    form={salesPeriodForm}
                    layout="vertical"
                    onFinish={handleSalesPeriodSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_delegations.errors.nameFrRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_delegations.errors.nameEnRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_delegations.errors.nameArRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="date_start"
                                label={t("manage_campaigns.labels.startDate")}
                                rules={[{ required: true, message: t("manage_campaigns.errors.startDateRequired") }]}
                            >
                                <DatePicker
                                    className="w-full"
                                    format="YYYY-MM-DD"
                                    placeholder={t("manage_campaigns.placeholders.startDate")}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="date_end"
                                label={t("manage_campaigns.labels.endDate")}
                                rules={[{ required: true, message: t("manage_campaigns.errors.endDateRequired") }]}
                            >
                                <DatePicker
                                    className="w-full"
                                    format="YYYY-MM-DD"
                                    placeholder={t("manage_campaigns.placeholders.endDate")}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                name="id_abn_type"
                                label={t("manage_campaigns.labels.abnType")}
                                rules={[{ required: true, message: t("manage_campaigns.errors.abnTypeRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_campaigns.placeholders.selectAbnType")}
                                    loading={!abnTypes.length}
                                >
                                    {abnTypes.map((type) => (
                                        <Select.Option key={type.id} value={type.id}>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                <div
                                                    style={{
                                                        width: '12px',
                                                        height: '12px',
                                                        borderRadius: '50%',
                                                        backgroundColor: type.color
                                                    }}
                                                />
                                                {type[`nom_${currentLang}`]}
                                            </div>
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item
                        name="status"
                        label={t("manage_campaigns.labels.status")}
                        initialValue={true}
                        valuePropName="checked"
                    >
                        <Switch
                            checkedChildren={t("manage_campaigns.open")}
                            unCheckedChildren={t("manage_campaigns.closed")}
                            className="bg-gray-300"
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}

export default CampaignPeriodsManager;
