import { useRef, useState, useEffect } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
    InputNumber,
    DatePicker, Tag,
    Switch,
    Card,
    Space,
    Divider
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined, PlusOutlined, MinusCircleOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {useDispatch} from "react-redux";
import dayjs from 'dayjs';
import {
    deleteTarifBase,
    getTarifBases,
    storeTarifBase,
    updateTarifBase
} from "../../../features/admin/tarifBaseSlice.ts";
import { getAbnTypesAll } from "../../../features/admin/abnTypeSlice.ts";
import { useSelector } from "react-redux";
import { hasPermission } from "../../../helpers/permissions.ts";

function TariffBases() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;
    const actionRef = useRef<any>();
    const dispatch = useDispatch();
    const [form] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingTarifBase, setEditingTarifBase] = useState<any>(null);
    const [isRegular, setIsRegular] = useState(false);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);


    const abnTypes = useSelector((state: any) => state.abnType.items.data);

    /*|--------------------------------------------------------------------------
    | FETCH ALL ABN TYPES
    |-------------------------------------------------------------------------- */
    const fetchStoreData = async () => {
        if(!abnTypes?.length){
            await dispatch(getAbnTypesAll()).unwrap()
        }
    }

    useEffect(() => {
        fetchStoreData();
    }, []);

    /*|--------------------------------------------------------------------------
    | FETCH ALL TARIF BASES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetTariffBases = (params:any,sort:any,filter:any) =>
        dispatch(getTarifBases({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                 setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });

    /*|--------------------------------------------------------------------------
    |  - COLUMNS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t("manage_tariffBase.labels.name"),
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: t("manage_tariffBase.labels.tariffs"),
            dataIndex: "tariffs",
            sorter: false,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            width: 260,
            render: (_: any, record: any) => {
                if (!record.tariffs || record.tariffs.length === 0) {
                    return "-";
                }
                const latestTariff = record.tariffs[record.tariffs.length - 1];
                return (
                    <div>
                        <div className="flex gap-3 items-center ">
                            <div className="font-medium">{latestTariff.tariff} {t("common.tnd")}</div>
                            <div className="text-xs text-gray-500">
                                ({dayjs(latestTariff.date_subscription).format('DD/MM/YYYY')},
                                 {dayjs(latestTariff.date_subscription).format('DD/MM/YYYY')})
                            </div>
                        </div>
                        <div className="text-xs text-gray-500">
                            {record.tariffs.length > 1 ? `+${record.tariffs.length - 1} autres` : 'Tarif actuel'}
                        </div>
                    </div>
                );
            }
        },
        {
            title: t("manage_tariffBase.labels.types"),
            responsive: ["xs", "sm", "md", "lg"],
            dataIndex: "id_subs_type",
            render: (_: any, record: any) => {
                return (
                    <Tag color={record.subs_type?.color || "default"}>
                        {record.subs_type?.[`nom_${currentLang}`]}
                    </Tag>
                )
            },
            renderFormItem: () => (
                <Select
                    allowClear
                    placeholder={t("manage_tariffBase.filters.abn_types")}
                    options={abnTypes?.map((el:any) => ({
                        label: el[`nom_${currentLang}`],
                        value: el.id,
                    }))}
                />
            ),
        },
        {
            title: t("manage_tariffBase.labels.isRegular"),
            dataIndex: "is_regular",
            sorter: true,
            search : false,
            render: (value: any) => (
                <Tag color={value ? "success" : "error"}>
                    {value ? t("manage_tariffBase.regular") : t("manage_tariffBase.notRegular")}
                </Tag>
            )
        },
        {
            title: t("manage_tariffBase.labels.interval"),
            dataIndex: "interval",
            search: false,
            sorter: true,
            render: (_: any, record: any) => {
                return record.is_regular ? (
                    <Tag color="default">
                        {record.km_start} - {record.km_end}
                    </Tag>
                ) : '-'
            }
        },
        {
            title: t("manage_tariffBase.labels.actions"),
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_tariff_bases") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_tariff_bases") && (
                            <Popconfirm
                                title={t("manage_tariffBase.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_tariffBase.yes")}
                                cancelText={t("manage_tariffBase.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.pricing")}</Link>,
        },
        {
            title: t("manage_tariffBase.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingTarifBase(record);
        setIsRegular(record.is_regular);

        const formattedTariffs = record.tariffs?.map((tariff: any) => ({
            ...tariff,
            date_subscription: tariff.date_subscription ? dayjs(tariff.date_subscription) : null,
            date_website: tariff.date_website ? dayjs(tariff.date_website) : null
        })) || [];

        form.setFieldsValue({
            ...record,
            id_subs_type: record.subs_type?.id,
            is_regular: record.is_regular === true ? 1 : 0,
            is_triff_fixed: record.is_triff_fixed === true ? 1 : 0,
            tariffs: formattedTariffs
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingTarifBase(record);
        setIsRegular(record.is_regular);

        const formattedTariffs = record.tariffs?.map((tariff: any) => ({
            ...tariff,
            date_subscription: tariff.date_subscription ? dayjs(tariff.date_subscription) : null,
            date_website: tariff.date_website ? dayjs(tariff.date_website) : null
        })) || [];

        form.setFieldsValue({
            ...record,
            id_subs_type: record.subs_type?.id,
            is_regular: record.is_regular === true ? 1 : 0,
            is_triff_fixed: record.is_triff_fixed === true ? 1 : 0,
            tariffs: formattedTariffs
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingTarifBase(null);
        form.resetFields();
        // Initialize with one empty tariff
        form.setFieldsValue({
            tariffs: [{ tariff: '', date_subscription: null, date_website: null }]
        });
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE TARIF BASE
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);

        const formattedTariffs = values.tariffs?.map((tariff: any) => ({
            tariff: Number(tariff.tariff),
            date_subscription: tariff.date_subscription ? tariff.date_subscription.format('YYYY-MM-DD') : null,
            date_website: tariff.date_website ? tariff.date_website.format('YYYY-MM-DD') : null
        })) || [];

        const formattedValues = {
            ...values,
            is_regular: values.is_regular ? 1 : 0,
            is_triff_fixed: values.is_triff_fixed || null,
            km_start: values.km_start ? Number(values.km_start) : null,
            km_end: values.km_end ? Number(values.km_end) : null,
            tariffs: formattedTariffs
        };

        const payload = editingTarifBase
            ? { id: editingTarifBase.id, ...formattedValues }
            : formattedValues;

        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        try {
            if (editingTarifBase) {
                await dispatch(updateTarifBase(payload)).unwrap();
            } else {
                await dispatch(storeTarifBase(formattedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_tariffBase.confirmAction"),
            content: editingTarifBase
                ? t("manage_tariffBase.confirmUpdate")
                : t("manage_tariffBase.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE TARIFF BASE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteTarifBase(id)).unwrap()
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
        setLoading(false);
        setIsRegular(false);
        setEditingTarifBase(null);
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_tariffBase.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetTariffBases(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_tariffBase.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1200}
                centered={true}
                title={
                    viewMode
                        ? t("manage_tariffBase.details")
                        : editingTarifBase
                            ? t("manage_tariffBase.edit")
                            : t("manage_tariffBase.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_tariffBase.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                {!viewMode ? (
                    <Form
                        className="form-inputs"
                        form={form}
                        layout="vertical"
                        onFinish={confirmSubmit}
                        disabled={loading}
                    >

                        <Row gutter={16}>
                            <Col xs={24} sm={8}>
                                <Form.Item
                                    label={t("manage_delegations.labels.name_fr")}
                                    name="nom_fr"
                                    rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                                >
                                    <Input
                                        placeholder={t("manage_delegations.placeholders.name_fr")}
                                        disabled={viewMode}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={8}>
                                <Form.Item
                                    label={t("manage_delegations.labels.name_en")}
                                    name="nom_en"
                                    rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                                >
                                    <Input
                                        placeholder={t("manage_delegations.placeholders.name_en")}
                                        disabled={viewMode}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={8}>
                                <Form.Item
                                    label={t("manage_delegations.labels.name_ar")}
                                    name="nom_ar"
                                    rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                                >
                                    <Input
                                        placeholder={t("manage_delegations.placeholders.name_ar")}
                                        disabled={viewMode}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row gutter={16}>
                            <Col xs={24} sm={24}>
                                <Form.Item
                                    label={t("manage_tariffBase.labels.abn_type")}
                                    name="id_subs_type"
                                    rules={[{ required: true, message: t("manage_tariffBase.errors.abnTypeRequired") }]}
                                >
                                    <Select
                                        disabled={viewMode}
                                        placeholder={t("manage_tariffBase.placeholders.abn_type")}
                                    >
                                        {abnTypes?.map((el:any) => (
                                            <Select.Option key={el.id} value={el.id}>
                                                {el[`nom_${currentLang}`]}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row gutter={16}>
                            <Col xs={24} sm={8}>
                                <Form.Item
                                    label={t("manage_tariffBase.labels.isRegular")}
                                    name="is_regular"
                                    valuePropName="checked"
                                >
                                    <Switch
                                        disabled={viewMode}
                                        onChange={(checked) => setIsRegular(checked)}
                                    />
                                </Form.Item>
                            </Col>
                            {
                                isRegular && (
                                   <>
                                    <Col xs={24} sm={8}>
                                        <Form.Item
                                            label={t("manage_tariffBase.labels.km_start")}
                                            name="km_start"
                                            rules={[{ required: isRegular, message: t("manage_tariffBase.errors.kmStartRequired") }]}
                                        >
                                            <Input
                                                placeholder={t("manage_tariffBase.placeholders.km_start")}
                                                disabled={viewMode}
                                                style={{ width: '100%' }}
                                                min={0}
                                                type="number"
                                            />
                                        </Form.Item>
                                    </Col>
                                     <Col xs={24} sm={8}>
                                        <Form.Item
                                            label={t("manage_tariffBase.labels.km_end")}
                                            name="km_end"
                                            rules={[{ required: isRegular, message: t("manage_tariffBase.errors.kmEndRequired") }]}
                                        >
                                            <Input
                                                placeholder={t("manage_tariffBase.placeholders.km_end")}
                                                disabled={viewMode}
                                                style={{ width: '100%' }}
                                                min={0}
                                                type="number"
                                            />
                                        </Form.Item>
                                    </Col>
                                   </>
                                )
                            }
                        </Row>

                        {
                            isRegular && (
                                <Row gutter={16}>
                                    <Col xs={24} sm={24}>
                                        <Form.Item
                                            label={t("manage_tariffBase.labels.isTariffFixed")}
                                            name="is_triff_fixed"
                                            rules={[{ required: isRegular, message: t("manage_tariffBase.errors.tariffFixedRequired") }]}
                                        >
                                             <Select
                                                disabled={viewMode}
                                                placeholder={t("manage_tariffBase.placeholders.isTariffFixed")}
                                            >
                                                <Select.Option value={0}>{t("manage_tariffBase.notFixed")}</Select.Option>
                                                <Select.Option value={1}>{t("manage_tariffBase.fixed")}</Select.Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                </Row>
                            )
                        }

                        <Row gutter={16}>
                            <Col xs={24}>
                                <Form.Item
                                    label={t("manage_tariffBase.labels.tariffs")}
                                    required
                                >
                                    <Form.List
                                        name="tariffs"
                                        rules={[
                                            {
                                                validator: async (_, tariffs) => {
                                                    if (!tariffs || tariffs.length < 1) {
                                                        return Promise.reject(new Error('Au moins un tarif est requis'));
                                                    }
                                                },
                                            },
                                        ]}
                                    >
                                        {(fields, { add, remove }, { errors }) => (
                                            <>
                                                {fields.map(({ key, name, ...restField }) => (
                                                    <Card
                                                        key={key}
                                                        size="small"
                                                        className="mb-4"
                                                        title={`Tarif ${name + 1}`}
                                                        extra={
                                                            fields.length > 1 && !viewMode ? (
                                                                <Button
                                                                    type="link"
                                                                    danger
                                                                    icon={<MinusCircleOutlined />}
                                                                    onClick={() => remove(name)}
                                                                >
                                                                    Supprimer
                                                                </Button>
                                                            ) : null
                                                        }
                                                    >
                                                        <Row gutter={16}>
                                                            <Col xs={24} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, 'tariff']}
                                                                    label={t("manage_tariffBase.labels.tariff")}
                                                                    rules={[
                                                                        { required: true, message: t("manage_tariffBase.errors.tariffRequired") }
                                                                    ]}
                                                                >
                                                                    <Input
                                                                        placeholder={t("manage_tariffBase.placeholders.tariff")}
                                                                        disabled={viewMode}
                                                                        type="number"
                                                                        min={0}
                                                                        step={0.001}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            <Col xs={24} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, 'date_subscription']}
                                                                    label={t("manage_tariffBase.labels.dateSubscription")}
                                                                    rules={[
                                                                        { required: true, message: t("manage_tariffBase.errors.dateSubscriptionRequired") }
                                                                    ]}
                                                                >
                                                                    <DatePicker
                                                                        style={{ width: '100%' }}
                                                                        format="YYYY-MM-DD"
                                                                        disabled={viewMode}
                                                                        placeholder={t("manage_tariffBase.placeholders.dateSubscription")}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            <Col xs={24} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, 'date_website']}
                                                                    label={t("manage_tariffBase.labels.dateWebsite")}
                                                                    rules={[
                                                                        { required: true, message: t("manage_tariffBase.errors.dateWebsiteRequired") }
                                                                    ]}
                                                                >
                                                                    <DatePicker
                                                                        style={{ width: '100%' }}
                                                                        format="YYYY-MM-DD"
                                                                        disabled={viewMode}
                                                                        placeholder={t("manage_tariffBase.placeholders.dateWebsite")}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                    </Card>
                                                ))}
                                                {!viewMode && (
                                                    <Form.Item>
                                                        <Button
                                                            type="dashed"
                                                            onClick={() => add()}
                                                            block
                                                            icon={<PlusOutlined />}
                                                        >
                                                            Ajouter un tarif
                                                        </Button>
                                                        <Form.ErrorList errors={errors} />
                                                    </Form.Item>
                                                )}
                                            </>
                                        )}
                                    </Form.List>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form>
                ) : (
                    <div className="p-4">
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={8}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_delegations.labels.name_fr")}
                                    </div>
                                    <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingTarifBase?.nom_fr || "-"}
                                    </div>
                                </div>
                            </Col>
                            <Col xs={24} sm={8}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_delegations.labels.name_en")}
                                    </div>
                                    <div className="bg-purple-50 text-purple-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingTarifBase?.nom_en || "-"}
                                    </div>
                                </div>
                            </Col>
                            <Col xs={24} sm={8}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_delegations.labels.name_ar")}
                                    </div>
                                    <div className="bg-emerald-50 text-emerald-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingTarifBase?.nom_ar || "-"}
                                    </div>
                                </div>
                            </Col>
                        </Row>

                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={24}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_tariffBase.labels.abn_type")}
                                    </div>
                                    <div className="bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                                        <Tag color={editingTarifBase?.subs_type?.color || "default"}>
                                            {editingTarifBase?.subs_type?.[`nom_${currentLang}`] || "-"}
                                        </Tag>
                                    </div>
                                </div>
                            </Col>
                        </Row>

                        <Row gutter={[16, 16]}>
                            <Col xs={24}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-2">
                                        {t("manage_tariffBase.labels.tariffs")}
                                    </div>
                                    {editingTarifBase?.tariffs && editingTarifBase.tariffs.length > 0 ? (
                                        <div className="space-y-3">
                                            {editingTarifBase.tariffs.map((tariff: any, index: number) => (
                                                <Card key={index} size="small" className="bg-gray-50">
                                                    <Row gutter={16}>
                                                        <Col xs={24} sm={8}>
                                                            <div className="mb-2">
                                                                <div className="text-xs font-medium text-gray-500 mb-1">
                                                                    {t("manage_tariffBase.labels.tariff")}
                                                                </div>
                                                                <div className="bg-amber-50 text-amber-700 px-2 py-1 rounded text-sm font-medium">
                                                                    {tariff.tariff} TND
                                                                </div>
                                                            </div>
                                                        </Col>
                                                        <Col xs={24} sm={8}>
                                                            <div className="mb-2">
                                                                <div className="text-xs font-medium text-gray-500 mb-1">
                                                                    {t("manage_tariffBase.labels.dateSubscription")}
                                                                </div>
                                                                <div className="bg-pink-50 text-pink-700 px-2 py-1 rounded text-sm">
                                                                    {tariff.date_subscription ? dayjs(tariff.date_subscription).format('YYYY-MM-DD') : "-"}
                                                                </div>
                                                            </div>
                                                        </Col>
                                                        <Col xs={24} sm={8}>
                                                            <div className="mb-2">
                                                                <div className="text-xs font-medium text-gray-500 mb-1">
                                                                    {t("manage_tariffBase.labels.dateWebsite")}
                                                                </div>
                                                                <div className="bg-cyan-50 text-cyan-700 px-2 py-1 rounded text-sm">
                                                                    {tariff.date_website ? dayjs(tariff.date_website).format('YYYY-MM-DD') : "-"}
                                                                </div>
                                                            </div>
                                                        </Col>
                                                    </Row>
                                                </Card>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-gray-500">Aucun tarif défini</div>
                                    )}
                                </div>
                            </Col>
                        </Row>

                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={8}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_tariffBase.labels.isRegular")}
                                    </div>
                                    <div className="bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                                        <Tag color={editingTarifBase?.is_regular ? "success" : "error"}>
                                            {editingTarifBase?.is_regular ? t("manage_tariffBase.regular") : t("manage_tariffBase.notRegular")}
                                        </Tag>
                                    </div>
                                </div>
                            </Col>
                            {editingTarifBase?.is_regular && (
                                <>
                                    <Col xs={24} sm={8}>
                                        <div className="mb-4">
                                            <div className="text-sm font-medium text-gray-500 mb-1">
                                                {t("manage_tariffBase.labels.interval")}
                                            </div>
                                            <div className="bg-indigo-50 text-indigo-700 px-3 py-2 rounded-md text-base font-medium">
                                                {editingTarifBase?.km_start !== null && editingTarifBase?.km_end !== null
                                                    ? `${editingTarifBase.km_start} - ${editingTarifBase.km_end} km`
                                                    : "-"
                                                }
                                            </div>
                                        </div>
                                    </Col>
                                    <Col xs={24} sm={8}>
                                        <div className="mb-4">
                                            <div className="text-sm font-medium text-gray-500 mb-1">
                                                {t("manage_tariffBase.labels.isTariffFixed")}
                                            </div>
                                            <div className="bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                                                <Tag color={editingTarifBase?.is_triff_fixed ? "success" : "warning"}>
                                                    {editingTarifBase?.is_triff_fixed ? t("manage_tariffBase.fixed") : t("manage_tariffBase.notFixed")}
                                                </Tag>
                                            </div>
                                        </div>
                                    </Col>
                                </>
                            )}
                        </Row>


                    </div>
                )}
            </Modal>
        </>
    );
}

export default TariffBases;










