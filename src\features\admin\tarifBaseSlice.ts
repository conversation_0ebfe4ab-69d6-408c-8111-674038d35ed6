import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/tariff-bases';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};
export const getTarifBasesAll: any = createAsyncThunk(
    "getTarifBasesAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTarifBases: any = createAsyncThunk(
    "getTarifBases",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any,
            sort: any,
            filter: any
        },
        thunkAPI:any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar, tariff, id_subs_type, date_subscription, date_commercial, is_regular } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (tariff) {
                searchParams.push(`tariff:${tariff}`);
            }
            if (id_subs_type) {
                searchParams.push(`id_subs_type:${id_subs_type}`);
            }
            if (date_subscription) {
                searchParams.push(`date_subscription:${date_subscription}`);
            }
            if (date_commercial) {
                searchParams.push(`date_commercial:${date_commercial}`);
            }
            if (is_regular !== undefined && is_regular !== null) {
                searchParams.push(`is_regular:${is_regular}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeTarifBase: any = createAsyncThunk(
    "storeTarifBase",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateTarifBase: any = createAsyncThunk(
    "updateTarifBase",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteTarifBase: any = createAsyncThunk(
    "deleteTarifBase",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const tarifBaseSlice = createSlice({
    name: 'tarifBase',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getTarifBaseAll
            .addCase(getTarifBasesAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTarifBasesAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTarifBasesAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getTarifBases
            .addCase(getTarifBases.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTarifBases.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getTarifBases.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // storeTarifBase
            .addCase(storeTarifBase.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeTarifBase.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(storeTarifBase.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // updateTarifBase
            .addCase(updateTarifBase.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateTarifBase.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateTarifBase.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // deleteTarifBase
            .addCase(deleteTarifBase.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteTarifBase.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deleteTarifBase.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = tarifBaseSlice.actions;
export default tarifBaseSlice.reducer;