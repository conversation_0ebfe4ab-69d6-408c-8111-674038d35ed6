import { useEffect, useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col, 
    Select,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {toast} from "react-toastify";
import {
    deleteSchoolDegree,
    getSchoolDegrees,
    storeSchoolDegree,
    updateSchoolDegree,
} from "../../../features/admin/schoolDegreeSlice.ts";
import { getEstablishmentTypeAll } from "../../../features/admin/establishmentTypeSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageSchoolDegrees() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;
    const dispatch:any = useDispatch();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingSchoolDegree, setEditingSchoolDegree] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);
    const [establishmentTypes, setEstablishmentTypes] = useState([]);

    /*|--------------------------------------------------------------------------
    | FETCH ALL SCHOOl DEGREES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetSchoolDegrees = (params:any, sort:any, filter:any) => {
        return dispatch(getSchoolDegrees({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    /*|--------------------------------------------------------------------------
    | FETCH ALL ESTABLISHMENT TYPES
    |-------------------------------------------------------------------------- */
    const fetchEstablishmentTypes = async () => {
        try {
            const response = await dispatch(getEstablishmentTypeAll()).unwrap();
            setEstablishmentTypes(response.data);
        } catch (error) {
            console.error('Error fetching establishment types:', error);
        }
    };
    useEffect(() => {
        fetchEstablishmentTypes();
    }, []);

    /*|--------------------------------------------------------------------------
    | VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t(`manage_schoolDegrees.labels.name`),
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_schoolDegrees.labels.establishmentType")}`,
            dataIndex: "id_type_establishment",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.type_establishment[`nom_${currentLang}`] || "-",
            renderFormItem: () => (
                <Select
                    allowClear
                    placeholder={t("manage_schoolDegrees.filters.establishmentType")}
                    options={establishmentTypes.map((el:any) => ({
                        label: el[`nom_${currentLang}`],
                        value: el.id,
                    }))}
                />
            ),
        },
        {
            title: `${t("manage_schoolDegrees.labels.ageMax")}`,
            dataIndex: "age_max",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.age_max,
        },
        {
            title: `${t("manage_schoolDegrees.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_schoolDegrees.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_school_degrees") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_school_degrees") && (
                            <Popconfirm
                                title={t("manage_schoolDegrees.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_schoolDegrees.yes")}
                                cancelText={t("manage_schoolDegrees.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems:any = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.education")}</Link>,
        },
        {
            title: t("manage_schoolDegrees.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingSchoolDegree(record);
        form.setFieldsValue({
            ...record,
            id_type_establishment: record.type_establishment?.id,
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingSchoolDegree(record);
        form.setFieldsValue({
            ...record,
            id_type_establishment: record.type_establishment?.id,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingSchoolDegree(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE School Degree
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = editingSchoolDegree ? { id: editingSchoolDegree.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingSchoolDegree) {
                await dispatch(updateSchoolDegree(payload)).unwrap();
            } else {
                await dispatch(storeSchoolDegree(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_schoolDegrees.confirmAction"),
            content: editingSchoolDegree
                ? t("manage_schoolDegrees.confirmUpdate")
                : t("manage_schoolDegrees.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };


    /*|--------------------------------------------------------------------------
    |  - DELETE School Degree
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteSchoolDegree(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setViewMode(false);
        form.resetFields();
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <Row>
                <Col span={24}>
                    <ProTable
                        className="component-modern"
                        headerTitle={t("manage_schoolDegrees.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true);
                            const dataFilter:any = await handleGetSchoolDegrees(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_schoolDegrees.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_schoolDegrees.details")
                        : editingSchoolDegree
                            ? t("manage_schoolDegrees.edit")
                            : t("manage_schoolDegrees.add")
                }
                open={modalVisible}
                onCancel={handleReset}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_schoolDegrees.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    {editingSchoolDegree && (
                        <Form.Item
                            label={t("manage_schoolDegrees.labels.id")}
                            name="id"
                        >
                            <Input disabled={true} />
                        </Form.Item>
                    )}

                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_schoolDegrees.labels.ageMax")}
                                name="age_max"
                                rules={[{ required: true, message: t("manage_schoolDegrees.errors.ageMaxRequired") }]}
                            >
                                <Input
                                    type="number"
                                    placeholder={t("manage_schoolDegrees.placeholders.ageMax")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_schoolDegrees.labels.establishmentType")}
                                name="id_type_establishment"
                                rules={[{ required: true, message: t("manage_schoolDegrees.errors.establishmentTypeRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_schoolDegrees.placeholders.establishmentType")}
                                    disabled={viewMode}
                                >
                                    {establishmentTypes.map((el:any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageSchoolDegrees;
