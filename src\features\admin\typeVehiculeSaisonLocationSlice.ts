import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/type-vehicule-saison-locations";


const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getTypeVehiculeSaisonLocationsAll: any = createAsyncThunk(
    "getTypeVehiculeSaisonLocationsAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehiculeSaisonLocationsByVehicleType: any = createAsyncThunk(
    "getTypeVehiculeSaisonLocationsByVehicleType",
    async (typeVehiculeId: number, thunkAPI:any) => {
        try {
            const url:string = `${URL}/type-vehicule/${typeVehiculeId}`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehiculeSaisonLocations: any = createAsyncThunk(
    "getTypeVehiculeSaisonLocations",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { id_type_vehicule, id_saison_location, prix_km, status } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (id_type_vehicule) {
                searchParams.push(`id_type_vehicule:${id_type_vehicule}`);
            }
            if (id_saison_location) {
                searchParams.push(`id_saison_location:${id_saison_location}`);
            }
            if (prix_km) {
                searchParams.push(`prix_km:${prix_km}`);
            }
            if (status !== undefined) {
                searchParams.push(`status:${status}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehiculeSaisonLocationById: any = createAsyncThunk(
    "getTypeVehiculeSaisonLocationById",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeTypeVehiculeSaisonLocation: any = createAsyncThunk(
    "storeTypeVehiculeSaisonLocation",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateTypeVehiculeSaisonLocation: any = createAsyncThunk(
    "updateTypeVehiculeSaisonLocation",
    async (data: any, thunkAPI: any) => {
        try {
            const { id, ...payload } = data;
            const url: string = `${URL}/${id}`;
            const resp: any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteTypeVehiculeSaisonLocation: any = createAsyncThunk(
    "deleteTypeVehiculeSaisonLocation",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const bulkStoreTypeVehiculeSaisonLocation: any = createAsyncThunk(
    "bulkStoreTypeVehiculeSaisonLocation",
    async (data: any[], thunkAPI: any) => {
        try {
            const url: string = `${URL}/bulk`;
            const resp: any = await api.post(url, { items: data });
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const typeVehiculeSaisonLocationSlice = createSlice({
    name: 'typeVehiculeSaisonLocation',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getTypeVehiculeSaisonLocationsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehiculeSaisonLocationsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTypeVehiculeSaisonLocationsAll.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = typeVehiculeSaisonLocationSlice.actions;
export default typeVehiculeSaisonLocationSlice.reducer;