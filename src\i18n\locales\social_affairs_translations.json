{"en": {"social_affairs": {"title": "Social Affairs", "title_with_governorate": "Social Affairs - {{governorate}}", "list_title": "Social Affairs List", "add": "Add Social Affair", "edit": "Edit Social Affair", "details": "Social Affair Details", "save": "Save", "manage": "Manage Social Affairs", "import": "Import", "uploading": "Uploading...", "start_upload": "Start Upload", "select_file": "Select File", "download_template": "Download Template", "supported_formats": "Supported formats: .csv, .xls, .xlsx", "import_success": "Social affairs imported successfully!", "import_instructions_title": "Import Instructions", "import_instructions_1": "1. Download the template file to see the required format.", "import_instructions_2": "2. Fill in the data according to the template.", "import_instructions_3": "3. Upload the file to import the data.", "template_download_info": "Template download functionality will be available soon.", "file_type_error": "You can only upload CSV or Excel files!", "confirmDelete": "Are you sure you want to delete this social affair?", "yes": "Yes", "no": "No", "labels": {"governorate": "Governorate", "delegation": "Delegation", "eleve_etudiant": "Student", "societe": "Company", "nom_parent": "Parent Name", "cin_parent": "Parent CIN", "telephone": "Phone", "nom_complet": "Full Name", "niveau_etude": "Education Level", "trajet_requise": "Required Route", "academic_year": "Academic Year", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions"}, "placeholders": {"delegation": "Select delegation", "nom_parent": "Enter parent name", "cin_parent": "Enter parent CIN", "telephone": "Enter phone number", "nom_complet": "Enter full name", "niveau_etude": "Enter education level", "trajet_requise": "Enter required route", "academic_year": "Select academic year"}, "errors": {"delegationRequired": "Delegation is required", "nomParentRequired": "Parent name is required", "cinParentRequired": "Parent CIN is required", "cinParentInvalid": "Parent CIN must be 8 digits", "telephoneRequired": "Phone number is required", "telephoneInvalid": "Phone number must be 8 digits", "nomCompletRequired": "Full name is required", "niveauEtudeRequired": "Education level is required", "trajetRequiseRequired": "Required route is required"}}}, "fr": {"social_affairs": {"title": "Affaires Sociales", "title_with_governorate": "Affaires Sociales - {{governorate}}", "list_title": "Liste des Affaires Sociales", "add": "Ajouter une Affaire Sociale", "edit": "Modifier l'Affaire Sociale", "details": "Détails de l'Affaire Sociale", "save": "Enregistrer", "manage": "<PERSON><PERSON>rer les Affaires Sociales", "import": "Importer", "uploading": "Téléchargement...", "start_upload": "Commencer le Téléchargement", "select_file": "Sélectionner un Fichier", "download_template": "Télécharger le Modèle", "supported_formats": "Formats pris en charge: .csv, .xls, .xlsx", "import_success": "Affaires sociales importées avec succès!", "import_instructions_title": "Instructions d'Importation", "import_instructions_1": "1. T<PERSON><PERSON>cha<PERSON><PERSON> le fichier modèle pour voir le format requis.", "import_instructions_2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> les données selon le modèle.", "import_instructions_3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le fichier pour importer les données.", "template_download_info": "La fonctionnalité de téléchargement de modèle sera bientôt disponible.", "file_type_error": "Vous ne pouvez télécharger que des fichiers CSV ou Excel!", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette affaire sociale?", "yes": "O<PERSON>", "no": "Non", "types": {"eleve": "É<PERSON>è<PERSON>", "etudiant": "Étudiant"}, "labels": {"governorate": "Gouvernorat", "delegation": "Délégation", "eleve_etudiant": "Élève / Étudiant", "societe": "Société", "nom_parent": "Nom du Parent", "cin_parent": "CIN du Parent", "telephone": "Téléphone", "nom_complet": "Nom Co<PERSON>t", "niveau_etude": "Niveau d'Étude", "trajet_requise": "<PERSON><PERSON><PERSON>", "academic_year": "<PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "actions": "Actions"}, "placeholders": {"delegation": "Sélectionner une délégation", "nom_parent": "Entrer le nom du parent", "cin_parent": "Entrer le CIN du parent", "telephone": "Entrer le numéro de téléphone", "nom_complet": "Entrer le nom complet", "niveau_etude": "Entrer le niveau d'étude", "trajet_requise": "Entrer le trajet requis", "academic_year": "Sélectionner l'année académique", "societe": "Entrer le nom de la société"}, "errors": {"delegationRequired": "La délégation est requise", "nomParentRequired": "Le nom du parent est requis", "cinParentRequired": "Le CIN du parent est requis", "cinParentInvalid": "Le CIN du parent doit comporter 8 chiffres", "telephoneRequired": "Le numéro de téléphone est requis", "telephoneInvalid": "Le numéro de téléphone doit comporter 8 chiffres", "nomCompletRequired": "Le nom complet est requis", "niveauEtudeRequired": "Le niveau d'étude est requis", "trajetRequiseRequired": "Le trajet requis est requis"}}}, "ar": {"social_affairs": {"title": "الشؤون الاجتماعية", "title_with_governorate": "الشؤون الاجتماعية - {{governorate}}", "list_title": "قائمة الشؤون الاجتماعية", "add": "إضافة شأن اجتماعي", "edit": "تعديل الشأن الاجتماعي", "details": "تفاصيل الشأن الاجتماعي", "save": "<PERSON><PERSON><PERSON>", "manage": "إدارة الشؤون الاجتماعية", "import": "استيراد", "uploading": "جاري التحميل...", "start_upload": "بدء التحميل", "select_file": "اختيار ملف", "download_template": "تنزيل النموذج", "supported_formats": "الصيغ المدعومة: .csv, .xls, .xlsx", "import_success": "تم استيراد الشؤون الاجتماعية بنجاح!", "import_instructions_title": "تعليمات الاستيراد", "import_instructions_1": "1. قم بتنزيل ملف النموذج لمعرفة التنسيق المطلوب.", "import_instructions_2": "2. املأ البيانات وفقًا للنموذج.", "import_instructions_3": "3. قم بتحميل الملف لاستيراد البيانات.", "template_download_info": "ستتوفر وظيفة تنزيل النموذج قريبًا.", "file_type_error": "يمكنك فقط تحميل ملفات CSV أو Excel!", "confirmDelete": "هل أنت متأكد من رغبتك في حذف هذا الشأن الاجتماعي؟", "yes": "نعم", "no": "لا", "labels": {"governorate": "المحافظة", "delegation": "المعتمدية", "eleve_etudiant": "تلميذ/طالب", "societe": "شركة", "nom_parent": "اسم الولي", "cin_parent": "بطاقة تعريف الولي", "telephone": "الهاتف", "nom_complet": "الاسم الكامل", "niveau_etude": "المستوى التعليمي", "trajet_requise": "المسار المطلوب", "academic_year": "السنة الدراسية", "createdAt": "تاريخ الإنشاء", "updatedAt": "تاريخ التحديث", "actions": "الإجراءات"}, "placeholders": {"delegation": "اختر المعتمدية", "nom_parent": "أد<PERSON>ل اسم الولي", "cin_parent": "أدخل بطاقة تعريف الولي", "telephone": "أدخل رقم الهاتف", "nom_complet": "أد<PERSON>ل الاسم الكامل", "niveau_etude": "أد<PERSON>ل المستوى التعليمي", "trajet_requise": "أد<PERSON>ل المسار المطلوب", "academic_year": "اختر السنة الدراسية"}, "errors": {"delegationRequired": "المعتمدية مطلوبة", "nomParentRequired": "اسم الولي مطلوب", "cinParentRequired": "بطاقة تعريف الولي مطلوبة", "cinParentInvalid": "يجب أن تتكون بطاقة تعريف الولي من 8 أرقام", "telephoneRequired": "رقم الها<PERSON><PERSON> مطلوب", "telephoneInvalid": "يج<PERSON> أن يتكون رقم الهاتف من 8 أرقام", "nomCompletRequired": "الاسم الكامل مطلوب", "niveauEtudeRequired": "المستوى التعليمي مطلوب", "trajetRequiseRequired": "المسار المطلوب مطلوب"}}}}