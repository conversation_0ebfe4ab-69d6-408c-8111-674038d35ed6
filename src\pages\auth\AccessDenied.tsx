import { Button, Result } from "antd";
import {useTranslation} from "react-i18next";
import {assets} from "../../assets/assets.ts";

function AccessDenied() {
    const {t} = useTranslation();

    const handleGoHome = () => {
        window.location.href="/";
    };

    return (
        <div className="flex items-center justify-center min-h-[80vh] bg-white">
            <Result
                status="error"
                title={t('access_denied.title')}
                subTitle={t('access_denied.sub_title')}
                icon={
                    <div className="flex justify-center items-center">
                        <img src={assets.accessDenied} alt={t('access_denied.title')}
                             className="w-1/3 text-center"/>
                    </div>
                }
                extra={
                    <Button
                        type="primary"
                        onClick={handleGoHome}
                        className="bg-red-500 border-red-500 text-white hover:bg-red-600 hover:border-red-600 font-bold py-2 px-6"
                    >
                        {t('access_denied.button')}
                    </Button>
                }
            />
        </div>
    );
}

export default AccessDenied;
