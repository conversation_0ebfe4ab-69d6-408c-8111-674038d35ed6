import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/type-vehicules";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getTypeVehiculesAll: any = createAsyncThunk(
    "getTypeVehiculesAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehicules: any = createAsyncThunk(
    "getTypeVehicules",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { nom_fr, nom_ar, nom_en, nbre_max_place, status } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nbre_max_place) {
                searchParams.push(`nbre_max_place:${nbre_max_place}`);
            }
            if (status !== undefined) {
                searchParams.push(`status:${status}`);
            }
        
            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehiculeById: any = createAsyncThunk(
    "getTypeVehiculeById",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeTypeVehicule: any = createAsyncThunk(
    "storeTypeVehicule",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateTypeVehicule: any = createAsyncThunk(
    "updateTypeVehicule",
    async (data: any, thunkAPI: any) => {
        try {
            const { id, ...payload } = data;
            const url: string = `${URL}/${id}`;
            const resp: any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteTypeVehicule: any = createAsyncThunk(
    "deleteTypeVehicule",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const typeVehiculeSlice = createSlice({
    name: 'typeVehicule',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getTypeVehiculesAll
            .addCase(getTypeVehiculesAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehiculesAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTypeVehiculesAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getTypeVehicules
            .addCase(getTypeVehicules.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehicules.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getTypeVehicules.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getTypeVehiculeById
            .addCase(getTypeVehiculeById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehiculeById.fulfilled, (state, action) => {
                state.loading = false;
                state.currentItem = action.payload;
            })
            .addCase(getTypeVehiculeById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // storeTypeVehicule
            .addCase(storeTypeVehicule.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeTypeVehicule.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(storeTypeVehicule.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // updateTypeVehicule
            .addCase(updateTypeVehicule.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateTypeVehicule.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateTypeVehicule.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // deleteTypeVehicule
            .addCase(deleteTypeVehicule.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteTypeVehicule.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deleteTypeVehicule.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = typeVehiculeSlice.actions;
export default typeVehiculeSlice.reducer;