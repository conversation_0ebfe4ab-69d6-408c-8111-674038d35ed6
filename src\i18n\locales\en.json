{"messages": {"success": "Success", "error": "An error occurred", "loading": "Loading...", "code_invalide": "يرجى التحقق من الرمز المُدخل، الرمز صالح لمدة ساعتين فقط."}, "components": {"sellsPeriodClosed": {"noOpenPeriod": "No sales period is currently open!", "reopening": "Our subscription space reopens in:", "days": "days", "description": "You cannot create new subscriptions at this time because there is no active sales period.", "nextOpening": "Next opening date", "contactAdmin": "Please contact an administrator for more information."}}, "login": {"welcome": "Welcome back !", "subtitle": "Sign in to your account", "tooltip": "Choose your preferred login method", "email": "Email", "phone": "Phone", "cin": "ID Card", "password": "Password", "signIn": "Sign In", "forgotPassword": "Forgot Password?", "or": "OR", "noAccount": "Don't have an account?", "signUp": "Sign up now", "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Phone number must contain 8 digits", "cin": "ID number must contain 8 digits"}, "placeholder": {"email": "Enter your email", "phone": "Enter your phone number", "cin": "Enter your ID number", "password": "Enter your password", "captcha": "Enter cap<PERSON>a"}}, "register": {"welcome": "Welcome !", "step1Title": "Personal Information", "step2Title": "Phone Verification", "step3Title": "Address", "step4Title": "Account Details", "step5Title": "Email Verification", "step6Title": "Terms and Conditions", "subtitle": "Create an account to get started.", "tooltip": "Enter your details carefully.", "placeholder": {"email": "Email address", "password": "Password", "confirmPassword": "Confirm Password", "nom": "Last Name", "prenom": "First Name", "telephone": "Phone Number", "adresse": "Address", "cin": "CIN"}, "validation": {"required": "This field is required.", "email": "Please enter a valid email address.", "phone": "Phone number must be 8 digits", "cin": "CIN must be 8 digits", "passwordMismatch": "Passwords do not match.", "acceptTerms": "You must accept the terms and conditions."}, "acceptTerms": "I accept the", "termsLink": "terms and conditions", "next": "Next", "previous": "Back", "signUp": "Sign Up", "or": "or", "haveAccount": "Already have an account?", "signIn": "Sign In", "resendCode": "Resend code", "phoneValidated": "Phone number validated", "emailValidated": "Email validated", "termsAgreement": "I agree to the", "retryIn": "<PERSON><PERSON><PERSON><PERSON> dans", "seconds": "secondes", "OTPPhoneTitle": "Enter the OTP Code", "OTPPhoneDescription": "Please enter the OTP code sent to your phone number", "OTPEmailTitle": "Enter the OTP Code", "OTPEmailDescription": "Please enter the OTP code sent to your email address"}, "reset": {"requestTitle": "Reset Your Password", "requestDescription": "Enter your email to receive a reset code and follow the steps.", "step1Title": "<PERSON><PERSON>", "step2Title": "Verify OTP", "step3Title": "Set New Password", "emailPlaceholder": "Enter your email", "resendCode": "Resend Code", "retryIn": "Retry in", "seconds": "seconds", "haveAccount": "Already have an account?", "signIn": "Sign In", "next": "Next", "previous": "Previous", "confirm": "Confirm", "validation": {"emailRequired": "Please enter your email.", "emailValid": "Please enter a valid email address.", "invalidOtp": "Invalid OTP code.", "passwordRequired": "Please enter your password.", "passwordLength": "Password must be at least 8 characters.", "confirmRequired": "Please confirm your password.", "passwordMatch": "Passwords do not match."}, "EmailValidated": "Email has been successfully validated.", "newPasswordTitle": "Set Your New Password", "newPasswordDescription": "Enter and confirm your new password.", "passwordPlaceholder": "New Password", "confirmPlaceholder": "Confirm Password", "OTPEmailTitle": "Enter the OTP Code", "OTPEmailDescription": "We have sent an OTP code to your email. Please enter it below to verify your email."}, "access_denied": {"title": "Access Denied", "sub_title": "Sorry, you do not have permission to access this page.", "button": "Return to Home"}, "not_found": {"title": "Page Not Found", "sub_title": "Sorry, the page you visited does not exist.", "button": "Back to Home"}, "auth_header": {"profile": "Profile", "settings": "Settings", "logout": "Logout"}, "auth_sidebar": {"dashboard": "Dashboard", "roles_permissions": "Roles and Permissions", "settings": "Settings", "users_section": "Manage Users", "settings_section": "Subscription Settings", "manage_admins": "Manage Admins", "manage_clients": "Manage Clients", "manage_governorates": "Manage Governorates", "manage_delegations": "Manage Delegations", "manage_stations": "Manage stops", "manage_routes": "Manage routes", "manage_lines": "Manage Lines", "manage_establishments": "Manage Establishments", "manage_establishmentTypes": "Manage Establishment Types", "manage_schoolDegrees": "Manage School Degrees", "categories": {"security": "Security", "clients": "Clients", "location": "Location", "education": "Education", "transport": "Transport", "sales": "Sales", "pricing": "Pricing", "subscriptions": "Subscriptions"}, "manage_academicYears": "Manage Academic Years", "manage_abnTypes": "Manage Subscription Types", "manage_abnSubTypes": "Manage Subscription Subtypes", "manage_baseTariff": "Manage Base Tariff", "manage_seasons": "Manage Seasons", "manage_location_seasons": "Manage Location Seasons", "manage_vehicle_season_pricing": "Vehicle Season Pricing", "manage_typeVehicules": "Manage Vehicle Types", "manage_locationTypes": "Manage Location Types", "manage_typeVehicleTypeLocations": "Vehicle Location Types", "manage_configs": "System Configurations", "manage_options": "Options"}, "dashboard": {"Statistiques_A1": "Statistics A1", "Statistiques_A2": "Statistics A2", "active_plans": "Active Plans", "expiring": "Expiring Soon", "revenue": "Revenue", "total_subscribers": "Total Subscribers", "total_clients": "Total Clients", "monthly": "Monthly", "pending_subscriptions": "Pending Subscriptions", "paid_subscriptions": "Paid Subscriptions", "summary": "Summary", "subscriptions": "Subscriptions", "financial": "Financial", "cards": "Cards", "clients": "Clients", "trips": "Trips", "agents": "Agents", "date_range": "Date Range", "group_by": "Group By", "day": "Day", "week": "Week", "month": "Month", "year": "Year", "loading": "Loading...", "unknown": "Unknown", "paid": "Paid", "not_paid": "Not Paid", "canceled": "Canceled", "pending": "Pending", "subscription_by_status": "Subscriptions by Status", "subscription_by_type": "Subscriptions by Type", "subscription_by_periodicity": "Subscriptions by Periodicity", "subscription_by_client_type": "Subscriptions by Client Type", "subscription_by_governorate": "Subscriptions by Governorate", "subscriptions_over_time": "Subscriptions Over Time", "revenue_over_time": "Revenue Over Time", "revenue_by_payment_method": "Revenue by Payment Method", "revenue_by_subscription_type": "Revenue by Subscription Type", "revenue_by_sale_point": "Revenue by Sale Point", "revenue_by_governorate": "Revenue by Governorate", "cards_by_type": "Cards by Type", "cards_by_motif": "Cards by Reason", "cards_by_sale_point": "Cards by Sale Point", "card_stock_status": "Card Stock Status", "new_clients_over_time": "New Clients Over Time", "clients_by_type": "Clients by Type", "clients_by_governorate": "Clients by Governorate", "popular_trips": "Popular Trips", "popular_lines": "Popular Lines", "station_usage": "Station Usage", "subscriptions_by_agent": "Subscriptions by Agent", "revenue_by_agent": "Revenue by Agent", "cards_by_agent": "Cards by Agent", "reset_filters": "Reset Filters", "filters_reset": "Filters reset"}, "manage_users": {"client": {"title": "Manage Clients", "add": "Add Client", "edit": "Edit Client", "details": "Client Details", "placeholders": {"lastname": "<PERSON><PERSON>'s name", "firstname": "<PERSON><PERSON>'s first name", "phone": "Phone", "cin": "CIN", "address": "Full address of the client", "email": "Client's email"}, "confirmDelete": "Are you sure you want to delete this client?", "deleteSuccess": "Client deleted successfully!"}, "manage_locationSeasons": {"title": "Manage Location Seasons", "add": "Add Location Season", "edit": "Edit Location Season", "details": "Location Season Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this location season?", "confirmUpdate": "Are you sure you want to update this location season?", "confirmDelete": "Are you sure you want to delete this location season?", "yes": "Yes", "no": "No", "sections": {"basicInfo": "Basic Information", "dateRange": "Date Range"}, "labels": {"id": "ID", "name": "Name", "name_fr": "French Name", "name_en": "English Name", "name_ar": "Arabic Name", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name"}, "errors": {"nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "endDateAfterStartDate": "End date must be after start date", "startDateNotPast": "Start date cannot be in the past"}}, "manage_typeVehiculeSaisonLocations": {"title": "Vehicle Season Pricing", "add": "Add Pricing", "bulkAdd": "Bulk Add Pricing", "edit": "Edit Pricing", "details": "Pricing Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this pricing?", "confirmUpdate": "Are you sure you want to update this pricing?", "confirmDelete": "Are you sure you want to delete this pricing?", "confirmBulkAction": "Confirm Bulk Action", "confirmBulkAdd": "Are you sure you want to add {totalEntries} pricing entries for {vehicleCount} vehicle types and {seasonCount} seasons?", "yes": "Yes", "no": "No", "priceMatrix": "Price Matrix", "selectVehicleAndSeason": "Please select vehicle types and seasons to display the price matrix", "labels": {"id": "ID", "vehicleType": "Vehicle Type", "vehicleTypes": "Vehicle Types", "season": "Season", "seasons": "Seasons", "pricePerKm": "Price per KM", "status": "Status", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"selectVehicleType": "Select vehicle type", "selectSeason": "Select season", "enterPrice": "Enter price per kilometer"}, "errors": {"vehicleTypeRequired": "Vehicle type is required", "seasonRequired": "Season is required", "priceRequired": "Price per kilometer is required", "pricePositive": "Price must be a positive number"}}, "manage_typeVehicules": {"title": "Vehicle Types Management", "add": "Add Vehicle Type", "edit": "Edit Vehicle Type", "details": "Vehicle Type Details", "confirmAction": "Confirm Action", "confirmUpdate": "Are you sure you want to update this vehicle type?", "confirmAdd": "Are you sure you want to add this vehicle type?", "yes": "Yes", "no": "No", "labels": {"id": "ID", "name_fr": "French Name", "name_en": "English Name", "name_ar": "Arabic Name", "code": "Code", "capacity": "Capacity", "status": "Status"}, "placeholders": {"name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "code": "Enter code", "capacity": "Enter capacity"}, "errors": {"nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "codeRequired": "Code is required", "capacityRequired": "Capacity is required"}}, "manage_locationTypes": {"title": "Manage Location Types", "add": "Add Location Type", "edit": "Edit Location Type", "details": "Location Type Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this location type?", "confirmUpdate": "Are you sure you want to update this location type?", "confirmDelete": "Are you sure you want to delete this location type?", "yes": "Yes", "no": "No", "sections": {"basicInfo": "Basic Information"}, "labels": {"id": "ID", "name": "Name", "code": "Code", "documents": "Required Documents", "status": "Status", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"name_fr": "Enter name in French", "name_en": "Enter name in English", "name_ar": "Enter name in Arabic", "code": "Enter location type code", "documents": "Enter required documents"}, "errors": {"nameRequired": "Name in French is required", "codeRequired": "Code is required"}}, "manage_typeVehicleTypeLocations": {"title": "Vehicle Location Types", "add": "Add Relation", "bulkAdd": "Bulk Add Relations", "edit": "Edit Relation", "details": "Relation Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this relation?", "confirmUpdate": "Are you sure you want to update this relation?", "confirmDelete": "Are you sure you want to delete this relation?", "confirmBulkAction": "Confirm Bulk Action", "confirmBulkAdd": "Are you sure you want to add {totalEntries} relations for {vehicleCount} vehicle types and {locationTypeCount} location types?", "yes": "Yes", "no": "No", "minKmMatrix": "Minimum KM Matrix", "selectVehicleAndLocationType": "Please select vehicle types and location types to display the matrix", "labels": {"id": "ID", "vehicleType": "Vehicle Type", "vehicleTypes": "Vehicle Types", "locationType": "Location Type", "locationTypes": "Location Types", "minKm": "Minimum KM", "defaultMinKm": "Default Minimum KM", "status": "Status", "actions": "Actions"}, "placeholders": {"selectVehicleType": "Select vehicle type", "selectVehicleTypes": "Select vehicle types", "selectLocationType": "Select location type", "selectLocationTypes": "Select location types", "enterMinKm": "Enter minimum kilometers", "enterDefaultMinKm": "Enter default minimum kilometers"}, "errors": {"vehicleTypeRequired": "Vehicle type is required", "locationTypeRequired": "Location type is required", "minKmRequired": "Minimum KM is required", "minKmPositive": "Minimum KM must be a positive number", "selectVehicleAndLocationType": "Please select at least one vehicle type and one location type"}}, "admin": {"title": "Manage Admins", "add": "Add Admin", "edit": "Edit Admin", "details": "Admin Details", "defaultPasswordMessage": "The default password is 'admin'.", "placeholders": {"lastname": "<PERSON><PERSON>'s name", "firstname": "<PERSON><PERSON>'s first name", "phone": "Phone", "cin": "CIN", "address": "Full address of the admin", "email": "Admin's email"}, "confirmDelete": "Are you sure you want to delete this admin?", "deleteSuccess": "Admin deleted successfully!"}, "labels": {"lastname": "Last Name", "firstname": "First Name", "phone": "Phone", "address": "Address", "cin": "CIN", "email": "Email", "createdAt": "Created At", "actions": "Actions"}, "errors": {"nameRequired": "Please enter the name", "firstNameRequired": "Please enter the first name", "phoneRequired": "Please enter a valid phone number", "phoneInvalid": "The phone number must be 10 digits", "cinRequired": "Please enter the CIN", "cinInvalid": "Invalid CIN (only uppercase letters and digits)", "addressRequired": "Please enter the address", "emailRequired": "Please enter a valid email"}, "filters": {"phone": "Filter by phone", "cin": "Filter by CIN", "email": "Filter by email"}, "yes": "Yes", "no": "No", "save": "Save", "error": "Error:"}, "manage_governorates": {"title": "Governorates", "add": "Add Governorate", "edit": "Edit Governorate", "details": "Governorate Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this governorate?", "yes": "Yes", "no": "No", "labels": {"id": "ID", "name": "Name", "nom_fr": "French Name", "nom_en": "English Name", "nom_ar": "Arabic Name", "hasPO": "Has Purchase Order", "createdAt": "Created At", "actions": "Actions", "code": "Code"}, "errors": {"nomFrRequired": "French name is required", "nomEnRequired": "English name is required", "nomArRequired": "Arabic name is required"}, "placeholders": {"nom_fr": "Enter French name", "nom_en": "Enter English name", "nom_ar": "Enter Arabic name", "code": "Enter governorate code"}, "po": {"title": "Purchase Orders Management", "add": "Add Purchase Order", "edit": "Edit Purchase Order", "details": "Purchase Order Details", "save": "Save", "confirmAction": "Confirmation", "confirmAdd": "Are you sure you want to add this purchase order?", "confirmUpdate": "Are you sure you want to update this purchase order?", "confirmDelete": "Are you sure you want to delete this purchase order?", "labels": {"reference": "Reference", "amount": "Amount", "status": "Status", "date": "Date"}, "placeholders": {"reference": "Enter reference", "amount": "Enter amount", "status": "Select status", "date": "Select date"}, "errors": {"referenceRequired": "Reference is required", "amountRequired": "Amount is required", "dateRequired": "Date is required"}}, "noPO": "No purchase orders available", "addPO": "Add purchase order"}, "manage_delegations": {"title": "Manage Delegations", "add": "Add Delegation", "edit": "Edit Delegation", "details": "Delegation Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this delegation?", "yes": "Yes", "no": "No", "labels": {"id": "ID", "name": "Name", "name_fr": "French Name", "name_en": "English Name", "name_ar": "Arabic Name", "governorate": "Governorate", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "governorate": "Select governorate"}, "errors": {"nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "governorateRequired": "Governorate is required"}, "filters": {"governorate": "Filter by governorate"}}, "manage_stations": {"title": "Manage Stations", "labels": {"name": "Name", "address": "Address", "governorate": "Governorate", "delegation": "Delegation", "stationType": "Station Type", "latitude": "Latitude", "longitude": "Longitude", "createdAt": "Created At", "actions": "Actions"}, "confirmDelete": "Are you sure you want to delete this station?", "yes": "Yes", "no": "No", "add": "Add a Station", "edit": "Edit Station", "details": "Station Details", "save": "Save", "filters": {"name": "Filter by Name", "governorate": "Filter by Governorate", "delegation": "Filter by Delegation", "stationType": "Filter by Station Type"}, "errors": {"nameRequired": "Name is required", "addressRequired": "Address is required", "delegationRequired": "Delegation is required", "governorateRequired": "Governorate is required", "stationTypeRequired": "Station Type is required", "latitudeRequired": "Latitude is required", "longitudeRequired": "Longitude is required"}, "placeholders": {"name": "Enter the station name", "address": "Enter the station address", "governorate": "Select a governorate", "delegation": "Select a delegation", "stationType": "Select the station type", "latitude": "Enter latitude", "longitude": "Enter longitude"}, "stationTypes": {"intermediate": "Intermediate", "terminus": "Terminus", "hidden": "Hidden"}, "view": "View Station", "confirmAction": "Confirm Action", "confirmUpdate": "Are you sure you want to update this station?", "confirmAdd": "Are you sure you want to add this station?", "selectGovernorate": "Please select a governorate first", "validation": {"nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "governorateRequired": "Governorate is required"}}, "manage_routes": {"title": "Subscription Routes Management", "title_inter_station": "Inter-station Routes Management", "add": "Add Subscription Route", "edit": "Edit Route", "details": "Route Details", "yes": "Yes", "no": "No", "save": "Save", "options_regular": {"yes": "Yes", "no": "No"}, "options": {"one_way": "One Way", "round_trip": "Round Trip"}, "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this route?", "confirmUpdate": "Are you sure you want to update this route?", "confirmDelete": "Are you sure you want to delete this route?", "labels": {"id": "Id", "name": "Name", "status": "Enabled", "abnType": "Subscription Type", "numberOfKm": "Kilometers", "delegation": "Delegation", "line": "Line", "createdAt": "Created At", "actions": "Actions", "manual_tariff": "Manual Tariff", "station_depart": "Departure", "station_arrival": "Arrival", "base_tariff": "Base Tariff", "regular": "Regular", "voyage_number": "Number of Trips", "subsTypes": "Subscription Types"}, "filters": {"name": "Search for route", "regular": "Select applied tariff type", "subsType": "Filter by subscription type"}, "placeholders": {"name": "Enter route name", "status": "Select status", "delegation": "Select delegation", "abnType": "Select subscription type", "governorate": "Select governorate", "station_depart": "Select departure station", "station_arrival": "Select arrival station", "base_tariff": "Select base tariff", "manual_tariff": "Enter manual tariff", "voyage_number": "Select trip type", "numberOfKm": "Enter number of kilometers", "regular": "Select applied tariff type", "line": "Select line"}, "errors": {"nameRequired": "Route name is required", "baseTariffRequired": "Base tariff is required", "manualTariffRequired": "Manual tariff is required", "governorateRequired": "Governorate is required", "delegationDepartRequired": "Departure delegation is required", "delegationArrivalRequired": "Arrival delegation is required", "stationDepartRequired": "Departure station is required", "stationArrivalRequired": "Arrival station is required", "voyageNumberRequired": "Trip type is required", "isRegularRequired": "Tariff type is required", "numberOfKmRequired": "Number of kilometers is required", "abnTypeRequired": "Subscription type is required", "lineRequired": "Line is required"}, "select_depart_arrival": "Select departure and arrival"}, "manage_establishment": {"title": "Establishments", "add": "Add Establishment", "edit": "Edit Establishment", "details": "Establishment Details", "selectGovernorate": "Select Governorate", "placeholders": {"name": "Establishment name", "abbreviation": "Establishment abbreviation", "delegation": "Establishment delegation", "establishmentType": "Establishment type", "governorate": "Establishment governorate"}, "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this establishment?", "confirmUpdate": "Are you sure you want to update this establishment?", "confirmDelete": "Are you sure you want to delete this establishment?", "deleteSuccess": "Establishment deleted successfully!", "labels": {"id": "Id", "name": "Name", "abbreviation": "Abbreviation", "governorate": "Governorate", "delegation": "Delegation", "establishmentType": "Establishment Type", "createdAt": "Created At", "actions": "Actions"}, "errors": {"nameRequired": "Please enter the name", "delegationRequired": "Please enter the delegation", "governorateRequired": "Please enter the governorate", "abbreviationRequired": "Please enter the abbreviation", "establishmentTypeRequired": "Please enter the establishment type", "addressRequired": "Please enter the address"}, "filters": {"name": "Filter by name", "abbreviation": "Filter by abbreviation", "delegation": "Filter by delegation", "governorate": "Filter by governorate", "establishmentType": "Filter by establishment type"}, "yes": "Yes", "no": "No", "save": "Save", "error": "Error:"}, "manage_rolePerm": {"title": "Manage Roles and Permissions", "add": "Add Role", "edit": "Edit Role", "details": "Role Details", "labels": {"name": "Name", "guard_name": "Guard Name", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"name": "Role Name", "guard_name": "Guard Name"}, "deleteSuccess": "Role successfully deleted!", "errors": {"nameRequired": "Please enter the name", "guard_nameRequired": "Please enter the guard name"}, "filters": {"name": "Filter by Name", "guard_name": "Filter by Guard Name"}, "confirmDelete": "Are you sure you want to delete this role?", "confirmAction": "Confirmation", "confirmAdd": "Do you really want to add this role?", "confirmUpdate": "Do you really want to update this role?", "yes": "Yes", "no": "No", "save": "Save", "error": "Error:", "permissions": {"unassigned_permissions": "Unassigned Permissions", "assigned_permissions": "Assigned Permissions"}, "roles": {"unassigned_roles": "Unassigned roles", "assigned_roles": "Assigned roles"}}, "manage_abnTypes": {"title": "Subscription Type Management", "add": "Add a Subscription Type", "edit": "Edit Subscription Type", "details": "Subscription Type Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this type?", "yes": "Yes", "no": "No", "labels": {"id": "Identifier", "name": "Name", "color": "Color", "createdAt": "Creation Date", "actions": "Actions"}, "errors": {"nameRequired": "Name is required", "colorRequired": "Color is required"}, "placeholders": {"name": "Enter name", "color": "Enter color"}, "filters": {"id": "Search by id", "name": "Search by name"}}, "manage_abnSubTypes": {"title": "Manage Subscription Sub-Types", "add": "Add Subscription Sub-Type", "edit": "Edit Subscription Sub-Type", "details": "Subscription Sub-Type Details", "save": "Save", "yes": "Yes", "no": "No", "confirmDelete": "Are you sure you want to delete this subscription sub-type?", "labels": {"id": "Identifier", "name": "Name", "color": "Color", "createdAt": "Created At", "actions": "Actions", "abnType": "Subscription Type"}, "errors": {"nameRequired": "Name is required", "colorRequired": "Color is required", "abnTypeRequired": "Subscription Type is required"}, "placeholders": {"name": "Enter the subscription sub-type name", "selectAbnType": "Select a subscription type"}, "filters": {"id": "Search by id", "name": "Search by name", "selectAbnType": "Search by sub-type subscription name"}}, "manage_baseTariff": {"title": "Manage Base Tariff", "add": "Add Base Tariff", "edit": "Edit Base Tariff", "details": "Base Tariff Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this base tariff?", "yes": "Yes", "no": "No", "filters": {"name": "Search by name", "date": "Search by date"}, "labels": {"name": "Name", "tariffPerKm": "Tariff per km", "date": "Date", "actions": "Actions", "createdAt": "Created At"}, "placeholders": {"name": "Enter name", "tariffPerKm": "Enter tariff per km", "date": "Enter date"}, "errors": {"nameRequired": "Name is required", "tariffPerKmRequired": "Tariff per km is required", "dateRequired": "Date is required"}}, "manage_schoolCampaigns": {"title": "School Campaign Management", "add": "Add Campaign", "edit": "Edit Campaign", "details": "Campaign Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this school campaign?", "yes": "Yes", "no": "No", "open": "Open", "closed": "Closed", "labels": {"name": "Name", "period": "Campaign Period", "isOpen": "Status", "startDate": "Start Date", "endDate": "End Date", "createdAt": "Created On", "actions": "Actions"}, "filters": {"name": "Campaign Name"}, "errors": {"nameRequired": "Name is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "isOpenRequired": "Campaign status is required"}, "placeholders": {"name": "Enter campaign name", "startDate": "Choose start date", "endDate": "Choose end date"}}, "manage_newSubs": {"title": "Subscriptions", "add": "Add Subscription", "edit": "Edit Subscription", "details": "Subscription Details", "selectSubscriptionType": "Select Subscription Type", "save": "Save", "createSuccess": "Subscription created successfully!", "updateSuccess": "Subscription updated successfully!", "renewalSuccess": "Subscription renewed successfully!", "deleteSuccess": "Subscription deleted successfully!", "upload": "Upload Photo", "cropImage": "Crop Photo", "noPhoto": "No Photo", "backgroundRequirement": "Image must have a white background", "selectSubscriptionPrompt": "Please select a subscription type", "selectDepartureFirst": "Please select a departure station first", "confirmCrop": "Save", "cancel": "Cancel", "labels": {"id": "ID", "client": "Client", "clientCivil": "Civil Client", "clientUniversitaire": "University Client", "clientImpersonal": "Impersonal Client", "clientConventional": "Conventional Client", "identityNumber": "ID Number", "conventionNumber": "Convention Number", "cin": "ID Card", "matricule": "Registration Number", "station_depart": "Departure Station", "station_arrival": "Arrival Station", "line": "Line", "periodicity": "Periodicity", "restDays": "Rest Days", "subsNumber": "Number of Subscriptions", "matriculate": "Registration Number", "clientDob": "Date of Birth", "createdAt": "Created At", "actions": "Actions", "abnType": "Subscription Type", "trip": "Trip", "socialAffair": "Social Affair", "socialAffairYes": "Social Affair", "socialAffairNo": "Regular", "reversed": "Reversed", "reversedYes": "Reversed", "reversedNo": "Normal", "status": "Status", "vacation": "Vacation"}, "placeholders": {"matriculate": "Enter registration number", "clientDob": "Select date of birth", "selectClientCin": "Select client by ID", "selectClient": "Select client", "station_depart": "Select departure station", "station_arrival": "Select arrival station", "line": "Select line", "periodicity": "Select periodicity", "restDays": "Select rest days", "subsNumber": "Enter number of subscriptions"}, "errors": {"motifDuplicate": "Reason for duplication is required", "abnTypeRequired": "Subscription type is required", "clientRequired": "Client is required", "matriculateRequired": "Registration number is required", "clientDobRequired": "Date of birth is required", "stationDepartRequired": "Departure station is required", "stationArrivalRequired": "Arrival station is required", "lineRequired": "Line is required", "periodicityRequired": "Periodicity is required", "restDaysRequired": "Rest days are required", "tooManyRestDays": "You cannot select more than {{max}} rest days for {{periodicity}} periodicity", "subsNumberRequired": "Number of subscriptions is required", "photoRequired": "Photo is required", "unexpected": "An unexpected error occurred", "socialAffairRequired": "Social affair selection is required", "statusRequired": "Status is required", "vacationRequired": "Vacation selection is required"}, "modal": {"cardTitle": "Subscription Card", "cardDuplicate": "Card Duplication", "motifDuplicate": "Duplication reason", "print": "Print", "close": "Close"}, "card": {"title": "Member Card", "name": "Name", "number": "Card Number", "expiry": "Expiry Date", "footerText": "Thank you for being a valued member!"}, "paymentOptions": {"notPayed": "Not Paid", "payed": "Paid", "canceled": "Canceled"}, "restDays": {"1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday", "7": "Sunday"}, "options": {"withVacation": "With Vacation", "withoutVacation": "Without Vacation"}, "tooltips": {"payment": "Manage Payment", "renew": "Renew Subscription", "viewCard": "View Subscription Card"}, "filters": {"client": "Search by Subscriber"}, "confirmDelete": "Are you sure you want to delete this subscription?", "yes": "Yes", "no": "No"}, "common": {"yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "close": "Close", "save": "Save", "cancel": "Cancel", "edit": "Edit", "export": "Export", "view": "View", "delete": "Delete", "open": "Open", "closed": "Closed", "na": "N/A", "tnd": "TND", "impression": "Print", "reimpression": "Reprint", "duplicata": "Duplicate", "motif": "Reason", "amount": "Amount", "originalCard": "Original Card"}, "manage_duplicateMotifs": {"title": "Manage Duplicate <PERSON>", "add": "Add Duplicate <PERSON>", "edit": "Edit Duplicate <PERSON>", "details": "Duplicate <PERSON><PERSON><PERSON>", "save": "Save", "confirmAction": "Confirmation", "confirmAdd": "Are you sure you want to add this duplicate motif?", "confirmUpdate": "Are you sure you want to update this duplicate motif?", "confirmDelete": "Are you sure you want to delete this duplicate motif?", "yes": "Yes", "no": "No", "labels": {"id": "ID", "name": "Name", "name_fr": "French Name", "name_en": "English Name", "name_ar": "Arabic Name", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"id": "Enter ID", "name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name"}, "errors": {"idRequired": "ID is required", "nameRequired": "Name is required"}}, "manage_configs": {"title": "System Configurations", "add": "Add Configuration", "edit": "Edit Configuration", "details": "Configuration Details", "save": "Save", "confirmAction": "Confirmation", "confirmAdd": "Are you sure you want to add this configuration?", "confirmUpdate": "Are you sure you want to update this configuration?", "confirmDelete": "Are you sure you want to delete this configuration?", "yes": "Yes", "no": "No", "types": {"string": "String", "integer": "Integer", "boolean": "Boolean", "json": "JSON", "array": "Array"}, "groups": {"system": "System", "app": "Application", "email": "Email", "payment": "Payment", "notification": "Notification"}, "labels": {"id": "ID", "key": "Key", "value": "Value", "type": "Type", "group": "Group", "label": "Label", "label_fr": "French Label", "label_en": "English Label", "label_ar": "Arabic Label", "description_fr": "French Description", "description_en": "English Description", "description_ar": "Arabic Description", "is_public": "Public", "is_system": "System", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"key": "Enter configuration key", "value": "Enter configuration value", "type": "Select configuration type", "group": "Select configuration group", "label_fr": "Enter French label", "label_en": "Enter English label", "label_ar": "Enter Arabic label", "description_fr": "Enter French description", "description_en": "Enter English description", "description_ar": "Enter Arabic description"}, "errors": {"keyRequired": "Key is required", "valueRequired": "Value is required", "typeRequired": "Type is required", "groupRequired": "Group is required", "labelFrRequired": "French label is required", "labelEnRequired": "English label is required", "labelArRequired": "Arabic label is required"}}, "manage_establishmentTypes": {"title": "Manage Establishment Types", "add": "Add New Establishment Type", "edit": "Edit Establishment Type", "details": "View Establishment Type Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this establishment type?", "confirmUpdate": "Are you sure you want to update this establishment type?", "confirmDelete": "Are you sure you want to delete this establishment type?", "yes": "Yes", "no": "No", "save": "Save", "labels": {"id": "ID", "name": "Name", "name_fr": "Name (French)", "name_en": "Name (English)", "name_ar": "Name (Arabic)", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"id": "ID", "name_fr": "Enter name in French", "name_en": "Enter name in English", "name_ar": "Enter name in Arabic"}, "errors": {"idRequired": "ID is required", "nameRequired": "Name is required"}}, "manage_assignAgents": {"title": "Agent Assignment", "labels": {"id": "Id", "salesPoint": "Sales Point", "agent": "Agent", "salesPeriod": "Sales Period", "cardTypes": "Assigned Card Types", "cardTypesDetails": "Assigned Card Types Details", "startNumber": "Sequential Start", "endNumber": "Sequential End", "abnNumber": "Number of Subscriptions", "createdAt": "Created At", "actions": "Actions", "subscriptionCards": "Subscription Cards"}, "filters": {"salesPoint": "Search by sales point...", "agent": "Search by agent...", "salesPeriod": "Search by sales period...", "cardTypes": "Search by card types...", "subscriptionCards": "Search by subscription cards..."}, "confirmDelete": "Are you sure you want to delete this assignment?", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this assignment?", "confirmUpdate": "Are you sure you want to update this assignment?", "yes": "Yes", "no": "No", "add": "Add Agency", "details": "Assignment Details", "edit": "Edit Assignment", "save": "Save", "errors": {"salesPointRequired": "Please select a sales point", "agentRequired": "Please select an agent", "salesPeriodRequired": "Please select a sales period", "subscriptionCardsRequired": "Please select at least one subscription card", "cardTypesRequired": "Please select at least one card type", "startNumberRequired": "Start sequential number of assigned cards is required", "endNumberRequired": "End sequential number of assigned cards is required"}, "placeholders": {"salesPoint": "Select a sales point", "agent": "Select an agent", "salesPeriod": "Select a sales period", "cardsNumber": "Enter number of cards", "startNumber": "Start sequential number of assigned cards", "endNumber": "End sequential number of assigned cards", "abnNumber": "Enter number of subscriptions", "subscriptionCards": "Select subscription cards", "cardTypes": "Select assigned card types"}}, "labels": {"id": "ID", "salesPoint": "Sales Point", "agent": "Agent", "salesPeriod": "Sales Period", "cardTypes": "Card Types", "cardTypesDetails": "Card Types Details", "startNumber": "Start Number", "endNumber": "End Number", "createdAt": "Created At", "actions": "Actions", "availableSequences": "Available Sequences", "occupiedSequences": "Occupied Sequences", "sequenceVisualization": "Sequence Visualization", "sequenceStats": "Sequence Statistics", "totalAvailable": "Total Available", "totalOccupied": "Total Occupied", "totalCards": "Total Cards", "totalUsed": "Total Used", "cardsInRange": "cards in range"}, "placeholders": {"salesPoint": "Select sales point", "agent": "Select agent", "salesPeriod": "Select sales period", "cardTypes": "Select card types", "startNumber": "Enter start number", "endNumber": "Enter end number"}, "errors": {"salesPointRequired": "Sales point is required", "agentRequired": "Agent is required", "salesPeriodRequired": "Sales period is required", "cardTypesRequired": "Card types are required", "startNumberRequired": "Start number is required", "endNumberRequired": "End number is required", "endNumberLessThanStart": "End number must be greater than or equal to start number", "sequenceNotAvailable": "This sequence is not available", "sequenceValidationError": "Error validating sequence", "someSequencesNotAvailable": "Some sequences are not available. Please check and try again."}, "manage_schoolDegrees": {"title": "Manage School Degrees", "add": "Add New School Degree", "edit": "Edit School Degree", "details": "School Degree Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this school degree?", "confirmUpdate": "Are you sure you want to update this school degree?", "confirmDelete": "Are you sure you want to delete this school degree?", "labels": {"id": "ID", "name": "Name", "name_fr": "Name (French)", "name_en": "Name (English)", "name_ar": "Name (Arabic)", "establishmentType": "Establishment Type", "ageMax": "Maximum Age", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"name_fr": "Enter name in French", "name_en": "Enter name in English", "name_ar": "Enter name in Arabic", "ageMax": "Enter maximum age", "establishmentType": "Select establishment type"}, "filters": {"establishmentType": "Filter by establishment type"}, "errors": {"nameRequired": "Name is required", "ageMaxRequired": "Maximum age is required", "establishmentTypeRequired": "Establishment type is required"}, "yes": "Yes", "no": "No", "save": "Save", "error": "Error:"}, "manage_paymentMethods": {"title": "Payment Methods Management", "add": "Add Payment Method", "edit": "Edit Payment Method", "details": "Payment Method Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this payment method?", "confirmUpdate": "Are you sure you want to update this payment method?", "confirmDelete": "Are you sure you want to delete this payment method?", "labels": {"id": "ID", "name": "Name", "name_fr": "Name (French)", "name_en": "Name (English)", "name_ar": "Name (Arabic)", "status": "Status", "createdAt": "Created At", "actions": "Actions"}, "status": {"active": "Active", "inactive": "Inactive"}, "placeholders": {"name_fr": "Enter name in French", "name_en": "Enter name in English", "name_ar": "Enter name in Arabic"}, "errors": {"nameRequired": "Name is required"}}, "manage_cardTypes": {"title": "Card Types", "add": "Add Card Type", "edit": "Edit Card Type", "details": "Card Type Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this card type?", "confirmUpdate": "Are you sure you want to update this card type?", "confirmDelete": "Are you sure you want to delete this agency?", "deleteSuccess": "Agency deleted successfully!", "labels": {"id": "Id", "code": "Code", "name": "Name", "nameFr": "Name (French)", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "address": "Address", "contact": "Contact", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"name": "Enter Name", "nameFr": "Enter French Name", "nameEn": "Enter English Name", "nameAr": "Enter Arabic Name", "code": "Enter Code", "address": "Enter Address", "contact": "Enter Contact"}, "errors": {"nameRequired": "Please enter the name", "nameFrRequired": "Please enter the French name", "nameEnRequired": "Please enter the English name", "nameArRequired": "Please enter the Arabic name", "codeRequired": "Please enter the code", "addressRequired": "Please enter the address", "contactRequired": "Please enter the contact"}, "filters": {"code": "Filter by code", "name": "Filter by name", "nameFr": "Filter by French name", "nameEn": "Filter by English name", "nameAr": "Filter by Arabic name", "address": "Filter by address", "contact": "Filter by contact"}, "yes": "Yes", "no": "No", "save": "Save", "error": "Error:"}, "manage_tariffBase": {"title": "Base Tariff Management", "add": "Add Tariff", "edit": "Edit Tariff", "details": "Tariff Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this tariff?", "confirmAction": "Confirmation", "confirmAdd": "Are you sure you want to add this tariff?", "confirmUpdate": "Are you sure you want to update this tariff?", "yes": "Yes", "no": "No", "labels": {"id": "Id", "abn_type": "Subscription Type", "createdAt": "Created At", "actions": "Actions", "name": "Name", "name_fr": "Name (French)", "name_en": "Name (English)", "name_ar": "Name (Arabic)", "tariffPerKM": "Tariff per KM", "date": "Application Date"}, "placeholders": {"id": "Enter identifier", "abn_type": "Select subscription type", "name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "tariffPerKM": "Enter tariff per kilometer", "date": "Select application date"}, "errors": {"idRequired": "Identifier is required", "abnTypeRequired": "Subscription type is required", "nameRequired": "Name is required", "tariffPerKMRequired": "Tariff per kilometer is required", "dateRequired": "Application date is required"}, "filters": {"id": "Filter by identifier", "abn_types": "Filter by subscription type", "name": "Filter by name", "date": "Filter by application date"}}, "manage_options": {"title": "Options", "add": "Add Option", "edit": "Edit Option", "details": "Option Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this option?", "labels": {"id": "ID", "nom_fr": "French Name", "nom_en": "English Name", "nom_ar": "Arabic Name", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"nom_fr": "Enter French name", "nom_en": "Enter English name", "nom_ar": "Enter Arabic name"}, "errors": {"nomFrRequired": "French name is required", "nomEnRequired": "English name is required", "nomArRequired": "Arabic name is required"}}, "manage_seasons": {"title": "Manage Seasons", "add": "Add Season", "edit": "Edit Season", "details": "Season Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this season?", "yes": "Yes", "no": "No", "labels": {"id": "ID", "name": "Name", "name_fr": "French Name", "name_en": "English Name", "name_ar": "Arabic Name", "period": "Period", "priority": "Priority", "start_date": "Start Date", "end_date": "End Date", "actions": "Actions", "createdAt": "Created At"}, "placeholders": {"name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "start_date": "Select start date", "end_date": "Select end date", "priority": "Enter priority"}, "errors": {"name_required": "Name is required", "start_date_required": "Start date is required", "end_date_required": "End date is required", "priority_required": "Priority is required"}}, "manage_lines": {"title": "Lines Management", "add": "Add Line", "save": "Save", "addStation": "Add Stations", "editStation": "Edit Stations", "addDepartureTime": "Add Departure Times", "title_inter_station": "Inter-station Routes Management", "labels": {"code_line": "Line Code", "name": "Name", "status": "Enabled", "schedules": "Schedules", "numberOfKm": "Mileage", "commercial_speed": "Speed", "routes": "Routes", "route": "Route", "departureTimes": "Departure", "serviceType": "Service Type", "comfort": "Comfort", "normal": "Normal", "position": "Position", "createdAt": "Creation Date", "actions": "Actions", "stations": "Stations"}, "filters": {"code_line": "Search by line code", "name": "Search by name", "status": "Select status", "serviceType": "Select service type", "commercial_speed": "Search by commercial speed"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "placeholders": {"code_line": "Enter line code", "name": "Enter name", "status": "Select status", "commercial_speed": "Enter commercial speed", "selectStation": "Select a station", "position": "Enter position", "serviceType": "Select service type"}, "messages": {"no_routes": "No Station available", "confirmDelete": "Are you sure you want to delete this line?", "yes": "Yes", "no": "No", "no_route_assigned": "No Station assigned", "unknownRoute": "Unknown Station"}, "errors": {"codeLineRequired": "Line code is required", "nameRequired": "Name is required", "selectRoute": "Please select a route", "positionRequired": "Position is required", "selectDepartureTimeRequired": "Please select a departure time", "serviceTypeRequired": "Please select a service type", "stationRequired": "Please select a station", "manualTariffRequired": "Please enter the tariff", "departureTimeRequired": "Departure times are required"}, "addStationAssignment": "Assign Stations", "stations": "Associated Stations", "addstation": "Add Station", "direction": {"normal": "Normal Direction", "reverse": "Reverse Direction"}}, "manage_campaigns": {"title": "Campaigns", "add": "Add Campaign", "edit": "Edit Campaign", "details": "Campaign Details", "save": "Save", "salesPeriods": "Sales Periods", "addSalesPeriod": "Add Sales Period", "addFirstPeriod": "Add First Period", "noSalesPeriods": "No sales periods available", "confirmDelete": "Are you sure you want to delete this campaign?", "confirmAction": "Confirmation", "confirmAdd": "Are you sure you want to add this campaign?", "confirmUpdate": "Are you sure you want to update this campaign?", "confirmDeletePeriod": "Are you sure you want to delete this sales period?", "period": {"edit": "Edit Period", "delete": "Delete Period"}, "yes": "Yes", "no": "No", "salesPeriodDeleted": "Sales period successfully deleted", "salesPeriodSaved": "Sales period successfully saved", "error": "An error occurred", "labels": {"name": "Name", "campaignType": "Campaign Type", "period": "Period", "status": "Status", "salesPeriods": "Sales Periods", "actions": "Actions", "startDate": "Start Date", "endDate": "End Date", "periodName": "Period Name", "abnType": "Subscription Type"}, "filters": {"name": "Search by Name", "campaignType": "Search by Campaign Type", "abnType": "Search by Subscription Type"}, "placeholders": {"name": "Enter campaign name", "campaignType": "Enter campaign type", "startDate": "Select start date", "endDate": "Select end date", "periodName": "Enter period name", "status": "Select status", "selectAbnType": "Select subscription type"}, "errors": {"nameRequired": "Campaign name is required", "statusRequired": "Campaign status is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "periodNameRequired": "Period name is required", "campaignTypeRequired": "Campaign type is required", "abnTypeRequired": "Subscription type is required"}, "open": "Open", "closed": "Closed", "WEEKLY": "Weekly", "MONTHLY": "Monthly", "YEARLY": "Yearly", "BIANNUAL": "Biannual", "salesPeriodsCount": "Number of Sales Periods", "salesPeriodModal": "Sales Periods Management"}, "manage_salesPeriods": {"title": "Sales Periods", "add": "Add Sales Period", "edit": "Edit Sales Period", "details": "Sales Period Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this sales period?", "confirmAction": "Confirmation", "confirmAdd": "Are you sure you want to add this sales period?", "confirmUpdate": "Are you sure you want to update this sales period?", "periodsAutoUpdated": "{{count}} sales period(s) automatically closed because end date has passed", "yes": "Yes", "no": "No", "open": "Open", "closed": "Closed", "labels": {"name_fr": "Name (French)", "name_en": "Name (English)", "name_ar": "Name (Arabic)", "status": "Status", "campaign": "Campaign", "period": "Period", "startDate": "Start Date", "endDate": "End Date"}, "placeholders": {"name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "campaign": "Select campaign", "startDate": "Select start date", "endDate": "Select end date"}, "errors": {"nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "statusRequired": "Status is required", "campaignRequired": "Campaign is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required"}}, "manage_agencies": {"title": "Manage Agencies", "add": "Add Agency", "edit": "Edit Agency", "details": "Agency Details", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this agency?", "confirmUpdate": "Are you sure you want to update this agency?", "confirmDelete": "Are you sure you want to delete this agency?", "save": "Save", "selectGovernorate": "Please select a governorate first", "labels": {"name_fr": "French Name", "name_en": "English Name", "name_ar": "Arabic Name", "code": "Code", "contact": "Contact", "address": "Address", "governorate": "Governorate", "delegation": "Delegation"}, "placeholders": {"name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "code": "Enter code", "contact": "Enter contact number", "address": "Enter address", "governorate": "Select governorate", "delegation": "Select delegation"}, "errors": {"nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "codeRequired": "Code is required", "contactRequired": "Contact is required", "contactInvalid": "Contact must be 8 digits", "addressRequired": "Address is required", "governorateRequired": "Governorate is required", "delegationRequired": "Delegation is required"}, "filters": {"name": "Search by name", "name_fr": "Filter by French name", "name_en": "Filter by English name", "name_ar": "Filter by Arabic name", "code": "Filter by code", "contact": "Filter by contact", "address": "Filter by address", "governorate": "Filter by governorate", "delegation": "Filter by delegation"}, "salesPeriodModal": "Add Sales Point", "salesPoints": "Sales Points", "addSalesPoint": "Add Sales Point", "addFirstSalesPoint": "Add First Sales Point", "noSalesPoints": "No sales points available", "open": "Open", "closed": "Closed"}, "manage_salesPoints": {"title": "Sales Points Management", "add": "Add Sales Point", "edit": "Edit Sales Point", "details": "Sales Point Details", "save": "Save", "yes": "Yes", "no": "No", "confirmDelete": "Are you sure you want to delete this sales point?", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this sales point?", "confirmUpdate": "Are you sure you want to update this sales point?", "labels": {"id": "ID", "name": "Name", "name_fr": "French Name", "name_en": "English Name", "name_ar": "Arabic Name", "contact": "Contact", "address": "Address", "agency": "Agency", "status": "Status", "governorate": "Governorate", "delegation": "Delegation", "createdAt": "Created At", "actions": "Actions"}, "placeholders": {"name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "contact": "Enter contact information", "address": "Enter address", "agency": "Select agency", "status": "Select status", "governorate": "Select governorate", "delegation": "Select delegation"}, "errors": {"nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "contactRequired": "Contact is required", "addressRequired": "Address is required", "agencyRequired": "Agency is required", "governorateRequired": "Governorate is required", "delegationRequired": "Delegation is required", "contactInvalid": "Contact must be 8 digits"}, "selectGovernorate": "Please select a governorate first", "active": "Active", "inactive": "Inactive"}, "manage_discounts": {"title": "Manage Discounts", "add": "Add Discount", "edit": "Edit Discount", "details": "Discount Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this discount?", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this discount?", "confirmUpdate": "Are you sure you want to update this discount?", "yes": "Yes", "no": "No", "labels": {"id": "ID", "name": "Name", "name_fr": "Name (French)", "name_en": "Name (English)", "name_ar": "Name (Arabic)", "subsType": "Subscription Type", "abnPeriod": "Subscription Periods", "discountPercentage": "Discount (%)", "is_stagiaire": "Trainee", "is_affaire_sociale": "Social Affair", "createdAt": "Created At", "actions": "Actions"}, "errors": {"nameRequired": "Name is required", "nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "discountPercentageRequired": "Discount percentage is required", "subsTypeRequired": "Subscription type is required", "abnPeriodRequired": "Subscription period is required"}, "placeholders": {"name": "Enter discount name", "name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "discountPercentage": "Enter discount percentage", "subsType": "Select subscription type", "abnPeriod": "Select subscription periods"}, "filters": {"discountPercentage": "Search by percentage", "subsType": "Search by subscription type", "abnPeriod": "Search by subscription period"}}, "manage_periodicities": {"title": "Periodicities", "add": "Add Periodicity", "edit": "Edit Periodicity", "details": "Periodicity Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this periodicity?", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this periodicity?", "confirmUpdate": "Are you sure you want to modify this periodicity?", "yes": "Yes", "no": "No", "labels": {"id": "Id", "name": "Name", "periodicityType": "Periodicity Type", "maxReposDaysNumber": "Max Days/Week", "createdAt": "Created At", "actions": "Actions"}, "errors": {"nameRequired": "Subscription period name is required", "periodicityType": "Periodicity type is required", "maxReposDaysNumberRequired": "Maximum rest days number is required"}, "placeholders": {"name": "Enter subscription period name", "daysNumber": "Enter number of days", "periodicityType": "Select periodicity type", "maxReposDaysNumber": "Enter max days/week"}, "filters": {"name": "Search by name", "periodicityType": "Search by periodicity type", "maxRestDays": "Search by maximum rest days"}}, "manage_clientTypes": {"title": "Client Types", "add": "Add Client Type", "edit": "Edit Client Type", "details": "Client Type Details", "save": "Save", "confirmDelete": "Are you sure you want to delete this client type?", "confirmAction": "Confirm Action", "confirmAdd": "Are you sure you want to add this client type?", "confirmUpdate": "Are you sure you want to modify this client type?", "yes": "Yes", "no": "No", "labels": {"id": "Id", "name": "Name", "abnType": "Subscription Type", "hasCIN": "Has ID Card?", "isStudent": "Is Student?", "isImpersonal": "Is Impersonal?", "color": "Color", "createdAt": "Created At", "actions": "Actions"}, "errors": {"nameRequired": "Client type name is required", "hasCINRequired": "Please specify if this type has ID card", "abnTypeRequired": "Please choose the subscription type", "colorRequired": "Color is required", "isStudentRequired": "Please specify if this type is for students"}, "placeholders": {"name": "Enter client type name", "abnType": "Enter subscription type", "color": "Enter color"}, "filters": {"name": "Search by name", "hasCIN": "Has ID Card?", "abnType": "Search by subscription type", "isStudent": "Is Student?"}}, "manage_stockCards": {"title": "Stock Cards", "add": "Add Stock", "edit": "Edit Stock", "details": "Stock Details", "ajout": "Addition", "retour": "Return", "placeholders": {"name": "Stock name", "quantity": "Stock quantity", "typeStock": "Card types", "mouvement": "Movement", "status": "Stock status", "initialQuantity": "Initial quantity", "remainingQuantity": "Remaining quantity", "num_seq_start": "Start sequence number", "num_seq_end": "End sequence number", "cardTypes": "Select card type"}, "yes": "Yes", "no": "No", "confirmDelete": "Are you sure you want to delete this card?", "labels": {"id": "Id", "name": "Name", "typeStock": "Card types", "initialQuantity": "Initial quantity", "remainingQuantity": "Remaining quantity", "quantity": "Stock quantity", "status": "Status", "createdAt": "Created at", "mouvement": "Movement", "id_agent": "Agent", "id_card_type": "Card type", "num_seq_start": "Start sequence", "num_seq_end": "End sequence", "actions": "Actions"}, "errors": {"sequence_not_in_affectation": "The card sequence must be part of an affectation sequence", "nameRequired": "Stock name is required", "initialQuantityRequired": "Please enter the initial quantity", "remainingQuantityRequired": "Please enter the remaining quantity", "statusRequired": "Please select the status", "mouvementRequired": "Please select the movement", "id_card_typeRequired": "Please select the card type", "id_agentRequired": "Please select the agent", "num_seq_startRequired": "Please enter the start sequence number", "num_seq_endRequired": "Please enter the end sequence number", "sequence_end_before_start": "End sequence must be greater than or equal to start sequence", "sequence_out_of_range": "Sequence must be between {min} and {max}"}}, "manage_cardsFees": {"title": "<PERSON>es", "add": "Add Fee", "edit": "<PERSON>", "details": "<PERSON><PERSON>", "save": "Save", "confirmDelete": "Are you sure you want to delete this fee?", "confirmAction": "Confirmation", "confirmAdd": "Are you sure you want to add this fee?", "confirmUpdate": "Are you sure you want to update this fee?", "yes": "Yes", "no": "No", "labels": {"id": "Id", "name": "Name", "name_fr": "Name (French)", "name_en": "Name (English)", "name_ar": "Name (Arabic)", "abnType": "Subscription Type", "amount": "Amount in TND", "createdAt": "Created At", "actions": "Actions"}, "errors": {"nameRequired": "Name is required", "nameFrRequired": "French name is required", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "abnTypeRequired": "Subscription type is required", "amountRequired": "Amount is required"}, "placeholders": {"name": "Enter name", "name_fr": "Enter French name", "name_en": "Enter English name", "name_ar": "Enter Arabic name", "abnType": "Select subscription type", "amount": "Enter amount"}}, "manage_stats": {"title": "Statistics", "placeholder": "Select an option", "paragraph": "Please choose a statistic type", "btn": "Apply", "reset": "Reset", "export_excel": "Export to Excel", "export_loading": "Exporting...", "export_success": "Excel export successful", "export_error": "Export error", "select_statistic_first": "Please select a statistic type first", "no_data_to_export": "No data to export", "abonnees_trajet": "Subscribers by Trip", "abonnees_ligne": "Subscribers by Line", "type_abn": "Subscription Type", "start_date": "Start Date", "end_date": "End Date", "period": "Sales Period", "trajet": "Trip", "ligne": "Line", "nb_abonnees": "Number of Subscribers", "subscription": "Subscription", "sub_count": "Subscriber Count", "sales_periods_compaign": "Sales Periods by Campaign", "montant": "Amount", "sub_etab": "Subscribers by Establishment", "sales_periods": "Sales Period", "etab": "Establishment", "subscribersCount": "Number of Subscribers", "detail_abn_etab": "Subscriber Details by Establishment", "total_abonnees": "Total Subscribers", "total_montant": "Total Amount", "total_trajets": "Total Trips", "moyenne_trajet": "Average per Trip", "donnees_detaillees": "Detailed Data", "total_etablissements": "Total Establishments", "moyenne_etablissement": "Average per Establishment", "sub_trancheKm_ligne": "Subscribers by Line Kilometric Range", "ligne_tranch_km": "Line - Kilometric Range", "total_lignes": "Total Lines", "moyenne_ligne": "Average per Line", "abonnees_remise": "Subscribers by Discount", "remise": "Discount", "nb_abonnements": "Number of Subscriptions", "total_remises": "Total Discounts", "moyenne_remise": "Average per Discount", "recettes_periode_vente": "Revenue by Sale Period", "campagne": "Campaign", "periode_vente": "Sale Period", "nb_transactions": "Number of Transactions", "total_periodes": "Total Periods", "moyenne_periode": "Average per Period", "recettes_agence": "Revenue by Agency", "agence": "Agency", "total_agences": "Total Agencies", "moyenne_agence": "Average per Agency", "recettes_gouvernorat": "Revenue by Governorate", "gouvernorat": "Governorate", "total_gouvernorats": "Total Governorates", "moyenne_gouvernorat": "Average per Governorate", "recettes_methode_paiement": "Revenue by Payment Method", "methode_paiement": "Payment Method", "total_methodes_paiement": "Total Payment Methods", "moyenne_methode_paiement": "Average per Payment Method"}}