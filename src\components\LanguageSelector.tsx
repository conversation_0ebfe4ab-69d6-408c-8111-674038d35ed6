import { Select} from "antd";
import { useTranslation } from "react-i18next";
import {useEffect} from "react";

const { Option } = Select;


const LanguageSelector = () => {
    const { i18n } = useTranslation();

    useEffect(() => {
        const storedLanguage = localStorage.getItem('language') || 'fr';
        if (storedLanguage) {
            i18n.changeLanguage(storedLanguage);
            document.documentElement.dir = storedLanguage === 'ar' ? 'rtl' : 'ltr';
        }
    }, [i18n]);

    const handleLanguageChange = (value: string) => {
        i18n.changeLanguage(value);
        document.documentElement.dir = value === 'ar' ? 'rtl' : 'ltr';
        localStorage.setItem('language', value);
    };

    return (
        <div className="z-10">
            <Select
                value={i18n.language || 'fr'}
                onChange={handleLanguageChange}
                className="language-select"
            >
                <Option value="fr">Français</Option>
                <Option value="ar">العربية</Option>
            </Select>
        </div>
    );
};

export default LanguageSelector;
