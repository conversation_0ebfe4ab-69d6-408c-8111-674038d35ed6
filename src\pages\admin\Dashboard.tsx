import React, { useEffect, useState } from 'react';
import { Card, Typography, Row, Col, Spin, DatePicker, Tabs, Button, message } from 'antd';
import { <PERSON><PERSON><PERSON>riangle, CreditCard, Users, RefreshCw } from "lucide-react";
import { Line, Pie } from "@ant-design/charts";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from 'react-redux';
import {
    getDashboardStats
} from '../../features/admin/dashboardSlice';
import { RootState } from '../../features/store';
import dayjs from 'dayjs';

const { Title } = Typography;

const Dashboard: React.FC = () => {
    const dispatch = useDispatch();
    const { i18n, t } = useTranslation();
    const currentLang = i18n.language;

    const {
        dashboard,
        loading
    } = useSelector((state: RootState) => state.dashboard);

    const [activeTab, setActiveTab] = useState<string>("summary");
    const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
    const [isFiltering, setIsFiltering] = useState<boolean>(false);

    useEffect(() => {
        if (!isFiltering) {
            applyFilters(activeTab, dateRange);
        }

        setIsFiltering(false);
    }, [activeTab]);

    const handleTabChange = (key: string) => {
        setActiveTab(key);
    };

    const handleDateRangeChange = (dates: any) => {
        setIsFiltering(true);
        if (dates && dates.length === 2) {
            setDateRange([dates[0], dates[1]]);
        } else {
            setDateRange(null);
        }
        applyFilters(activeTab, dates && dates.length === 2 ? [dates[0], dates[1]] : null);
    };

    const applyFilters = (tab: string, dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
        const params = {
            start_date: dates ? dates[0].format('YYYY-MM-DD') : undefined,
            end_date: dates ? dates[1].format('YYYY-MM-DD') : undefined
        };

        switch(tab) {
            case "summary":
                dispatch(getDashboardStats(params));
                break;
            default:
                break;
        }
    };

    const resetFilters = () => {
        setDateRange(null);
        setIsFiltering(true);
        applyFilters(activeTab, null);
        message.success(t("dashboard.filters_reset"));
    };


    const getSummaryStatusPieConfig = () => {
        if (!dashboard || !dashboard.subscription_status) {
            return {
                data: [{ type: t("dashboard.loading"), value: 0 }],
                angleField: 'value',
                colorField: 'type',
                legend: true,
                state: {
                    inactive: { opacity: 0.5 },
                    active: { opacity: 1 },
                },
                radius: 0.8,
                height: 380,
                style: { stroke: '#fff', lineWidth: 2 },
            };
        }

        const statusMap: Record<string, string> = {
            'PAYED': t("dashboard.paid"),
            'NOTPAYED': t("dashboard.not_paid"),
            'CANCELED': t("dashboard.canceled"),
            'PENDING': t("dashboard.pending")
        };

        const allStatuses = ['PAYED', 'NOTPAYED', 'CANCELED', 'PENDING'];

        const data = allStatuses.map(status => {
            const count = dashboard.subscription_status[status] || 0;
            const label = statusMap[status] || status;
            return {
                type: label,
                abonnements: typeof count === 'string' ? parseInt(count, 10) : count
            };
        });

        return {
            data,
            angleField: 'abonnements',
            colorField: 'type',
            label : {
                text: (d:any) => `${d.type} (${d.abonnements})`,
                position: 'spider',
            },
            interaction: {
                elementHighlight: true,
            },
            state: {
                inactive: { opacity: 0.5 },
                active: { opacity: 1 },
            },
            legend: true,
            radius: 0.8,
            height: 380,
            style: { stroke: '#fff', lineWidth: 2 }
        };
    };

    const getSummarySubscriptionTypesLineConfig = () => {
        if (!dashboard || !dashboard.subscriptions_by_type) {
            return {
                data: [{ type: t("dashboard.loading"), nombre: 0 }],
                xField: 'type',
                yField: 'nombre',
                height: 380,
                color: '#dc2626',
                point: {
                    size: 5,
                    shape: 'diamond',
                    fill: '#dc2626',
                },
            };
        }
        const data = [...dashboard.subscriptions_by_type]
            .sort((a: any, b: any) => a[`nom_${currentLang}`].localeCompare(b[`nom_${currentLang}`]))
            .map((item: any, index: number) => {
                return {
                    type: item[`nom_${currentLang}`],
                    nombre: item.count,
                    index: index
                };
            });

        return {
            data,
            xField: 'type',
            yField: 'nombre',
            height: 380,
            smooth: true,
            point: {
                size: 5,
                shape: 'diamond',
                fill: '#dc2626',
            },
        };
    };

    return (
        <div className="space-y-6" dir={currentLang === "ar" ? "rtl" : "ltr"}>
            <Card className="mb-4">
                <div className="flex flex-wrap gap-4 items-center justify-between">
                    <div className="flex flex-wrap gap-4 items-center">
                        <div>
                            <p className={`text-gray-600 mb-2 ${currentLang === "ar" ? "text-right" : "text-left"}`}>{t("dashboard.date_range")}</p>
                            <DatePicker.RangePicker
                                onChange={handleDateRangeChange}
                                format="YYYY-MM-DD"
                                value={dateRange}
                            />
                        </div>
                    </div>
                    <div>
                        <Button
                            icon={<RefreshCw size={16} />}
                            onClick={resetFilters}
                            type="default"
                        >
                            {t("dashboard.reset_filters")}
                        </Button>
                    </div>
                </div>
            </Card>

            <Tabs
                defaultActiveKey="summary"
                onChange={handleTabChange}
                className="dashboard-tabs"
                tabPosition="top"
            >
                <Tabs.TabPane
                    tab={<span>{t("dashboard.summary")}</span>}
                    key="summary"
                >
                    <Row gutter={[16, 16]} className="mb-6">
                        <Col xs={24} sm={12} lg={6}>
                            <Card className="hover:shadow-sm transition-shadow">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-md" style={{backgroundColor:"var(--primary-color_light)"}}>
                                        <Users className="w-6 h-6" style={{color:"var(--primary-color)"}}/>
                                    </div>
                                    <div className={`${currentLang === "ar" ? "mr-4 text-right" : "ml-4 text-left"}`}>
                                        <p className="text-gray-600 text-sm">{t("dashboard.total_subscriptions")}</p>
                                        {loading ? (
                                            <Spin size="small" />
                                        ) : (
                                            <p className="text-2xl font-semibold">
                                                {dashboard?.total_subscriptions?.toLocaleString() || 0}
                                            </p>
                                        )}
                                        <p className="text-xs text-gray-500">
                                            {t("dashboard.total")}: {dashboard?.total_subscriptions?.toLocaleString() || 0}
                                        </p>
                                    </div>
                                </div>
                            </Card>
                        </Col>

                        <Col xs={24} sm={12} lg={6}>
                            <Card className="hover:shadow-sm transition-shadow">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-md" style={{backgroundColor:"var(--secondary-color_light)"}}>
                                        <CreditCard className="w-6 h-6" style={{color:"var(--secondary-color)"}}/>
                                    </div>
                                    <div className={`${currentLang === "ar" ? "mr-4 text-right" : "ml-4 text-left"}`}>
                                        <p className="text-gray-600 text-sm">{t("dashboard.revenue")}</p>
                                        {loading ? (
                                            <Spin size="small" />
                                        ) : (
                                            <p className="text-2xl font-semibold">
                                                {dashboard?.total_revenue?.toLocaleString() || 0}  {t("common.tnd")}
                                            </p>
                                        )}
                                         <p className="text-xs text-gray-500">
                                            {t("dashboard.total")}: {dashboard?.total_revenue?.toLocaleString() || 0} {t("common.tnd")}
                                        </p>
                                    </div>
                                </div>
                            </Card>
                        </Col>

                        <Col xs={24} sm={12} lg={6}>
                            <Card className="hover:shadow-sm transition-shadow">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-md" style={{backgroundColor:"var(--third-color_light)"}}>
                                        <AlertTriangle className="w-6 h-6" style={{color:"var(--third-color)"}}/>
                                    </div>
                                    <div className={`${currentLang === "ar" ? "mr-4 text-right" : "ml-4 text-left"}`}>
                                        <p className="text-gray-600 text-sm">{t("dashboard.paid")}</p>
                                        {loading ? (
                                            <Spin size="small" />
                                        ) : (
                                            <p className="text-2xl font-semibold">
                                                {typeof dashboard?.subscription_status?.PAYED === 'string'
                                                    ? parseInt(dashboard.subscription_status.PAYED, 10)
                                                    : dashboard?.subscription_status?.PAYED || 0}
                                            </p>
                                        )}

                                        <p className="text-xs text-gray-500">
                                            {t("dashboard.total")}: {dashboard?.subscription_status?.PAYED?.toLocaleString() || 0}
                                        </p>
                                    </div>
                                </div>
                            </Card>
                        </Col>

                        <Col xs={24} sm={12} lg={6}>
                            <Card className="hover:shadow-sm transition-shadow">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-md" style={{backgroundColor:"var(--fourth-color_light)"}}>
                                        <AlertTriangle className="w-6 h-6" style={{color:"var(--fourth-color)"}}/>
                                    </div>
                                    <div className={`${currentLang === "ar" ? "mr-4 text-right" : "ml-4 text-left"}`}>
                                        <p className="text-gray-600 text-sm">{t("dashboard.not_paid")}</p>
                                        {loading ? (
                                            <Spin size="small" />
                                        ) : (
                                            <p className="text-2xl font-semibold">
                                                {typeof dashboard?.subscription_status?.NOTPAYED === 'string'
                                                    ? parseInt(dashboard.subscription_status.NOTPAYED, 10)
                                                    : dashboard?.subscription_status?.NOTPAYED || 0}
                                            </p>
                                        )}
                                        <p className="text-xs text-gray-500">
                                            {t("dashboard.total")}: {dashboard?.subscription_status?.NOTPAYED?.toLocaleString() || 0}
                                        </p>
                                    </div>
                                </div>
                            </Card>
                        </Col>
                    </Row>

                    {/* ----------------- Graphiques de résumé ----------------- */}
                    <div className="flex flex-col md:flex-row justify-center gap-4">
                        <div className="w-full md:w-1/2">
                            <Card
                                title={
                                   <Title level={4} className={`!mb-0 text-gray-800 font-bold ${currentLang === "ar" ? "text-right" : "text-left"}`}>
                                        {t("dashboard.subscription_by_status")}
                                    </Title>
                                }
                            >
                                {loading ? (
                                    <div className="flex justify-center items-center h-60">
                                        <Spin size="large" />
                                    </div>
                                ) : (
                                    <Pie {...getSummaryStatusPieConfig()} />
                                )}
                            </Card>
                        </div>
                        <div className="w-full md:w-1/2">
                            <Card
                                title={
                                    <Title level={4} className={`!mb-0 text-gray-800 font-bold ${currentLang === "ar" ? "text-right" : "text-left"}`}>
                                        {t("dashboard.subscription_by_type")}
                                    </Title>
                                }
                            >
                                {loading ? (
                                    <div className="flex justify-center items-center h-60">
                                        <Spin size="large" />
                                    </div>
                                ) : (

                                    <Line {...getSummarySubscriptionTypesLineConfig()} />

                                )}
                            </Card>
                        </div>
                    </div>
                </Tabs.TabPane>
            </Tabs>
        </div>
    );
};

export default Dashboard;
