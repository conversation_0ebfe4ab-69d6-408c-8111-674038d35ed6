import {message, Modal} from "antd";
import <PERSON><PERSON><PERSON>, {Area} from "react-easy-crop";
import {ZoomInIcon, ZoomOutIcon} from "lucide-react";
import {useTranslation} from "react-i18next";
import {useCallback, useState} from "react";
import getCroppedImg from "../../tools/cropImage.ts";


const CropModal:any = ({
form,
setCroppedImage,
imageSrc,
cropModalVisible,
setCropModalVisible
}:any) => {
    const {t} = useTranslation();
    const [crop, setCrop] = useState({ x: 0, y: 0 });
    const [zoom, setZoom] = useState(1);
    const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

    const onCropComplete = useCallback((_: Area, croppedAreaPixels: Area) => {
        setCroppedAreaPixels(croppedAreaPixels);
    }, []);

    const handleCropConfirm = async () => {
        try {
            if(imageSrc && croppedAreaPixels) {
                // Get the cropped image as a File object
                const croppedImageFile = await getCroppedImg(
                    imageSrc,
                    croppedAreaPixels
                );

                // Create a preview URL for display purposes
                const previewUrl = URL.createObjectURL(croppedImageFile);

                // Set the cropped image for preview
                setCroppedImage(previewUrl);

                // Set the actual File object in the form
                form.setFieldsValue({ photo: croppedImageFile });

                setCropModalVisible(false);
                console.log('Cropped image set as File:', croppedImageFile);
                console.log('Preview URL created:', previewUrl);
            }
        } catch (e) {
            console.error('Error cropping image:', e);
            message.error("Erreur lors du recadrage de l'image");
        }
    };

    return (
            <Modal
                width={700}
                title={t("manage_newSubs.cropImage")}
                open={cropModalVisible}
                onCancel={() => setCropModalVisible(false)}
                onOk={handleCropConfirm}
                okText={t("manage_newSubs.confirmCrop")}
                cancelText={t("manage_newSubs.cancel")}
            >
                <div
                    style={{
                        width: "100%",
                        height: 350,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        overflow: "hidden",
                        borderRadius: 8
                    }}
                >
                    {imageSrc && (
                        <div
                            style={{
                                width: 300,
                                height: 300,
                                position: "relative"
                            }}
                        >
                            <Cropper
                                image={imageSrc}
                                crop={crop}
                                cropSize={{width: 300, height: 300}}
                                zoom={zoom}
                                aspect={1}
                                cropShape={"round"}
                                onCropChange={setCrop}
                                onZoomChange={setZoom}
                                onCropComplete={onCropComplete}
                                classes={{
                                    containerClassName: "crop-container",
                                    mediaClassName: "crop-media"
                                }}
                            />
                        </div>
                    )}
                </div>

                <div style={{
                    marginTop: 24,
                    padding: '0 24px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 16
                }}>
                    <ZoomOutIcon style={{color: '#8c8c8c'}}/>
                    <input
                        type="range"
                        value={zoom}
                        min={1}
                        max={3}
                        step={0.1}
                        onChange={(e) => setZoom(Number(e.target.value))}
                        className="custom-range"
                    />
                    <ZoomInIcon style={{color: '#8c8c8c'}}/>
                </div>
            </Modal>
    )
}

export default CropModal;