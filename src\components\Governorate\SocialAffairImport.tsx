import { useState, useEffect } from "react";
import {
    Modal,
    Upload,
    Button,
    message,
    Alert,
    Typography,
    Space,
    Select,
    Form,
    Divider
} from "antd";
import { UploadOutlined, DownloadOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { importSocialAffairs } from "../../features/admin/socialAffairSlice.ts";
import { getAcademicYearsAll } from "../../features/admin/academicYearSlice.ts";
import type { UploadFile, UploadProps } from "antd/es/upload/interface";

const { Text, Link } = Typography;

interface SocialAffairImportProps {
    modalVisible: boolean;
    onCancel: () => void;
    governorateId: number;
    onSuccess: () => void;
}

function SocialAffairImport({
    modalVisible,
    onCancel,
    governorateId,
    onSuccess
}: SocialAffairImportProps) {
    const { t } = useTranslation();
    const dispatch: any = useDispatch();
    const [form] = Form.useForm();

    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [uploading, setUploading] = useState(false);
    const [academicYears, setAcademicYears] = useState<any[]>([]);
    const [loadingAcademicYears, setLoadingAcademicYears] = useState(false);

    // Fetch academic years
    useEffect(() => {
        const fetchAcademicYears = async () => {
            setLoadingAcademicYears(true);
            try {
                const result = await dispatch(getAcademicYearsAll()).unwrap().then((originalPromiseResult:any) => {
                    setAcademicYears(originalPromiseResult.data);
                    return originalPromiseResult.data;
                })
                .catch((rejectedValueOrSerializedError:any) => {
                    console.log(rejectedValueOrSerializedError);
                });
            } catch (error) {
                console.error("Failed to fetch academic years:", error);
            } finally {
                setLoadingAcademicYears(false);
            }
        };

        fetchAcademicYears();
    }, [dispatch]);

    const handleUpload = async () => {
        const values = await form.validateFields();
        const formData = new FormData();
        fileList.forEach((file) => {
            formData.append('file', file as any);
        });
        formData.append('governorate_id', governorateId.toString());

        // Add academic year if selected
        if (values.academic_year_id) {
            formData.append('academic_year_id', values.academic_year_id.toString());
        }

        setUploading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        try {
            await dispatch(importSocialAffairs(formData)).unwrap();

            setFileList([]);

            toast.update(toastId, {
                render: t("social_affairs.import_success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });

            onSuccess();
            onCancel();
        } catch (error: any) {
            console.error(error);

            toast.update(toastId, {
                render: error.message || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setUploading(false);
        }
    };

    const uploadProps: UploadProps = {
        onRemove: (file) => {
            const index = fileList.indexOf(file);
            const newFileList = fileList.slice();
            newFileList.splice(index, 1);
            setFileList(newFileList);
        },
        beforeUpload: (file) => {
            const isCSVOrExcel =
                file.type === 'text/csv' ||
                file.type === 'application/vnd.ms-excel' ||
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

            if (!isCSVOrExcel) {
                message.error(t("social_affairs.file_type_error"));
                return Upload.LIST_IGNORE;
            }

            setFileList([file]);
            return false;
        },
        fileList,
        maxCount: 1,
    };

    // Function to download template
    const downloadTemplate = () => {
        // This would typically download a template file from the server
        // For now, we'll just show a message
        message.info(t("social_affairs.template_download_info"));
    };

    return (
        <Modal
            width={700}
            title={t("social_affairs.import")}
            open={modalVisible}
            onCancel={onCancel}
            footer={[
                <Button key="back" onClick={onCancel}>
                    {t("common.cancel")}
                </Button>,
                <Button
                    key="submit"
                    type="primary"
                    loading={uploading}
                    onClick={handleUpload}
                    disabled={fileList.length === 0}
                >
                    {uploading ? t("social_affairs.uploading") : t("social_affairs.start_upload")}
                </Button>,
            ]}
        >
            <Form form={form} layout="vertical">
                <Space direction="vertical" className="w-full">
                    <Alert
                        message={t("social_affairs.import_instructions_title")}
                        description={
                            <div>
                                <p>{t("social_affairs.import_instructions_1")}</p>
                                <p>{t("social_affairs.import_instructions_2")}</p>
                                <p>{t("social_affairs.import_instructions_3")}</p>
                            </div>
                        }
                        type="info"
                        showIcon
                    />

                    <div className="mt-4 mb-4">
                        <Button
                            icon={<DownloadOutlined />}
                            onClick={downloadTemplate}
                        >
                            {t("social_affairs.download_template")}
                        </Button>
                    </div>

                    <Form.Item
                        label={t("social_affairs.labels.academic_year")}
                        name="academic_year_id"
                    >
                        <Select
                            placeholder={t("social_affairs.placeholders.academic_year")}
                            loading={loadingAcademicYears}
                            allowClear
                            style={{ width: '100%' }}
                        >
                            {academicYears?.map((year) => (
                                <Select.Option key={year.id} value={year.id}>
                                    {year.code}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>

                    <Divider />

                    <Upload {...uploadProps} className="w-full">
                        <Button icon={<UploadOutlined />}>{t("social_affairs.select_file")}</Button>
                    </Upload>

                    <div className="mt-4">
                        <Text type="secondary">{t("social_affairs.supported_formats")}</Text>
                    </div>
                </Space>
            </Form>
        </Modal>
    );
}

export default SocialAffairImport;
