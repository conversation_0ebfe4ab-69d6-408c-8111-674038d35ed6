@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #BC0202;
  --secondary-color: #5F8670;
  --third-color: #ea8618;
  --fourth-color: #3e3e3e;
  --primary-color_light: rgba(220, 38, 38, 0.15);
  --secondary-color_light: rgba(92, 183, 186, 0.15);
  --third-color_light: rgba(234, 134, 24, 0.15);
  --fourth-color_light: rgba(62, 62, 62, 0.15);

  --toastify-color-success: var(--secondary-color);
  --toastify-color-error: var(--primary-color);

  --toastify-icon-color-success: var(--secondary-color);
  --toastify-icon-color-error: var(--primary-color);

  --toastify-color-progress-success: var(--secondary-color);
  --toastify-color-progress-error: var(--primary-color);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* For Arabic (RTL) */
html[dir="rtl"] .ant-tabs-tab:nth-child(3) {
  margin: 0 !important;
}
/* For French (LTR) */
html[dir="ltr"] .ant-tabs-tab:nth-child(1) {
  margin: 0 !important;
}

/* custom language select box */
.language-select .ant-select-selector {
  margin-right: 5px !important;
  margin-left: 5px !important;
  box-shadow: none !important;
  width: 110px !important;
  font-size: 13px !important;
}
.language-select .ant-select-selector:focus,
.language-select .ant-select-selector:active {
  border: none !important;
  box-shadow: none !important;
}

/* Masquer les flèches dans un input de type number */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}

/* minimiser size msg erreur & padding */
.ant-form-item-explain-error {
  font-size: 12px !important;
}
html[dir="rtl"] .ant-form-item-explain-error {
  margin-right: 5px !important;
}
html[dir="ltr"] .ant-form-item-explain-error {
  margin-left: 5px !important;
}

/* padding auth inputs */
.auth-form .ant-input-affix-wrapper {
  padding: 10px 12px !important;
}
.auth-form .ant-input-outlined  {
  padding: 10px 12px !important;
}

/* formulaire inputs */
.form-inputs .ant-input,
.form-inputs .ant-picker {
  padding: 8px 12px !important;
  border-radius: 3px !important;
}
.ant-input-affix-wrapper,
.ant-select-single .ant-select-selector{
  border-radius: 3px !important;
}

/* CSS GLOBAL OVERRIDE MODAL */
.ant-modal-content,
.ant-pro-card,
.ant-table-wrapper .ant-table {
  border-radius: 3px !important;
}
.ant-modal-header {
  padding: 16px !important;
  border-bottom: 1px solid #ddd !important;
}
.ant-modal-title {
  font-size: 18px !important;
  font-weight: bold !important;
  text-align: center !important;
}
.ant-modal-close {
  color: var(--primary-color_light) !important;
  background-color: #fff !important;
}
.ant-modal-close:hover {
  color: var(--primary-color) !important;
}
.ant-modal-body {
  padding: 20px 20px !important;
  font-size: 14px;
}
.ant-modal-footer {
  padding: 12px !important;
  border-top: 1px solid #ddd !important;
  display: flex;
  justify-content: center;
}
.ant-modal-footer {
  display: flex;
  justify-content: end;
}

/* -------- CSS GLOBAL OVERRIDE BUTTONS --------- */
 
.ant-btn-primary,
.ant-btn-default {
    box-shadow: 1px 1px var(--fourth-color_light) !important;
    border:  none;
    border-radius: 3px !important;
}
.ant-btn-primary:hover,
.ant-btn-default:hover {
  box-shadow: -1px -1px var(--fourth-color_light) !important;
}
.ant-btn-primary,
.ant-btn-primary:hover,
.ant-btn-primary:active
.ant-btn-primary:focus-visible,
.ant-btn-primary:focus-within {
  background: var(--primary-color) !important;
  outline: none;
}
.ant-btn-default {
  color:  var(--primary-color) !important;
}
.ant-btn-variant-link:not(:disabled):not(.ant-btn-disabled):hover {
  color: var(--primary-color) !important
}
.btn-view  {
  color : var(--fourth-color) !important;
}
.btn-add {
  border: none;
  color: var(--secondary-color) !important;
  font-weight: bold;
  transition: all 0.3s ease-in-out;
}
.btn-edit{
  color : var(--secondary-color) !important;
}
.btn-cancel {
  color : var(--third-color) !important;
}
.btn-view:focus-visible {
  outline: 3px solid var(--fourth-color_light) !important;
}
.btn-add:focus-visible,
.btn-edit:focus-visible{
  outline: 3px solid var(--secondary-color_light) !important;
}
.btn-delete  {
  color : var(--primary-color) !important;
}

/* --------------- Filter forms ------------- */
.ant-pro-query-filter {
  background-image: url("./assets/images/bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.ant-pro-query-filter-collapse-button {
  font-size: 0;
}
.ant-pro-query-filter-collapse-button span {
  margin: 0 !important;
  font-size: 15px;
  background: white;
  padding: 4px;
  box-shadow: 1px 1px var(--fourth-color_light);
}
.ant-pro-query-filter-collapse-button span:hover {
  color: var(--primary-color);
  box-shadow: -1px -1px 1px var(--fourth-color_light);
}
.ant-pro-table-column-setting-action-rest-button:hover {
  color: var(--secondary-color);
}
.ant-btn-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ant-table-row-expand-icon:hover,
.ant-table-row-expand-icon,
.ant-table-row-expand-icon:active,
.ant-table-row-expand-icon:focus {
  color: var(--primary-color) !important;
}

/* Border inputs */
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper:focus-within,
.form-inputs .ant-input:hover,
.form-inputs .ant-input:focus,
.form-inputs .ant-input:focus-within{
  border-color: rgba(15, 255, 255, 0) !important;
  box-shadow: 0 0 0 2px rgb(158 158 158 / 6%) !important;
  transition: all 0.5s ease-in-out;
}

.ant-input-outlined:focus,
.ant-input-outlined:focus-within,
.form-inputs .ant-input:hover,
.form-inputs .ant-input:focus,
.form-inputs .ant-input:focus-within {
  box-shadow: 0 0 0 2px rgb(158 158 158 / 6%) !important;
}
.ant-select-selector:hover,
.ant-select-selector:focus-within,
.ant-picker:hover,
.ant-picker:focus-within{
  border-color: rgba(15, 255, 255, 0) !important;
  box-shadow: 0 0 0 2px rgb(158 158 158 / 6%) !important;
}

.password .ant-input-affix-wrapper {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 11px !important;
  padding-right: 11px !important;
  border-radius:  3px !important;
}
/* -------------- label filter forms ------------ */
.ant-pro-query-filter-row {
  row-gap: 10px !important;
}

/* -------------- Spin animation -------------- */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


/* ----------------- FADE IN Animation ------------------ */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out forwards;
}

/* -------------- side bar auth layout -------------- */
.bouncing {
  animation: bounce 1s ease infinite;
}
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-12px);
  }
}

/* -------------- side bar auth layout -------------- */
.ant-menu-item:hover{
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
}

.ant-menu-item-selected {
  background-color: var(--primary-color) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: scale(1.03);
}

.ant-menu-vertical >.ant-menu-item {
  line-height: 45px !important;
}
.ant-menu-inline-collapsed >.ant-menu-item {
  padding-inline: calc(50% - 8px - 7px) !important;
}

/* -------- CSS GLOBAL OVERRIDE DROPDOWN --------- */
.ant-dropdown-menu {
  min-width: 100px !important;
}


/* -------- CSS GLOBAL OVERRIDE SCROLL BAR --------- */

::-webkit-scrollbar {
  width:2px;
  height:0;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: var(--primary-color);

}
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.06);
}


/* -------- CSS GLOBAL OVERRIDE Tag --------- */
.ant-tag.ant-tag-green {
  background-color: var(--secondary-color_light) !important;
  color: var(--secondary-color) !important;
  border-color: var(--secondary-color) !important;
}
.ant-tag.ant-tag-red {
  background-color: var(--primary-color_light) !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* -------- CSS GLOBAL OVERRIDE HOVER SELECT BOX --------- */
.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
  background-color: white !important;
}
.ant-menu-submenu-title:hover {
  background-color: transparent !important;
}
:where(.ant-select-dropdown) .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  background-color: transparent !important;
}
:where(.css-dev-only-do-not-override-9b31nm).ant-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled),
:where(.ant-select-dropdown) .ant-select-item-option:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
}
:where(.ant-menu-light:not(.ant-menu-horizontal)) .ant-menu-item:not(.ant-menu-item-selected):active {
  background-color: var(--primary-color_light) !important;
}

/* -------- CSS GLOBAL NOTIFICATIONS --------- */
.custom-notification {
  position: relative;
  overflow: hidden;
}
.custom-notification::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background:  white;
  animation: progressBar 3.5s linear forwards;
}
@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

.form-inputs :where(.css-dev-only-do-not-override-18wcqr0).ant-select-single {
  height: 36px !important;
}

/* - -------------- routes --------------- -*/
.wrapper {
  display: none;
}
.wrapper.show {
  display: flex;
  align-items: center;
}
.point {
  visibility: hidden;
  text-align: center;
  display: flex;
  gap: 5px;
  align-items: center;
  flex-direction: column;
}
.point.show {
  visibility: visible;
}
.line {
  position: absolute;
  left: 80px;
  top: 20px;
  border:1px dashed var(--primary-color);
  width: 0;
  transition: width 1s ease-in-out;
}
.line.show {
  width: calc(100% - 200px);
}
.station {
  width: 150px;
  overflow: hidden;
}
.depart {
  color: var(--primary-color);
  position: absolute;
  left: 0;
}
.arrival {
  color: var(--primary-color);
  position: absolute;
  right: 0;
}

:where(.css-dev-only-do-not-override-18wcqr0).ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
  background-color: transparent !important;
}
.ant-form-item-feedback-icon-success {
  color: var(--secondary-color) !important;
}
.ant-form-item-feedback-icon-error {
  color: var(--primary-color) !important;
}

/* ------------------- CSS GLOBAL FOR RANGE INPUTS ---------------- */
.custom-range {
  flex: 1;
  border-radius: 3px;
  background: #f0f0f0;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  width: 80%;
  height: 4px;
  transition: 0.2s;
}
/* Style pour le point du range (Webkit - Chrome, Safari) */
.custom-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
}
/* Style pour Firefox */
.custom-range::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
}
@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}
.animate-bounce-slow {
  animation: bounce-slow 2s infinite;
}

/* ------------------- CSS GLOBAL FOR SCROLLABLE DISPLAY LINE ---------------- */
.scrollable-steps-container {
  padding-top: 68px;
  overflow-x: auto;
  padding: 20px 10px;
  margin: 0 -10px;
}
.scrollable-steps-container::-webkit-scrollbar {
  height: 8px;
}
.scrollable-steps-container::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  border-radius: 3px;
}
.scrollable-steps-container::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 3px;
  border: 2px solid #f1f1f1;
}
.scrollable-steps-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-color_light);
}
.scrollable-steps-container {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) #f1f1f1;
}
.scrollable-steps-container:hover {
  cursor: pointer;
}

.ant-picker-now {
  font-size: 12px;
}
.ant-picker-now a:hover {
  color: var(--primary-color) !important;
}
.ant-picker-ranges {
  gap:5px;
}

.form-inputs .ant-select-single {
  height: 36px !important;
}
.expand-custom .ant-pro-query-filter {
  background: white !important;
}
.expand-custom .ant-btn-color-default,
.expand-custom .ant-btn-primary {
   background: white !important;
   color: #0b2e13 !important;
}

.form-inputs .ant-select-single {
  height: 36px !important;
}

.ant-select-multiple .ant-select-selector {
  padding-block: 3px !important;
}


