import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {PermissionsManager} from "../../../components";
import {useDispatch} from "react-redux";
import {deleteRole, getRoles, storeRole, updateRole} from "../../../features/auth/roleSlice.ts";
import {toast, ToastContainer} from "react-toastify";


function ManageRolePerm() {
    const {t} = useTranslation();

    const dispatch = useDispatch();
    const actionRef = useRef<any>();

    const [_, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false); // Mode d'affichage

    const [editingRole, setEditingRole] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);


    {/*|--------------------------------------------------------------------------
    | FETCH ALL ROLES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */}
    const handleGetRoles = (params:any,sort:any,filter:any) =>
        dispatch(getRoles({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });

    {/*|--------------------------------------------------------------------------
    | COLUMNS & BREADCRUMB
    |-------------------------------------------------------------------------- */}
    const columns: any = [
        {
            title: `${t("manage_rolePerm.labels.name")}`,
            dataIndex: "name",
            responsive: ["xs", "sm", "md", "lg"],
            sorter: true,
            render: (_: any, data: any) => data.name,
        },
        {
            title: `${t("manage_rolePerm.labels.guard_name")}`,
            search: false,
            dataIndex: "guard_name",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_rolePerm.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 130,
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_rolePerm.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_rolePerm.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_rolePerm.yes")}
                        cancelText={t("manage_rolePerm.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.security")}</Link>,
        },
        {
            title: t("manage_rolePerm.title"),
        },
    ];


    {/*|--------------------------------------------------------------------------
    | HANDLE MODAL & VIEW & ADD & EDIT
    |-------------------------------------------------------------------------- */}
    const handleView = (record: any) => {
        setEditingRole(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingRole(record);
        form.setFieldsValue(record);
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingRole(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE STORE & UPDATE ROLE
    |-------------------------------------------------------------------------- */}
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = editingRole ? { id: editingRole.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingRole) {
                await dispatch(updateRole(payload)).unwrap();
            } else {
                await dispatch(storeRole(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            handleReset()
        }
    };
    const confirmSubmit = (values: any) => {
        Modal.confirm({
            title: t("manage_rolePerm.confirmAction"),
            content: editingRole ? t("manage_rolePerm.confirmUpdate") : t("manage_rolePerm.confirmAdd"),
            okText: t("manage_rolePerm.yes"),
            cancelText: t("manage_rolePerm.no"),
            onOk: () => handleFormSubmit(values),
            centered:true,
        });
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE DELETE ROLE
    |-------------------------------------------------------------------------- */}
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteRole(id)).unwrap()
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    {/*|--------------------------------------------------------------------------
    | RESET FUNCTION
    |-------------------------------------------------------------------------- */}
    const handleReset = () => {
        form.resetFields();
        setModalVisible(false);
        setEditingRole(null);
        setLoading(false)
    };

    return (
        <>
            <ToastContainer />
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    {/*|--------------------------------------------------------------------------
                    |   - PRO_TABLE
                    |-------------------------------------------------------------------------- */}
                    <ProTable
                        headerTitle={t("manage_rolePerm.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetRoles(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        expandable={{
                            expandedRowRender: (record:any) => (
                                <PermissionsManager roleId={record.id} />
                            ),
                        }}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_rolePerm.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>


            {/*|--------------------------------------------------------------------------
            |   - MODAL & FORM
            |-------------------------------------------------------------------------- */}
            <Modal
                width={700}
                title={
                    viewMode
                        ? t("manage_rolePerm.details")
                        : editingRole
                            ? t("manage_rolePerm.edit")
                            : t("manage_rolePerm.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_rolePerm.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    initialValues={{
                        guard_name: "api"
                    }}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Form.Item
                        label={t("manage_rolePerm.labels.name")}
                        name="name"
                        rules={[{ required: true, message: t("manage_rolePerm.errors.nameRequired") }]}
                    >
                        <Input
                            placeholder={t("manage_rolePerm.placeholders.name")}
                            disabled={viewMode}
                        />
                    </Form.Item>

                    <Form.Item
                        label={t("manage_rolePerm.labels.guard_name")}
                        name="guard_name"
                        rules={[{ required: false, message: t("manage_rolePerm.errors.guard_nameRequired") }]}
                    >
                        <Input
                            value="api"
                            placeholder={t("manage_rolePerm.placeholders.guard_name")}
                            disabled={true}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}

export default ManageRolePerm;
