import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/transactions";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const storeTransaction: any = createAsyncThunk(
    "storeTransaction",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Combined function to process payment (store transaction and update subscription in one request)
export const processPayment: any = createAsyncThunk(
    "processPayment",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}/process-payment`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTransactions: any = createAsyncThunk(
    "getTransactions",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const sort = data.sort;
            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;

            // Handle search parameters
            const searchParams = [];

            // Extract all possible filter parameters from data.params
            const {
                subscription_id, client_id, amount, payment_date,
                payment_mode, payment_method_id, status, transaction_reference
            } = data.params;

            if (subscription_id) searchParams.push(`subscription_id:${subscription_id}`);
            if (client_id) searchParams.push(`client_id:${client_id}`);
            if (amount) searchParams.push(`amount:${amount}`);
            if (payment_date) searchParams.push(`payment_date:${payment_date}`);
            if (payment_mode) searchParams.push(`payment_mode:${payment_mode}`);
            if (payment_method_id) searchParams.push(`payment_method_id:${payment_method_id}`);
            if (status) searchParams.push(`status:${status}`);
            if (transaction_reference) searchParams.push(`transaction_reference:${transaction_reference}`);

            // Add search parameters to URL
            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(";")}`;
            }

            // Handle sorting
            const orderBy = [];
            const sortedBy = [];
            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === "ascend" ? "asc" : "desc");
            }
            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const cancelTransaction: any = createAsyncThunk(
  "cancelTransaction",
  async (id: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}/cancel`;
      const resp: any = await api.post(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

const transactionSlice = createSlice({
    name: 'transaction',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // Store Transaction
            .addCase(storeTransaction.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeTransaction.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(storeTransaction.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            // Process Payment (combined transaction + subscription update)
            .addCase(processPayment.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(processPayment.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(processPayment.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            // Get Transactions
            .addCase(getTransactions.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTransactions.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
                state.error = null;
            })
            .addCase(getTransactions.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            // Cancel Transaction
            .addCase(cancelTransaction.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(cancelTransaction.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(cancelTransaction.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    }
});

export const { setCurrentItem, clearError } = transactionSlice.actions;
export default transactionSlice.reducer;
