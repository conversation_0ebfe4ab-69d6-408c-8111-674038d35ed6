import { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Col,
  Form,
  Input,
  Modal,
  Popconfirm,
  Row,
  Select,
  Spin,
  Tag,
} from "antd";
import { DeleteOutlined, EditOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { useDispatch } from "react-redux";
import {
  deleteTrip,
  getTrips,
  storeTrip,
  updateTrip,
} from "../../../features/admin/tripsSlice.ts";
import { getTarifBasesAll } from "../../../features/admin/tarifBaseSlice.ts";
import { toast } from "react-toastify";
import { getAbnTypesAll } from "../../../features/admin/abnTypeSlice.ts";
import {
  getLinesAll,
  getLinesForStationsConfig,
} from "../../../features/admin/lineSlice.ts";
import {
  getStationsAll,
  getArrivalStationsByDeparture,
} from "../../../features/admin/stationSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";
import { useSelector } from "react-redux";
import dayjs from "dayjs";

function ManageRoutes() {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  const dispatch: any = useDispatch();

  const actionRef = useRef<any>();

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editingRoutes, setEditingRoutes] = useState<any>(null);
  const [form] = Form.useForm();

  const [arrivalStations, setArrivalStations] = useState<any[]>([]);
  const [availableLines, setAvailableLines] = useState<any[]>([]);
  const [selectedStationDepart, setSelectedStationDepart] = useState<
    number | null
  >(null);
  const [selectedStationArrival, setSelectedStationArrival] = useState<
    number | null
  >(null);
  const [selectedABNType, setSelectedABNType] = useState<string[]>([]);
  const [loadingArrivalStations, setLoadingArrivalStations] =
    useState<boolean>(false);

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState(1);

  const abnTypes = useSelector((state: any) => state.abnType.items.data);
  const baseTariffData = useSelector(
    (state: any) => state.tarifBase.items.data
  );
  const lines = useSelector((state: any) => state.line.items.data);
  const stations = useSelector((state: any) => state.station.items.data);

  /*|--------------------------------------------------------------------------
    | FETCH ALL DATA
    |-------------------------------------------------------------------------- */
  const fetchStoreData = async () => {
    try {
      setLoading(true);

      const promises = [];

      if (!abnTypes?.length) {
        promises.push(dispatch(getAbnTypesAll()).unwrap());
      }
      if (!baseTariffData?.length) {
        promises.push(dispatch(getTarifBasesAll()).unwrap());
      }
      if (!lines?.length) {
        promises.push(dispatch(getLinesAll()).unwrap());
      }
      if (!stations?.length) {
        promises.push(dispatch(getStationsAll()).unwrap());
      }
      await Promise.all(promises);
    } catch (error) {
      console.error("Error fetching initial data:", error);
      toast.error(t("common.errors.unexpected"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStoreData();
  }, []);

  const handleDepartureStationChange = async (stationId: number) => {
    setSelectedStationDepart(stationId);
    setSelectedStationArrival(null);
    setArrivalStations([]);
    setAvailableLines([]);
    form.setFieldsValue({ station_end: undefined, lines: undefined });

    // activate this to fetch arrival stations based on departure station
    // if (!stationId) return;
    // setLoadingArrivalStations(true);
    // try {
    //     const result = await dispatch(getArrivalStationsByDeparture({
    //         departureStationId: stationId
    //     })).unwrap();

    //     setArrivalStations(result.data || []);
    // } catch (error) {
    //     console.error('Error fetching arrival stations:', error);
    //     toast.error(t("messages.error"));
    // } finally {
    //     setLoadingArrivalStations(false);
    // }
  };

  const handleArrivalStationChange = async (stationId: number) => {
    setSelectedStationArrival(stationId);
    setAvailableLines([]);

    if (!selectedStationDepart || !stationId) return;

    setLoading(true);
    try {
      const result = await dispatch(
        getLinesForStationsConfig({
          departureStationId: selectedStationDepart,
          arrivalStationId: stationId,
        })
      ).unwrap();

      setAvailableLines(result.data || []);
    } catch (error) {
      console.error("Error fetching lines by stations:", error);
      toast.error(t("messages.error"));
    } finally {
      setLoading(false);
    }
  };

  /*|--------------------------------------------------------------------------
    | FETCH TRIPS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
  const handleGetTrips = (params: any, sort: any, filter: any) => {
    return dispatch(
      getTrips({
        pageNumber,
        perPage: pageSize,
        params,
        sort,
        filter,
      })
    )
      .unwrap()
      .then((originalPromiseResult: any) => {
        setTotal(originalPromiseResult.meta.total);
        return originalPromiseResult.data;
      })
      .catch((rejectedValueOrSerializedError: any) => {
        console.log(rejectedValueOrSerializedError);
      });
  };

  /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
  const columns: any = [
    {
      title: t("manage_routes.labels.name"),
      dataIndex: `nom_${currentLang}`,
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => record[`nom_${currentLang}`],
    },
    {
      title: t("manage_routes.labels.station_depart"),
      dataIndex: "station_start",
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => {
        return data.station_start
          ? data.station_start[`nom_${currentLang}`]
          : "-";
      },
      renderFormItem: () => (
        <Select
          placeholder={t("manage_routes.placeholders.station_depart")}
          showSearch
          filterOption={(input, option) =>
            (option?.label as string)
              ?.toLowerCase()
              ?.includes(input.toLowerCase())
          }
          allowClear
          options={stations?.map((el: any) => ({
            label: el[`nom_${currentLang}`] || el.name || "-",
            value: el.id,
          }))}
        />
      ),
    },
    {
      title: t("manage_routes.labels.station_arrival"),
      dataIndex: "station_end",
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => {
        return data.station_end ? data.station_end[`nom_${currentLang}`] : "-";
      },
      renderFormItem: () => (
        <Select
          allowClear
          showSearch
          filterOption={(input, option) =>
            (option?.label as string)
              ?.toLowerCase()
              ?.includes(input.toLowerCase())
          }
          placeholder={t("manage_routes.placeholders.station_arrival")}
          options={stations?.map((el: any) => ({
            label: el[`nom_${currentLang}`] || el.name || "-",
            value: el.id,
          }))}
        />
      ),
    },
    {
      title: t("manage_routes.labels.lines"),
      dataIndex: "lines",
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => {
        if (record.lines && record.lines.length > 0) {
          return (
            <div className="flex flex-wrap gap-1">
              {record.lines?.map((line: any) => (
                <Tag key={line.id} color="blue" className="text-xs">
                  {line.CODE_LINE}
                </Tag>
              ))}
            </div>
          );
        } else if (record.line) {
          return (
            <Tag color="blue" className="text-xs">
              {record.line.CODE_LINE}
            </Tag>
          );
        }
        return "-";
      },
    },
    {
      title: t("manage_routes.labels.subsTypes"),
      dataIndex: "id_subs_type",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => {
        if (record.tariff_options && record.tariff_options.length > 0) {
          return (
            <div className="flex flex-wrap gap-1">
              {record.tariff_options?.map((option: any) => {
                const subsType = abnTypes?.find(
                  (type: any) => type.id === option.id_subs_type
                );
                return subsType ? (
                  <Tag
                    key={option.id_subs_type}
                    color={subsType.color || "default"}
                    className="text-xs"
                  >
                    {subsType[`nom_${currentLang}`]}
                  </Tag>
                ) : null;
              })}
            </div>
          );
        }
        return "-";
      },
      renderFormItem: () => (
        <Select
          showSearch
          filterOption={(input, option) =>
            (option?.label as string)
              ?.toLowerCase()
              ?.includes(input.toLowerCase())
          }
          allowClear
          placeholder={t("manage_routes.filters.subsType")}
          options={abnTypes?.map((el: any) => ({
            label: el[`nom_${currentLang}`] || el.name || "-",
            value: el.id,
          }))}
        />
      ),
    },
    {
      title: t("manage_routes.labels.numberOfKm"),
      dataIndex: "number_of_km",
      key: "number_of_km",
      sorter: true,
      render: (text: any) => (text === "-" ? "-" : `${text} ${t("common.km")}`),
    },
    {
      title: t("manage_routes.labels.regular"),
      dataIndex: "is_regular",
      search: false,
      hidden: true,
      render: (is_regular: any) => (
        <Tag color={!is_regular ? "success" : "error"}>
          {!is_regular
            ? t("manage_routes.options_regular.yes")
            : t("manage_routes.options_regular.no")}
        </Tag>
      ),
      renderFormItem: () => (
        <Select placeholder={t("manage_routes.filters.regular")}>
          <Select.Option value="TRUE">
            {t("manage_routes.options_regular.yes")}
          </Select.Option>
          <Select.Option value="FALSE">
            {t("manage_routes.options_regular.no")}
          </Select.Option>
        </Select>
      ),
    },
    {
      title: t("manage_routes.labels.status"),
      dataIndex: "status",
      width: 100,
      responsive: ["xs", "sm", "md", "lg"],
      valueType: "select",
      render: (_: any, record: any) => (
        <Tag color={record.status === true ? "success" : "error"}>
          {record.status === true ? t("common.active") : t("common.inactive")}
        </Tag>
      ),
      valueEnum: {
        "1": {
          text: t("common.active"),
          status: "Success",
        },
        "0": {
          text: t("common.inactive"),
          status: "Error",
        },
      },
    },
    {
      title: t("manage_routes.labels.createdAt"),
      dataIndex: "created_at",
      valueType: "date",
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: t("manage_routes.labels.actions"),
      fixed: "right",
      width: 170,
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => (
        <div className="flex gap-1">
          <Button
            className="btn-view"
            icon={<EyeOutlined />}
            onClick={() => handleOpenModal(record, true)}
          />
          {hasPermission("edit_routes") && (
            <Button
              className="btn-edit"
              icon={<EditOutlined />}
              onClick={() => handleOpenModal(record, false)}
            />
          )}
          {hasPermission("delete_routes") && (
            <Popconfirm
              title={t("manage_routes.confirmDelete")}
              onConfirm={() => handleDelete(record.id)}
              okText={t("manage_routes.yes")}
              cancelText={t("manage_routes.no")}
            >
              <Button className="btn-delete" icon={<DeleteOutlined />} danger />
            </Popconfirm>
          )}
        </div>
      ),
    },
  ];
  const breadcrumbItems = [
    {
      title: (
        <Link className="!bg-white" to="/auth/commercial-dashboard">
          {t("auth_sidebar.categories.transport")}
        </Link>
      ),
    },
    {
      title: t("manage_routes.title"),
    },
  ];

  /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
  const handleAdd = () => {
    setEditingRoutes(null);
    form.resetFields();
    setViewMode(false);
    setModalVisible(true);
    setSelectedABNType([]);
  };
  const handleOpenModal = (record: any, isViewMode: boolean) => {
    setLoading(true);
    setEditingRoutes(record);
    setViewMode(isViewMode);

    const formValues: any = {
      nom_fr: record.nom_fr,
      nom_ar: record.nom_ar,
      nom_en: record.nom_en,
      status: record.status,
      number_of_km: record.number_of_km,
      line: record.line?.id,
    };

    if (record.tariff_options && Array.isArray(record.tariff_options)) {
      formValues.tariff_types = record.tariff_options.reduce(
        (acc: any, curr: any) => {
          const key = curr.id_subs_type;
          const abnType = abnTypes.find(
            (type: any) => type.id === curr.id_subs_type
          );

          if (curr.is_regular) {
            acc[key] = {
              is_regular: curr.is_regular,
              type_abn_id: curr.id_subs_type,
              tariff: curr.manual_tariff,
            };
          } else {
            if (abnType?.is_student === true) {
              acc[key] = {
                is_regular: curr.is_regular,
                type_abn_id: curr.id_subs_type,
                tariff: curr.id_tariff_base,
              };
            } else {
              acc[key] = {
                is_regular: curr.is_regular,
                type_abn_id: curr.id_subs_type,
                manual_tariff: curr.manual_tariff,
              };
            }
          }
          return acc;
        },
        {}
      );

      const selectedTypes = record.tariff_options?.map(
        (t: any) => t.id_subs_type
      );
      formValues.abnType = selectedTypes;
      setSelectedABNType(selectedTypes);
    }

    if (record.station_start) {
      formValues.station_start = record.station_start.id;
      setSelectedStationDepart(record.station_start.id);

      if (!isViewMode) {
        handleDepartureStationChange(record.station_start.id).then(async () => {
          if (record.station_end) {
            form.setFieldsValue({ station_end: record.station_end.id });
            setSelectedStationArrival(record.station_end.id);

            if (record.station_start) {
              const result = await dispatch(
                getLinesForStationsConfig({
                  departureStationId: record.station_start.id,
                  arrivalStationId: record.station_end.id,
                })
              ).unwrap();
              setAvailableLines(result.data || []);
            }
          }
        });
      } else {
        // In view mode, set the values directly
        if (record.station_end) {
          formValues.station_end = record.station_end.id;
          setSelectedStationArrival(record.station_end.id);
        }
      }
    } else if (record.station_end && isViewMode) {
      // If no departure station but has arrival station (view mode only)
      formValues.station_end = record.station_end.id;
      setSelectedStationArrival(record.station_end.id);
    }

    if (record.lines && record.lines.length > 0) {
      const lineIds = record.lines?.map((line: any) => line.id);
      formValues.lines = lineIds;
      setAvailableLines(record.lines);
    } else if (record.line?.id) {
      formValues.lines = [record.line.id];
      setAvailableLines([record.line]);
    }

    form.setFieldsValue(formValues);
    setModalVisible(true);
    setLoading(false);
  };

  /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE ROUTE
    |-------------------------------------------------------------------------- */
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      const stations = {
        id_station_start: values.station_start,
        id_station_end: values.station_end,
      };

      const tariff_options: any = Object.entries(values.tariff_types)?.map(
        ([typeId, data]: [string, any]) => {
          const abnType = abnTypes.find(
            (type: any) => type.id === Number(typeId)
          );

          if (data.is_regular) {
            return {
              id_subs_type: Number(typeId),
              is_regular: data.is_regular,
              manual_tariff: data.tariff,
            };
          } else {
            if (abnType?.is_student === true) {
              return {
                id_subs_type: Number(typeId),
                is_regular: data.is_regular,
                id_tariff_base: data.tariff,
              };
            } else {
              return {
                id_subs_type: Number(typeId),
                is_regular: data.is_regular,
                manual_tariff: Number(data.manual_tariff),
              };
            }
          }
        }
      );

      const selectedLineIds = values.lines || [];

      const payload = {
        nom_fr: values.nom_fr,
        nom_ar: values.nom_ar,
        nom_en: values.nom_en,
        number_of_km: Number(values.number_of_km),
        status: values.status,
        line_ids: selectedLineIds,
        stations,
        tariff_options,
        inter_station: false,
      };

      if (editingRoutes) {
        await dispatch(
          updateTrip({ id: editingRoutes.id, ...payload })
        ).unwrap();
      } else {
        await dispatch(storeTrip(payload)).unwrap();
      }

      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
      handleReset();
    } catch (error: any) {
      const fieldErrors: any = Object.keys(error.errors || {})?.map(
        (field: any) => ({
          name: field,
          errors: [error.errors[field][0]],
        })
      );
      form.setFields(fieldErrors);
      toast.update(toastId, {
        render: t("messages." + error.message) || t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
    }
  };
  const confirmSubmit: any = (values: any) => {
    const modal: any = Modal.confirm({
      title: t("manage_routes.confirmAction"),
      content: editingRoutes
        ? t("manage_routes.confirmUpdate")
        : t("manage_routes.confirmAdd"),
      okText: t("common.yes"),
      cancelText: t("common.no"),
      onOk: async () => {
        modal.destroy();
        await handleFormSubmit(values);
      },
      centered: true,
    });
  };

  /*|--------------------------------------------------------------------------
    |  - DELETE ROUTE
    |-------------------------------------------------------------------------- */
  const handleDelete = async (id: number) => {
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      await dispatch(deleteTrip(id)).unwrap();

      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
    } catch (error: any) {
      toast.update(toastId, {
        render: t("messages." + error.message) || t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    }
  };

  /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
  const handleReset = () => {
    setModalVisible(false);
    setViewMode(false);
    setSelectedABNType([]);
    setSelectedStationDepart(null);
    setSelectedStationArrival(null);
    setArrivalStations([]);
    setAvailableLines([]);
    setLoadingArrivalStations(false);
    form.resetFields();
  };

  return (
    <>
      <Breadcrumb className="mb-5" items={breadcrumbItems} />

      <Row>
        <Col span={24}>
          <ProTable
            headerTitle={t("manage_routes.title")}
            columns={columns}
            actionRef={actionRef}
            cardBordered
            rowKey={"id"}
            request={async (params: any, sort: any, filter: any) => {
              setLoading(true);
              const dataFilter: any = await handleGetTrips(
                params,
                sort,
                filter
              );
              setLoading(false);
              return {
                data: dataFilter,
                success: true,
              };
            }}
            pagination={{
              pageSize,
              total,
              onChange: (page) => setPageNumber(page),
              onShowSizeChange: (_, size) => setPageSize(size),
            }}
            sortDirections={["ascend", "descend"]}
            scroll={{
              x: 800,
            }}
            search={{
              labelWidth: "auto",
              className: "bg-[#FAFAFA]",
            }}
            loading={loading}
            toolBarRender={() => [
              <Button key="button" onClick={handleAdd} className="btn-add">
                {t("manage_routes.add")}
              </Button>,
            ]}
          />
        </Col>
      </Row>

      <Modal
        width={1200}
        title={
          viewMode
            ? t("manage_routes.details")
            : editingRoutes
            ? t("manage_routes.edit")
            : t("manage_routes.add")
        }
        open={modalVisible}
        onCancel={handleReset}
        onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
        okText={viewMode ? null : t("manage_routes.save")}
        footer={viewMode ? null : undefined}
        destroyOnClose
      >
        <Spin spinning={loadingArrivalStations}>
          {viewMode ? (
            <div className="p-4">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {t("manage_delegations.labels.name_fr")}
                    </div>
                    <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-base font-medium">
                      {editingRoutes?.nom_fr || "-"}
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {t("manage_delegations.labels.name_en")}
                    </div>
                    <div className="bg-purple-50 text-purple-700 px-3 py-2 rounded-md text-base font-medium">
                      {editingRoutes?.nom_en || "-"}
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {t("manage_delegations.labels.name_ar")}
                    </div>
                    <div className="bg-emerald-50 text-emerald-700 px-3 py-2 rounded-md text-base font-medium">
                      {editingRoutes?.nom_ar || "-"}
                    </div>
                  </div>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {t("manage_routes.labels.numberOfKm")}
                    </div>
                    <div className="bg-amber-50 text-amber-700 px-3 py-2 rounded-md text-base font-medium">
                      {editingRoutes?.number_of_km
                        ? `${editingRoutes.number_of_km} km`
                        : "-"}
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {t("manage_routes.labels.status")}
                    </div>
                    <div className="bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                      <Tag color={editingRoutes?.status ? "success" : "error"}>
                        {editingRoutes?.status
                          ? t("common.active")
                          : t("common.inactive")}
                      </Tag>
                    </div>
                  </div>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {t("manage_routes.labels.station_depart")}
                    </div>
                    <div className="bg-indigo-50 text-indigo-700 px-3 py-2 rounded-md text-base font-medium">
                      {editingRoutes?.station_start?.[`nom_${currentLang}`] ||
                        "-"}
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {t("manage_routes.labels.station_arrival")}
                    </div>
                    <div className="bg-pink-50 text-pink-700 px-3 py-2 rounded-md text-base font-medium">
                      {editingRoutes?.station_end?.[`nom_${currentLang}`] ||
                        "-"}
                    </div>
                  </div>
                </Col>
              </Row>

              <div className="mb-4">
                <div className="text-sm font-medium text-gray-500 mb-1">
                  {t("manage_routes.labels.lines")}
                </div>
                <div className="bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                  <div className="flex flex-wrap gap-1">
                    {editingRoutes?.lines && editingRoutes.lines.length > 0 ? (
                      editingRoutes.lines.map((line: any) => (
                        <div
                          key={line.id}
                          className="text-sm font-medium text-blue-700"
                        >
                          {line.CODE_LINE} - {line[`nom_${currentLang}`]}{" "}
                          <span className="text-gray-600 font-medium"> | </span>
                        </div>
                      ))
                    ) : editingRoutes?.line ? (
                      <Tag color="blue" className="text-sm">
                        {editingRoutes.line.CODE_LINE} -{" "}
                        {editingRoutes.line[`nom_${currentLang}`]}
                      </Tag>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </div>
                </div>
              </div>

              {editingRoutes?.tariff_options &&
                editingRoutes.tariff_options.length > 0 && (
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-500 mb-2">
                      {t("manage_routes.labels.abnType")}
                    </div>
                    <div className="space-y-3">
                      {editingRoutes.tariff_options.map((tariffOption: any) => {
                        const abnType = abnTypes.find(
                          (type: any) => type.id === tariffOption.id_subs_type
                        );
                        const baseTariff = baseTariffData.find(
                          (tariff: any) =>
                            tariff.id === tariffOption.id_tariff_base
                        );

                        return (
                          <div
                            key={tariffOption.id_subs_type}
                            className="bg-gray-50 border border-gray-200 p-3 rounded-md"
                          >
                            <div className="flex items-center gap-2 mb-2">
                              <div
                                style={{
                                  width: "12px",
                                  height: "12px",
                                  borderRadius: "50%",
                                  backgroundColor: abnType?.color || "#ccc",
                                }}
                              />
                              <span className="font-medium text-gray-700">
                                {abnType?.[`nom_${currentLang}`] || "-"}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <span className="text-xs text-gray-500">
                                  Type de tarif:
                                </span>
                                <div className="mt-1">
                                  <Tag
                                    color={
                                      tariffOption.is_regular
                                        ? "success"
                                        : "orange"
                                    }
                                  >
                                    {t("manage_routes.labels.regular")} :{" "}
                                    {tariffOption.is_regular
                                      ? t("manage_routes.options_regular.yes")
                                      : t("manage_routes.options_regular.no")}
                                  </Tag>
                                </div>
                              </div>
                              <div>
                                <span className="text-xs text-gray-500">
                                  {!tariffOption.is_regular
                                    ? abnType?.is_student
                                      ? "Base tarifaire:"
                                      : "Tarif manuel:"
                                    : "Tarif calculé automatiquement:"}
                                </span>
                                <div className="mt-1 font-medium text-gray-700">
                                  {!tariffOption.is_regular
                                    ? abnType?.is_student
                                      ? (() => {
                                          // Get the latest valid tariff (date_subscription before or equal to today)
                                          const validTariffs =
                                            baseTariff?.tariffs?.filter(
                                              (tariff: any) => {
                                                const tariffDate = dayjs(
                                                  tariff.date_subscription
                                                );
                                                const today = dayjs();
                                                return (
                                                  tariffDate.isBefore(today) ||
                                                  tariffDate.isSame(
                                                    today,
                                                    "day"
                                                  )
                                                );
                                              }
                                            ) || [];

                                          // Sort by date and get the latest valid one
                                          const latestValidTariff =
                                            validTariffs.length > 0
                                              ? validTariffs.sort(
                                                  (a: any, b: any) =>
                                                    dayjs(
                                                      b.date_subscription
                                                    ).diff(
                                                      dayjs(a.date_subscription)
                                                    )
                                                )[0]
                                              : null;

                                          const tariffValue = latestValidTariff
                                            ? latestValidTariff.tariff
                                            : 0;
                                          return `${
                                            baseTariff?.[
                                              `nom_${currentLang}`
                                            ] || "-"
                                          } (${tariffValue} TND)`;
                                        })()
                                      : `${tariffOption.manual_tariff || 0} TND`
                                    : "Basé sur la distance"}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
            </div>
          ) : (
            <Form
              className="form-inputs"
              form={form}
              layout="vertical"
              onFinish={confirmSubmit}
            >
              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label={t("manage_delegations.labels.name_fr")}
                    name="nom_fr"
                    rules={[
                      {
                        required: true,
                        message: t("manage_delegations.errors.nameFrRequired"),
                      },
                    ]}
                  >
                    <Input
                      placeholder={t("manage_delegations.placeholders.name_fr")}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label={t("manage_delegations.labels.name_en")}
                    name="nom_en"
                    rules={[
                      {
                        required: true,
                        message: t("manage_delegations.errors.nameEnRequired"),
                      },
                    ]}
                  >
                    <Input
                      placeholder={t("manage_delegations.placeholders.name_en")}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label={t("manage_delegations.labels.name_ar")}
                    name="nom_ar"
                    rules={[
                      {
                        required: true,
                        message: t("manage_delegations.errors.nameArRequired"),
                      },
                    ]}
                  >
                    <Input
                      placeholder={t("manage_delegations.placeholders.name_ar")}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={24}>
                  <Form.Item
                    label={t("manage_routes.labels.abnType")}
                    name="abnType"
                    rules={[
                      {
                        required: true,
                        message: t("manage_routes.errors.abnTypeRequired"),
                      },
                    ]}
                  >
                    <Select
                      disabled={viewMode || loading}
                      mode="multiple"
                      loading={loading}
                      placeholder={t("manage_routes.placeholders.abnType")}
                      onChange={setSelectedABNType}
                    >
                      {abnTypes?.map((el: any) => (
                        <Select.Option key={el.id} value={el.id}>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: "8px",
                            }}
                          >
                            <div
                              style={{
                                width: "12px",
                                height: "12px",
                                borderRadius: "50%",
                                backgroundColor: el.color,
                              }}
                            />
                            {el[`nom_${currentLang}`]}
                          </div>
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {selectedABNType && selectedABNType.length > 0 && (
                <>
                  {selectedABNType?.map((abnTypeId) => {
                    const key: any = String(abnTypeId);
                    const abnType: any = abnTypes.find(
                      (item: any) => item.id === abnTypeId
                    );
                    const baseTariffsForType = baseTariffData.filter(
                      (tariffBase: any) => {
                        const matchesType =
                          tariffBase.subs_type.id === abnTypeId;
                        const isNotRegularTariff = !tariffBase.is_regular;
                        // get first tariffs that have closest date subcription before today
                        const isDateValid = tariffBase.tariffs?.find(
                          (tariff: any) => {
                            const tariffDate = dayjs(tariff.date_subscription);
                            const today = dayjs();
                            return tariffDate.isBefore(today);
                          }
                        );
                        return matchesType && isNotRegularTariff && isDateValid;
                      }
                    );

                    return (
                      <Row gutter={[16, 16]} key={abnTypeId}>
                        <Col xs={24} sm={12}>
                          <Form.Item
                            label={`${t("manage_routes.labels.regular")} (${
                              abnType[`nom_${currentLang}`]
                            })`}
                            name={["tariff_types", key, "is_regular"]}
                            rules={[
                              {
                                required: true,
                                message: t(
                                  "manage_routes.errors.isRegularRequired"
                                ),
                              },
                            ]}
                          >
                            <Select
                              disabled={viewMode}
                              placeholder={t(
                                "manage_routes.placeholders.regular"
                              )}
                              onChange={() => {
                                form.setFieldsValue({
                                  tariff_types: {
                                    [key]: {
                                      tariff: null,
                                      manual_tariff: null,
                                    },
                                  },
                                });
                              }}
                            >
                              <Select.Option value={true}>
                                {t("manage_routes.options_regular.yes")}
                              </Select.Option>
                              <Select.Option value={false}>
                                {t("manage_routes.options_regular.no")}
                              </Select.Option>
                            </Select>
                          </Form.Item>

                          <Form.Item
                            name={["tariff_types", key, "type_abn_id"]}
                            initialValue={abnTypeId}
                            style={{ display: "none" }}
                          >
                            <Input type="hidden" />
                          </Form.Item>
                        </Col>

                        <Col xs={24} sm={12}>
                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                              prevValues?.tariff_types?.[key]?.is_regular !==
                              curValues?.tariff_types?.[key]?.is_regular
                            }
                          >
                            {({ getFieldValue }) => {
                              const isRegular = getFieldValue([
                                "tariff_types",
                                key,
                                "is_regular",
                              ]);
                              if (isRegular === false) {
                                // Check if the ABN type is for students
                                // if (abnType?.is_student === true) {
                                // Show tariff base selectbox for student types
                                return (
                                  <Form.Item
                                    key={`base-${key}-${isRegular}`}
                                    label={`${t(
                                      "manage_routes.labels.base_tariff"
                                    )} (${abnType[`nom_${currentLang}`]})`}
                                    name={["tariff_types", key, "tariff"]}
                                    rules={[
                                      {
                                        required: true,
                                        message: t(
                                          "manage_routes.errors.baseTariffRequired"
                                        ),
                                      },
                                    ]}
                                  >
                                    <Select
                                      disabled={viewMode}
                                      placeholder={t(
                                        "manage_routes.placeholders.base_tariff"
                                      )}
                                    >
                                      {baseTariffsForType?.map((el: any) => {
                                        const validTariffs = el.tariffs?.filter((tariff: any) => {
                                            const tariffDate = dayjs(tariff.date_subscription);
                                            const today = dayjs();
                                            return ( tariffDate.isBefore(today) || tariffDate.isSame(today, "day"));
                                          }) || [];

                                        const latestValidTariff =
                                          validTariffs.length > 0
                                            ? validTariffs.sort(
                                                (a: any, b: any) =>
                                                  dayjs(
                                                    b.date_subscription
                                                  ).diff(
                                                    dayjs(a.date_subscription)
                                                  )
                                              )[0]
                                            : null;

                                        const tariffValue = latestValidTariff
                                          ? latestValidTariff.tariff
                                          : 0;

                                        return (
                                          <Select.Option
                                            key={el.id}
                                            value={el.id}
                                            className="group hover:text-white"
                                          >
                                            {el[`nom_${currentLang}`]} -{" "}
                                            <span className="text-green-600 group-hover:text-white">
                                              {tariffValue} TND
                                            </span>
                                          </Select.Option>
                                        );
                                      })}
                                    </Select>
                                  </Form.Item>
                                );
                                // } else {
                                //     return (
                                //         <Form.Item
                                //             key={`manual-${key}-${isRegular}`}
                                //             label={`${t("manage_routes.labels.manual_tariff")} (${abnType[`nom_${currentLang}`]})`}
                                //             name={["tariff_types", key, "manual_tariff"]}
                                //             rules={[
                                //                 {
                                //                     required: true,
                                //                     message: t("manage_routes.errors.manualTariffRequired"),
                                //                 },
                                //             ]}
                                //         >
                                //             <Input
                                //                 type="number"
                                //                 min={0}
                                //                 disabled={viewMode}
                                //                 placeholder={t("manage_routes.placeholders.manual_tariff")}
                                //             />
                                //         </Form.Item>
                                //     );
                                // }
                              } else if (isRegular === true) {
                                return (
                                  <div className="bg-yellow-50 border border-yellow-200 rounded p-4 mb-4 flex items-center gap-3">
                                    <div className="flex-shrink-0">
                                      <svg
                                        className="w-5 h-5 text-yellow-600"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                          clipRule="evenodd"
                                        />
                                      </svg>
                                    </div>
                                    <div className="flex-1">
                                      <p className="text-sm font-medium text-black-800 mb-1">
                                        {t("manage_routes.autoCalculation")}
                                      </p>
                                      <p className="text-xs text-black-600">
                                        {t(
                                          "manage_routes.autoCalculationDescription"
                                        )}
                                      </p>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          </Form.Item>
                        </Col>
                      </Row>
                    );
                  })}
                </>
              )}

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={t("manage_routes.labels.numberOfKm")}
                    name="number_of_km"
                    rules={[
                      {
                        required: true,
                        message: t("manage_routes.errors.numberOfKmRequired"),
                      },
                    ]}
                  >
                    <Input
                      type="number"
                      placeholder={t("manage_routes.placeholders.numberOfKm")}
                      disabled={viewMode}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="status"
                    label={t("manage_routes.labels.status")}
                    initialValue={true}
                    rules={[{ required: true, message: t("common.required") }]}
                  >
                    <Select
                      disabled={viewMode}
                      options={[
                        { label: t("common.active"), value: true },
                        { label: t("common.inactive"), value: false },
                      ]}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <div className="mt-2 mb-4 font-semibold bg-gray-100 p-4 text-center">
                <h3>{t("manage_routes.select_depart_arrival")}</h3>
              </div>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={t("manage_routes.labels.station_depart")}
                    name="station_start"
                    rules={[
                      {
                        required: true,
                        message: t(
                          "manage_routes.errors.stationDepartRequired"
                        ),
                      },
                    ]}
                  >
                    <Select
                      disabled={viewMode}
                      placeholder={t(
                        "manage_routes.placeholders.station_depart"
                      )}
                      onChange={handleDepartureStationChange}
                      showSearch
                      filterOption={(input, option: any) =>
                        (option?.children as string)
                          ?.toLowerCase()
                          ?.includes(input.toLowerCase())
                      }
                    >
                      {stations?.map((station: any) => (
                        <Select.Option key={station.id} value={station.id}>
                          {station[`nom_${currentLang}`]}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={t("manage_routes.labels.station_arrival")}
                    name="station_end"
                    rules={[
                      {
                        required: true,
                        message: t(
                          "manage_routes.errors.stationArrivalRequired"
                        ),
                      },
                    ]}
                  >
                    <Select
                      disabled={viewMode || !selectedStationDepart}
                      placeholder={
                        !selectedStationDepart
                          ? t(
                              "manage_routes.placeholders.select_departure_first"
                            )
                          : t("manage_routes.placeholders.station_arrival")
                      }
                      onChange={handleArrivalStationChange}
                      showSearch
                      filterOption={(input, option: any) =>
                        (option?.children as string)
                          ?.toLowerCase()
                          ?.includes(input.toLowerCase())
                      }
                      loading={
                        loadingArrivalStations && !!selectedStationDepart
                      }
                      notFoundContent={
                        loadingArrivalStations && selectedStationDepart ? (
                          <div className="flex items-center justify-center py-2">
                            <span className="mr-2">
                              <Spin size="small" />
                            </span>
                            {t("common.loading")}
                          </div>
                        ) : !selectedStationDepart ? (
                          t("manage_routes.placeholders.select_departure_first")
                        ) : (
                          t("common.no_data")
                        )
                      }
                    >
                      {loadingArrivalStations && selectedStationDepart ? (
                        <Select.Option disabled value="loading">
                          <div className="flex items-center justify-center py-2">
                            <span className="mr-2">
                              <Spin size="small" />
                            </span>
                            {t("common.loading")}
                          </div>
                        </Select.Option>
                      ) : (
                        stations?.map((station: any) => (
                          <Select.Option key={station.id} value={station.id}>
                            {station[`nom_${currentLang}`]}
                          </Select.Option>
                        ))
                      )}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {selectedStationDepart && selectedStationArrival && (
                <Row gutter={[16, 16]}>
                  <Col xs={24}>
                    <Form.Item
                      label={t("manage_routes.labels.lines")}
                      name="lines"
                      rules={[
                        {
                          required: true,
                          message: t("manage_routes.errors.linesRequired"),
                        },
                      ]}
                    >
                      <Select
                        disabled={viewMode}
                        mode="multiple"
                        placeholder={t("manage_routes.placeholders.lines")}
                        loading={loading}
                        showSearch
                        filterOption={(input, option: any) =>
                          (option?.children as string)
                            ?.toLowerCase()
                            ?.includes(input.toLowerCase())
                        }
                        notFoundContent={
                          loading ? (
                            <div className="flex items-center justify-center py-2">
                              <span className="mr-2">
                                <Spin size="small" />
                              </span>
                              {t("common.loading")}
                            </div>
                          ) : (
                            t("common.no_data")
                          )
                        }
                      >
                        {loading ? (
                          <Select.Option disabled value="loading">
                            <div className="flex items-center justify-center py-2">
                              <span className="mr-2">
                                <Spin size="small" />
                              </span>
                              {t("common.loading")}
                            </div>
                          </Select.Option>
                        ) : (
                          availableLines?.map((line: any) => (
                            <Select.Option key={line.id} value={line.id}>
                              {line.CODE_LINE}{" "}
                              {line[`nom_${currentLang}`]
                                ? `- ${line[`nom_${currentLang}`]}`
                                : ""}
                            </Select.Option>
                          ))
                        )}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}
            </Form>
          )}
        </Spin>
      </Modal>
    </>
  );
}

export default ManageRoutes;
