import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {toast} from "react-toastify";
import {
    getPeriodicity,
    storePeriodicity,
    updatePeriodicity,
    deletePeriodicity
} from "../../../features/admin/periodicitySlice";
import {periodicityOptions} from "../../../data/StaticData";
import { hasPermission } from "../../../helpers/permissions";



function ManagePeiodicities() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const dispatch = useDispatch();
    const actionRef = useRef<any>();
    const [form] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingPeriodicity, setEditingPeriodicity] = useState<any>(null);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
    | FETCH ALL PERIODICITIES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetPeriodicities = (params: any, sort: any, filter: any) =>
        dispatch(getPeriodicity({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data.map((item: any) => ({
                    ...item,
                    key: item.id
                }));
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.error(rejectedValueOrSerializedError);
                return [];
            });

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_periodicities.labels.id")}`,
            dataIndex: "id",
            search: false,
            width: 60,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_periodicities.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_periodicities.labels.periodicityType")}`,
            dataIndex: "periodicity_code",
            responsive: ["xs", "sm", "md", "lg"],
            search:false,
        },
        {
            title: `${t("manage_periodicities.labels.maxReposDaysNumber")}`,
            dataIndex: "max_days_per_week",
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_periodicities.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 100,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_periodicities.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_periodicities") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {/*<Popconfirm
                        title={t("manage_periodicities.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_periodicities.yes")}
                        cancelText={t("manage_periodicities.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>*/}
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.sales")}</Link>,
        },
        {
            title: t("manage_periodicities.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingPeriodicity(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
    };

    const handleEdit = (record: any) => {
        setEditingPeriodicity(record);
        form.setFieldsValue(record);
        setViewMode(false);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingPeriodicity(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE SUBMIT
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });

        try {
            if (editingPeriodicity) {
                await dispatch(updatePeriodicity({
                    id: editingPeriodicity.id,
                    ...values
                })).unwrap();
            } else {
                await dispatch(storePeriodicity(values)).unwrap();
            }

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });

            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_periodicities.confirmAction"),
            content: editingPeriodicity
                ? t("manage_periodicities.confirmUpdate")
                : t("manage_periodicities.confirmAdd"),
                okText: t("common.yes"),
                cancelText: t("common.no"),
                onOk: async () => {
                    modal.destroy();
                    await handleFormSubmit(values);
                },
                centered: true,
        });
    };


    /*|--------------------------------------------------------------------------
    |  - HANDLE DELETE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });

        try {
            await dispatch(deletePeriodicity(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            actionRef.current?.reload();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setViewMode(false);
        form.resetFields();
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_periodicities.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetPeriodicities(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: setPageNumber,
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                            search: false,
                            reload: true,
                            setting: false,
                        }}
                        loading={loading}
                        /*toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_periodicities.add")}
                            </Button>,
                        ]}*/
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_periodicities.details")
                        : editingPeriodicity
                            ? t("manage_periodicities.edit")
                            : t("manage_periodicities.add")
                }
                open={modalVisible}
                onCancel={handleReset}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_periodicities.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={loading}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={true}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        label={t("manage_periodicities.labels.maxReposDaysNumber")}
                        name="max_days_per_week"
                        rules={[{ required: true, message: t("manage_periodicities.errors.maxReposDaysNumberRequired") }]}
                    >
                        <Input
                            type="number"
                            placeholder={t("manage_periodicities.placeholders.maxReposDaysNumber")}
                            disabled={viewMode}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}

export default ManagePeiodicities;
