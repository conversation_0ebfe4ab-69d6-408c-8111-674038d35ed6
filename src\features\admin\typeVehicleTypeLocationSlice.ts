import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/type-vehicle-type-locations";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getTypeVehicleTypeLocationsAll: any = createAsyncThunk(
    "getTypeVehicleTypeLocationsAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehicleTypeLocationsByVehicleType: any = createAsyncThunk(
    "getTypeVehicleTypeLocationsByVehicleType",
    async (typeVehiculeId: number, thunkAPI:any) => {
        try {
            const url:string = `${URL}/type-vehicule/${typeVehiculeId}`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehicleTypeLocationsByLocationType: any = createAsyncThunk(
    "getTypeVehicleTypeLocationsByLocationType",
    async (locationTypeId: number, thunkAPI:any) => {
        try {
            const url:string = `${URL}/type-location/${locationTypeId}`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehicleTypeLocations: any = createAsyncThunk(
    "getTypeVehicleTypeLocations",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { id_type_vehicule, id_type_location, km_min, status } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (id_type_vehicule) {
                searchParams.push(`id_type_vehicule:${id_type_vehicule}`);
            }
            if (id_type_location) {
                searchParams.push(`id_type_location:${id_type_location}`);
            }
            if (km_min) {
                searchParams.push(`km_min:${km_min}`);
            }
            if (status !== undefined) {
                searchParams.push(`status:${status}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTypeVehicleTypeLocationById: any = createAsyncThunk(
    "getTypeVehicleTypeLocationById",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeTypeVehicleTypeLocation: any = createAsyncThunk(
    "storeTypeVehicleTypeLocation",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateTypeVehicleTypeLocation: any = createAsyncThunk(
    "updateTypeVehicleTypeLocation",
    async (data: any, thunkAPI: any) => {
        try {
            const { id, ...payload } = data;
            const url: string = `${URL}/${id}`;
            const resp: any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteTypeVehicleTypeLocation: any = createAsyncThunk(
    "deleteTypeVehicleTypeLocation",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const bulkStoreTypeVehicleTypeLocation: any = createAsyncThunk(
    "bulkStoreTypeVehicleTypeLocation",
    async (data: any[], thunkAPI: any) => {
        try {
            const url: string = `${URL}/bulk`;
            const resp: any = await api.post(url, { items: data });
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const typeVehicleTypeLocationSlice = createSlice({
    name: 'typeVehicleTypeLocation',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getTypeVehicleTypeLocationsAll
            .addCase(getTypeVehicleTypeLocationsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehicleTypeLocationsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTypeVehicleTypeLocationsAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getTypeVehicleTypeLocationsByVehicleType
            .addCase(getTypeVehicleTypeLocationsByVehicleType.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehicleTypeLocationsByVehicleType.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTypeVehicleTypeLocationsByVehicleType.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getTypeVehicleTypeLocationsByLocationType
            .addCase(getTypeVehicleTypeLocationsByLocationType.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehicleTypeLocationsByLocationType.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTypeVehicleTypeLocationsByLocationType.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getTypeVehicleTypeLocations
            .addCase(getTypeVehicleTypeLocations.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehicleTypeLocations.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getTypeVehicleTypeLocations.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getTypeVehicleTypeLocationById
            .addCase(getTypeVehicleTypeLocationById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTypeVehicleTypeLocationById.fulfilled, (state, action) => {
                state.loading = false;
                state.currentItem = action.payload;
            })
            .addCase(getTypeVehicleTypeLocationById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // storeTypeVehicleTypeLocation
            .addCase(storeTypeVehicleTypeLocation.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeTypeVehicleTypeLocation.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(storeTypeVehicleTypeLocation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // updateTypeVehicleTypeLocation
            .addCase(updateTypeVehicleTypeLocation.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateTypeVehicleTypeLocation.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateTypeVehicleTypeLocation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // deleteTypeVehicleTypeLocation
            .addCase(deleteTypeVehicleTypeLocation.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteTypeVehicleTypeLocation.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deleteTypeVehicleTypeLocation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = typeVehicleTypeLocationSlice.actions;
export default typeVehicleTypeLocationSlice.reducer;
