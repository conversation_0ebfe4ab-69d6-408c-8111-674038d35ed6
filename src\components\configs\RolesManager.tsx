import { <PERSON>, <PERSON><PERSON>, <PERSON>, Spin } from "antd";
import { ArrowLeftCircle, ArrowRightCircle } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {toast, ToastContainer} from "react-toastify";
import {assignRolesToUser, getAssUnassRoles, removeRolesFromUser} from "../../features/auth/roleSlice.ts";

function RolesManager({ adminId }: { adminId: number }) {
    const { t, i18n } = useTranslation();
    const dispatch = useDispatch();

    const [assignedRoles, setAssignedRoles] = useState<any[]>([]);
    const [unassignedRoles, setUnassignedRoles] = useState<any[]>([]);
    const [selectedAssignedKeys, setSelectedAssignedKeys] = useState<string[]>([]);
    const [selectedUnassignedKeys, setSelectedUnassignedKeys] = useState<string[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
        fetchAssignedUnassignedRoles();
    }, [dispatch, adminId]);

    {/*|--------------------------------------------------------------------------
    | FETCH ALL ASSIGNED & UNASSIGNED ROLE FOR THE ADMIN
    |-------------------------------------------------------------------------- */}
    const fetchAssignedUnassignedRoles = async () => {
        setLoading(true);
        try {
            const response = await dispatch(getAssUnassRoles(adminId));
            const { assigned, unassigned } = response.payload;
            setAssignedRoles(assigned);
            setUnassignedRoles(unassigned);
        } catch (error) {
            console.log(error)
        } finally {
            handleReset()
        }
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE ASSIGN ROLE TO AN ADMIN
    |-------------------------------------------------------------------------- */}
    const assignRoles = async () => {
        setLoading(true);
        if (!selectedUnassignedKeys.length) return;
        const roleIds = selectedUnassignedKeys.map(Number);

        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(assignRolesToUser({ adminId, roleIds })).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });

            setAssignedRoles([...assignedRoles, ...unassignedRoles.filter(role => roleIds.includes(role.id))]);
            setUnassignedRoles(unassignedRoles.filter(role => !roleIds.includes(role.id)));
            setSelectedUnassignedKeys([]);
        } catch (error) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE REMOVE ROLE FROM AN ADMIN
    |-------------------------------------------------------------------------- */}
    const removeRoles = async () => {
        setLoading(true);
        if (!selectedAssignedKeys.length) return;
        const roleIds = selectedAssignedKeys.map(Number);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(removeRolesFromUser({ adminId, roleIds })).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            setUnassignedRoles([...unassignedRoles, ...assignedRoles.filter(role => roleIds.includes(role.id))]);
            setAssignedRoles(assignedRoles.filter(role => !roleIds.includes(role.id)));
            setSelectedAssignedKeys([]);
        } catch (error) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            handleReset()
        }
    };

    {/*|--------------------------------------------------------------------------
    | RESET FUNCTION
    |-------------------------------------------------------------------------- */}
    const handleReset = () => {
        setLoading(false)
    }

    return (
        <div style={{ display: "flex", flexWrap: "wrap", gap: "20px", justifyContent: "center" }}>
            <ToastContainer />
            <Card title={t('manage_rolePerm.roles.unassigned_roles')} className="w-[300px] shadow-sm hover:shadow-none">
                <Spin spinning={loading}>
                    <Tree
                        checkable
                        treeData={unassignedRoles?.map(role => ({ title: role.name, key: role.id.toString() }))}
                        onCheck={(_, { checkedNodes }) => setSelectedUnassignedKeys(checkedNodes.map(node => node.key.toString()))}
                    />
                </Spin>
            </Card>

            <div
                style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "15px",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <Button onClick={assignRoles} disabled={selectedUnassignedKeys.length === 0 || loading} className="btn-add">
                    {i18n.language === "ar" ? <ArrowLeftCircle /> : <ArrowRightCircle />}
                </Button>
                <Button onClick={removeRoles} disabled={selectedAssignedKeys.length === 0 || loading} className="btn-delete">
                    {i18n.language === "ar" ? <ArrowRightCircle /> : <ArrowLeftCircle />}
                </Button>
            </div>

            <Card title={t('manage_rolePerm.roles.assigned_roles')} className="w-[300px] shadow-sm hover:shadow-none">
                <Spin spinning={loading}>
                    <Tree
                        checkable
                        treeData={assignedRoles?.map(role => ({ title: role.name, key: role.id.toString() }))}
                        onCheck={(_, { checkedNodes }) => setSelectedAssignedKeys(checkedNodes.map(node => node.key.toString()))}
                    />
                </Spin>
            </Card>
        </div>
    );
}

export default RolesManager;
