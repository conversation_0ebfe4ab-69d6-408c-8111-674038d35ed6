import {
    <PERSON><PERSON>,
    Card,
    Col,
    DatePicker,
    Empty,
    Form,
    Input,
    Modal,
    Popconfirm,
    Row,
    Select,
    Space,
    Typography,
    Badge,
    Tooltip
} from "antd";
import {CalendarOutlined, DeleteOutlined, EditOutlined, EyeOutlined, PlusOutlined, PhoneOutlined, EnvironmentOutlined, GlobalOutlined} from "@ant-design/icons";
import moment from "moment/moment";
import {useTranslation} from "react-i18next";
import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { Spin } from "antd";
import { toast } from "react-toastify";
import { Switch } from "antd";
import { getAgencySalesPoints } from "../../features/admin/agencySlice";
import { deleteSalePoint, storeSalePoint, updateSalePoint } from "../../features/admin/salePointSlice";
import { getAbnTypesAll } from "../../features/admin/abnTypeSlice";
import { getGovernorateAll } from "../../features/admin/governorateSlice";
import { getDelegationsByGovernorate } from "../../features/admin/delegationSlice";
import { useSelector } from "react-redux";

const AgencySalesPointsManager = ({ agencyId }: { agencyId: number })  => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const dispatch = useDispatch<any>();

    const [viewMode, setViewMode] = useState(false);
    const [salesPeriodForm] = Form.useForm();
    const [editingSalesPoint, setEditingSalesPoint] = useState<any>(null);
    const [salesPointModal, setSalesPointModal] = useState(false);
    const [salesPoints, setSalesPoints] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);

    const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<number | null>(null);
    const [filteredDelegations, setFilteredDelegations] = useState<any[]>([]);
    const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);

    const governorates = useSelector((state: any) => state.governorate.items.data);


    /*|--------------------------------------------------------------------------
    | FETCH ALL SALES POINTS FOR THE SELECTED AGENCY
    |-------------------------------------------------------------------------- */
    useEffect(() => {
        fetchSalesPointsByAgency();
    }, [agencyId]);

    const fetchSalesPointsByAgency = async () => {
        try {
            setLoading(true);
            const result = await dispatch(getAgencySalesPoints(agencyId)).unwrap();
            setSalesPoints(result.data || []);
        } catch (error: any) {
            toast.error(`${t("manage_agencies.error")}: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const fetchStoreData = async () => {
        try {
            setLoading
            const promises = [];
            if(!governorates?.length){
                promises.push(dispatch(getGovernorateAll()).unwrap());
            }
            await Promise.all(promises);
        } catch (error) {
            console.error('Error fetching initial data:', error);
            toast.error(t("common.errors.unexpected"));
        } finally {
            setLoading(false);
        }
    }
    useEffect(() => {
        setLoading(true);
        fetchStoreData();
    }, []);

    /*|--------------------------------------------------------------------------
    | HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleAddSalesPoint = () => {
        setEditingSalesPoint(null);
        salesPeriodForm.resetFields();
        setSalesPointModal(true);
    };
    const handleEditSalesPoint = async(record: any) => {
        setEditingSalesPoint(record);
        console.log(record);

        if (record.governorate) {
            await handleGovernorateChange(record.governorate.id);
        }
                           
        salesPeriodForm.setFieldsValue({
            nom_fr: record.nom_fr,
            nom_en: record.nom_en,
            nom_ar: record.nom_ar,
            id_governorate: record.governorate?.id || null,
            id_delegation: record.delegation?.id || null,
            contact: record.contact,
            address: record.address,
            status: record.status
        });
        setSalesPointModal(true);
        setViewMode(false);
    };

    const handleViewSalesPoint = async(record: any) => {
        setEditingSalesPoint(record);

        if (record.governorate) {
            await handleGovernorateChange(record.governorate.id);
        }
                           
        salesPeriodForm.setFieldsValue({
            nom_fr: record.nom_fr,
            nom_en: record.nom_en,
            nom_ar: record.nom_ar,
            id_governorate: record.governorate?.id || null,
            id_delegation: record.delegation?.id || null,
            contact: record.contact,
            address: record.address,
            status: record.status
        });
        setSalesPointModal(true);
        setViewMode(true);
    };

    const handleGovernorateChange: any = async (governorateId: number | null) => {
                salesPeriodForm.setFieldsValue({ id_delegation: null });
                setSelectedFilterGovernorate(governorateId);
                setIsDelegationsLoading(true);
                setFilteredDelegations([]);
                if (!governorateId) {
                    setFilteredDelegations([]);
                    setIsDelegationsLoading(false);
                    return;
                }
                try {
                    const response:any = await dispatch(getDelegationsByGovernorate(governorateId)).unwrap();
                    setFilteredDelegations(response.data || []);
                } catch (error) {
                    console.error('Error fetching delegations:', error);
                    toast.error(t("common.errors.unexpected"));
                } finally {
                    setIsDelegationsLoading(false);
                }
            };
    

    /*|--------------------------------------------------------------------------
    | HANDLE SUBMIT
    |-------------------------------------------------------------------------- */
    const handleSalesPointSubmit = async (values: any) => {
        setLoading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {   
            const payload = {
                ...values,
                id_agency: agencyId
            }
            if (editingSalesPoint) {
                await dispatch(updateSalePoint({
                    id: editingSalesPoint.id,
                    ...payload
                })).unwrap();
            } else {
                await dispatch(storeSalePoint(payload)).unwrap();
            }

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });

            handleReset();
            await fetchSalesPointsByAgency();
            setSalesPointModal(false);
            handleReset()
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            salesPeriodForm.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({        
            title: t("manage_salesPoints.confirmAction"),
            content: editingSalesPoint
                ? t("manage_salesPoints.confirmUpdate")
                : t("manage_salesPoints.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleSalesPointSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE SALES POINT
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            await dispatch(deleteSalePoint(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            await fetchSalesPointsByAgency();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        }
    }

    /*|--------------------------------------------------------------------------
    | HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setEditingSalesPoint(null);
        setSalesPointModal(false);
        salesPeriodForm.resetFields();
    };
   
    return (
        <>
            <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
                    <h4 className="text-xl font-semibold text-gray-800">
                        {t("manage_agencies.salesPoints")}
                    </h4>
                    {!loading && salesPoints.length > 0 && (
                        <Button
                            type="dashed"
                            onClick={() => handleAddSalesPoint()}
                            className="w-full sm:w-auto"
                        >
                            {t("manage_agencies.addSalesPoint")}
                        </Button>
                    )}
                </div>

                {loading ? (
                    <div className="flex justify-center items-center py-8">
                        <Spin size="large" />
                    </div>
                ) : salesPoints.length > 0 ? (
                    <Row gutter={[16, 16]}>
                        {salesPoints.map((record: any) => (
                            <Col xs={24} sm={12} md={8} lg={8} xl={6} key={record.id}>
                                <Card
                                    className="h-full shadow-sm border border-gray-200 rounded-lg overflow-hidden"
                                    actions={[
                                        <Button
                                            type="text"
                                            icon={<EyeOutlined className="btn-view"/>}
                                            onClick={() => handleViewSalesPoint(record)}
                                        />,
                                        <Button
                                            type="text"
                                            icon={<EditOutlined className="btn-edit"/>}
                                            onClick={() => handleEditSalesPoint(record)}
                                        />,
                                        <Popconfirm
                                            title={t("manage_salesPoints.confirmDelete")}
                                            onConfirm={() => handleDelete(record.id)}
                                            okText={t("common.yes")}
                                            cancelText={t("common.no")}
                                        >
                                            <Button
                                                type="text"
                                                icon={<DeleteOutlined className="text-red-500 hover:text-red-600"/>}
                                            />
                                        </Popconfirm>
                                    ]}
                                >
                                    <Card.Meta
                                        title={
                                            <div className="flex justify-between items-center">
                                                <span className="font-medium text-gray-800">
                                                    {record[`nom_${currentLang}`]}
                                                </span>
                                                <Badge 
                                                    status={record.status ? "success" : "error"}
                                                    text={record.status ? t("common.open") : t("common.closed")}
                                                />
                                            </div>
                                        }
                                        description={
                                            <Space direction="vertical" size="small" className="w-full mt-3">
                                                <div className="flex items-start gap-2">
                                                    <PhoneOutlined className="text-gray-400 mt-1" />
                                                    <span className="text-gray-600">{record.contact}</span>
                                                </div>
                                                <div className="flex items-start gap-2">
                                                    <EnvironmentOutlined className="text-gray-400 mt-1" />
                                                    <span className="text-gray-600">{record.address}</span>
                                                </div>
                                                <div className="flex items-start gap-2">
                                                    <GlobalOutlined className="text-gray-400 mt-1" />
                                                    <span className="text-gray-600">
                                                        {record.governorate?.[`nom_${currentLang}`]} - {record.delegation?.[`nom_${currentLang}`]}
                                                    </span>
                                                </div>
                                            </Space>
                                        }
                                    />
                                </Card>
                            </Col>
                        ))}
                    </Row>
                ) : (
                    <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                            <Typography.Text type="secondary">
                                {t("manage_agencies.noSalesPoints")}
                            </Typography.Text>
                        }
                        className="py-12 rounded-lg"
                    >
                        <Button
                            type="dashed"
                            onClick={() => handleAddSalesPoint()}
                            className="mt-4"
                        >
                            {t("manage_agencies.addFirstSalesPoint")}
                        </Button>
                    </Empty>
                )}
            </div>

            <Modal
                title={t("manage_agencies.salesPeriodModal")}
                open={salesPointModal}
                onCancel={() => setSalesPointModal(false)}
                onOk={() => salesPeriodForm.submit()}
                width={900}
            >
                <Form
                    className="form-inputs"
                    form={salesPeriodForm}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_agencies.labels.name_fr")}
                                name="nom_fr"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_agencies.errors.nameFrRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_agencies.placeholders.name_fr")}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_agencies.labels.name_en")}
                                name="nom_en"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_agencies.errors.nameEnRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_agencies.placeholders.name_en")}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_agencies.labels.name_ar")}
                                name="nom_ar"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_agencies.errors.nameArRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_agencies.placeholders.name_ar")}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_agencies.labels.contact")}
                                name="contact"
                                rules={[
                                    { required: true, message: t("manage_agencies.errors.contactRequired") },
                                    { pattern: /^\d{8}$/, message: t("manage_agencies.errors.contactInvalid") }
                                ]}
                            >
                                <Input
                                    type="number"
                                    maxLength={8}
                                    placeholder={t("manage_agencies.placeholders.contact")}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_agencies.labels.address")}
                                name="address"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_agencies.errors.addressRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_agencies.placeholders.address")}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_governorate"
                                label={t("manage_agencies.labels.governorate")}
                                rules={[{ required: true, message: t("manage_agencies.errors.governorateRequired") }]}
                            >
                                <Select
                                    disabled={viewMode || loading}
                                    placeholder={t("manage_agencies.placeholders.governorate")}
                                    onChange={handleGovernorateChange}
                                    options={governorates.map((gov: any) => ({
                                        label: gov[`nom_${currentLang}`],
                                        value: gov.id,
                                    }))}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_delegation"
                                label={t("manage_agencies.labels.delegation")}
                                rules={[{ required: true, message: t("manage_agencies.errors.delegationRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_agencies.placeholders.delegation")}
                                    options={filteredDelegations.map((del: any) => ({
                                        label: del[`nom_${currentLang}`],
                                        value: del.id,
                                    }))}
                                    notFoundContent={
                                        isDelegationsLoading ? (
                                            <div className="flex items-center justify-center py-2">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                            </div>
                                        ) : (
                                            <div className="text-center py-2 text-gray-500">
                                                {!selectedFilterGovernorate
                                                    ? t("manage_agencies.selectGovernorate")
                                                    : t("common.noData")}
                                            </div>
                                        )
                                    }
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="status"
                        label={t("manage_agencies.labels.status")}
                        initialValue={true}
                        valuePropName="checked"
                    >
                        <Switch
                            checkedChildren={t("common.open")}
                            unCheckedChildren={t("common.closed")}
                            className="bg-gray-300"
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}

export default AgencySalesPointsManager;
