import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig";

const URL = '/governorate-purchase-orders';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getPurchaseOrderAll: any = createAsyncThunk(
    "getPurchaseOrderAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getPurchaseOrders: any = createAsyncThunk(
    "getPurchaseOrders",
    async (
        data: {
            governorateId: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { governorateId } = data;
            const url = `${URL}/${governorateId}`;
            const response:any = await api.get(url);
            console.log(response.data)
            return response.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

export const storePurchaseOrder = createAsyncThunk(
    "storePurchaseOrder",
    async (data: any, thunkAPI: any) => {
        try {
            const response = await api.post(URL, data);
            return response.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

export const updatePurchaseOrder:any = createAsyncThunk(
    "updatePurchaseOrder",
    async (data: any, thunkAPI: any) => {
        try {
            console.log("sdfsdfsfsfds",data)
            const { id, ...payload } = data;
            const response = await api.put(`${URL}/${id}`, payload);
            return response.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

export const deletePurchaseOrder:any = createAsyncThunk(
    "deletePurchaseOrder",
    async (id: number, thunkAPI: any) => {
        try {
            const response = await api.delete(`${URL}/${id}`);
            return response.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

const purchaseOrderSlice = createSlice({
    name: 'purchaseOrder',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getPurchaseOrderAll
            .addCase(getPurchaseOrderAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getPurchaseOrderAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getPurchaseOrderAll.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getPurchaseOrders
            .addCase(getPurchaseOrders.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getPurchaseOrders.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getPurchaseOrders.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // storePurchaseOrder
            .addCase(storePurchaseOrder.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storePurchaseOrder.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(storePurchaseOrder.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // updatePurchaseOrder
            .addCase(updatePurchaseOrder.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updatePurchaseOrder.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updatePurchaseOrder.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // deletePurchaseOrder
            .addCase(deletePurchaseOrder.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deletePurchaseOrder.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deletePurchaseOrder.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            });
    }
});

export const { setCurrentItem, clearError } = purchaseOrderSlice.actions;
export default purchaseOrderSlice.reducer;
