import { createAsyncThunk } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";



export const updateUserInfos: any = createAsyncThunk(
    "updateUserInfos",
    async (data, thunkAPI) => {
        try {
            const url:string = `/update-user-infos`;
            const resp = await api.post(url, data);
            return resp.data;
        }  catch (error: any) {
            if (error.response) {
                console.log(error.response)
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


