import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = "/degrees";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getSchoolDegreeAll: any = createAsyncThunk(
  "getSchoolDegreeAll",
  async (
    _: any,
    thunkAPI: any
  ) => {
    try {
      
      let url: any = `${URL}`;
      url += `-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getSchoolDegrees: any = createAsyncThunk(
  "getSchoolDegrees",
  async (
    data: {
      pageNumber: number;
      perPage: number;
      params: any;
      sort: any;
      filter: any;
    },
    thunkAPI: any
  ) => {
    try {
      const { nom_fr, nom_en, nom_ar, id_type_establishment, age_max } = data.params;

      const sort = data.sort;

      let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
      const searchParams = [];

      if (nom_fr) {
        searchParams.push(`nom_fr:${nom_fr}`);
      }
      if (nom_en) {
        searchParams.push(`nom_en:${nom_en}`);
      }
      if (nom_ar) {
        searchParams.push(`nom_ar:${nom_ar}`);
      }
      if (age_max) {
        searchParams.push(`age_max:${age_max}`);
      }
      if (id_type_establishment) {
        searchParams.push(`id_type_establishment:${id_type_establishment}`);
      }

      if (searchParams.length > 0) {
        url += `&search=${searchParams.join(";")}`;
      }

      const orderBy = [];
      const sortedBy = [];

      for (const [field, order] of Object.entries(sort)) {
        orderBy.push(field);
        sortedBy.push(order === "ascend" ? "asc" : "desc");
      }

      if (orderBy.length > 0) {
        url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
      }
      const joint = "&searchJoin=and";
      url += joint;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storeSchoolDegree: any = createAsyncThunk(
  "storeSchoolDegree",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const resp: any = await api.post(url, data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const updateSchoolDegree: any = createAsyncThunk(
  "updateSchoolDegree",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, ...payload } = data;
      const url: string = `${URL}/${id}`;
      const resp: any = await api.put(url, payload);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const deleteSchoolDegree: any = createAsyncThunk(
  "deleteSchoolDegree",
  async (id: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}`;
      const resp: any = await api.delete(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

const schoolDegreeSlice = createSlice({
    name: 'schoolDegree',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getSchoolDegreeAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSchoolDegreeAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getSchoolDegreeAll.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getSchoolDegrees.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSchoolDegrees.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getSchoolDegrees.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(storeSchoolDegree.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeSchoolDegree.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(storeSchoolDegree.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(updateSchoolDegree.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateSchoolDegree.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(updateSchoolDegree.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(deleteSchoolDegree.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteSchoolDegree.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(deleteSchoolDegree.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = schoolDegreeSlice.actions;
export default schoolDegreeSlice.reducer;
