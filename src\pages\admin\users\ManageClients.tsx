import { useEffect, useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col, Select, Tag,
    Switch,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import { useDispatch } from "react-redux";
import { deleteClient, getClients, storeClient, updateClient } from "../../../features/admin/clientSlice.ts";
import { toast } from "react-toastify";
import { getClientTypesAll } from "../../../features/admin/clientTypeSlice.ts";
import { DatePicker } from 'antd';
import moment from "moment";
import { getGovernorateAll } from "../../../features/admin/governorateSlice.ts";
import { getDelegationsByGovernorate } from "../../../features/admin/delegationSlice.ts";
import { getEstablishmentAll } from "../../../features/admin/establishmentSlice.ts";
import { getSchoolDegreeAll } from "../../../features/admin/schoolDegreeSlice.ts";
import { useSelector } from "react-redux";


function ManageClients() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const dispatch = useDispatch<any>();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);

   
    const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<number | null>(null);
    const [filteredDelegations, setFilteredDelegations] = useState<any[]>([]);
    const [isPasswordEditable, setPasswordEditable] = useState(false);
    const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);

    const [editingClient, setEditingClient] = useState<any>(null);
    const [selectedClientType, setSelectedClientType] = useState<any | null>(null);

    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);
    
    const governorates = useSelector((state: any) => state.governorate.items.data);
    const establishments = useSelector((state: any) => state.establishment.items.data);
    const schoolDegrees = useSelector((state: any) => state.schoolDegree.items.data);
    const clientTypes = useSelector((state: any) => state.clientType.items.data);

{   /*|--------------------------------------------------------------------------
    | FETCH ALL CLIENTS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */}
    const handleGetClients = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getClients({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                originalPromiseResult.data.forEach((item:any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

      const fetchStoreData = async () => {
            try {
                setLoading(true);
    
                const promises = [];
    
                if(!governorates?.length){
                    promises.push(dispatch(getGovernorateAll()).unwrap());
                }
                if (!clientTypes?.length) {
                    promises.push(dispatch(getClientTypesAll()).unwrap());
                }
                if (!establishments?.length) {
                    promises.push(dispatch(getEstablishmentAll()).unwrap());
                }
                if (!schoolDegrees?.length) {
                    promises.push(dispatch(getSchoolDegreeAll()).unwrap());
                }
    
                await Promise.all(promises);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        }
    
        useEffect(() => {
            setLoading(true);
            fetchStoreData();
        }, []);

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_users.labels.id")}`,
            dataIndex: "id",
            search: false,
            width: 60,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_users.labels.lastname")}`,
            dataIndex: "lastname",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => (
                <span style={{ fontWeight: 500 }}>{data.lastname}</span>
            ),
        },
        {
            title: `${t("manage_users.labels.firstname")}`,
            dataIndex: "firstname",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_users.labels.clientType")}`,
            dataIndex: "id_client_type",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => (
                <Tag color={data?.client_type?.color}>
                    {data?.client_type?.[`nom_${currentLang}`]}
                </Tag>
            ),
        },
        {
            title: `${t("manage_users.labels.phone")}`,
            dataIndex: "phone",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.phone,
        },
        {
            title: `${t("manage_establishment.labels.governorate")}`,
            dataIndex: "governorate",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.governorate?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    allowClear
                    placeholder={t("manage_establishment.filters.governorate")}
                    options={governorates?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    onChange={handleGovernorateChange}
                />
            ),
        },
        {
            title: `${t("manage_establishment.labels.delegation")}`,
            dataIndex: "delegation",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.delegation?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    allowClear
                    loading={isDelegationsLoading}
                    placeholder={t("manage_establishment.filters.delegation")}
                    options={filteredDelegations?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    disabled={!selectedFilterGovernorate}
                    className="w-full"
                    notFoundContent={
                        isDelegationsLoading ? (
                            <div className="flex items-center justify-center py-2">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                            </div>
                        ) : (
                            <div className="text-center py-2 text-gray-500">
                                {!selectedFilterGovernorate 
                                    ? t("manage_establishment.selectGovernorate") 
                                    : t("common.noData")}
                            </div>
                        )
                    }
                />
            ),
        },
        {
            title: `${t("manage_users.labels.cin")}`,
            dataIndex: "identity_number",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.cin,
        },
        {
            title: `${t("manage_users.labels.email")}`,
            dataIndex: "email",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.email,
        },
        {
            title: `${t("manage_users.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_users.labels.actions")}`,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_users.client.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_users.yes")}
                        cancelText={t("manage_users.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.dashboard")}</Link>,
        },
        {
            title: t("manage_users.client.title_client"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
      const handleAdd = () => {
            setEditingClient(null);
            setViewMode(false);
            form.resetFields();
            setModalVisible(true);
        };

        const handleEdit = async (record: any) => {
            setEditingClient(record);
            
            // First set the governorate and fetch its delegations if it exists
            if (record.governorate) {
                await handleGovernorateChange(record.governorate.id);
            }
            
            // Then set all form values with null checks
            form.setFieldsValue({
                ...record,
                id_client_type: record.client_type?.id || null,
                id_establishment: record?.establishment?.id || null,
                dob: record.dob ? moment(record.dob) : null,
                id_governorate: record.governorate?.id || null,
                id_delegation: record.delegation?.id || null,
                id_degree: record.degree?.id || null,
            });
    
            setSelectedClientType(record.client_type);
            setViewMode(false);
            setModalVisible(true);
        };
       
        const handleView = async (record: any) => {
            setEditingClient(record);
            
            // First set the governorate and fetch its delegations if it exists
            if (record.governorate) {
                await handleGovernorateChange(record.governorate.id);
            }

            // Then set all form values with null checks
            form.setFieldsValue({
                ...record,
                dob: record.dob ? moment(record.dob) : null,
                id_governorate: record.governorate?.id || null,
                id_delegation: record.delegation?.id || null,
                id_client_type: record.client_type?.id || null,
                id_establishment: record?.establishment?.id || null,
            });
    
            setSelectedClientType(record.client_type);
            setViewMode(true);
            setModalVisible(true);
        };

        const handleGovernorateChange: any = async (governorateId: number | null) => {
            form.setFieldsValue({ id_delegation: null });
            setSelectedFilterGovernorate(governorateId);
            setIsDelegationsLoading(true);
            setFilteredDelegations([]);
            if (!governorateId) {
                setFilteredDelegations([]);
                setIsDelegationsLoading(false);
                return;
            }
            try {
                const response:any = await dispatch(getDelegationsByGovernorate(governorateId)).unwrap();
                setFilteredDelegations(response.data || []);
            } catch (error) {
                console.error('Error fetching delegations:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setIsDelegationsLoading(false);
            }
        };
    

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE CLIENT
    |-------------------------------------------------------------------------- */
     
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        if (!values.password) {
            values.password = 'client';
        }
        const payload = editingClient ? { id: editingClient.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingClient) {
                await dispatch(updateClient(payload)).unwrap();
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
                handleReset()
            } else {
                await dispatch(storeClient(values)).unwrap();
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
            }
            actionRef.current?.reload();
            handleReset()
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    
    const confirmSubmit = (values: any) => {
        Modal.confirm({
            title: t("manage_users.admin.confirmAction"),
            content: editingClient ? t("manage_users.admin.confirmUpdate") : t("manage_users.admin.confirmAdd"),
            okText: t("manage_users.admin.yes"),
            cancelText: t("manage_users.admin.no"),
            onOk: () => handleFormSubmit(values),
            centered:true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE CLIENT
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteClient(id))
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        setViewMode(false)
        form.resetFields();
    }


    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_users.client.title_client")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetClients(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_users.client.add_client")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_users.client.details_client")
                        : editingClient
                            ? t("manage_users.client.edit_client")
                            : t("manage_users.client.add_client")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_users.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.lastname")}
                                name="lastname"
                                rules={[{ required: true, message: t("manage_users.errors.lastnameRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_users.client.placeholders.lastname")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.firstname")}
                                name="firstname"
                                rules={[{ required: true, message: t("manage_users.errors.firstnameRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_users.client.placeholders.firstname")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_users.labels.phone")}
                                name="phone"
                                rules={[
                                    { required: true, message: t("manage_users.errors.phoneRequired") },
                                    {
                                        pattern: /^[0-9]{8}$/,
                                        message: t("manage_users.errors.phoneInvalid"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_users.client.placeholders.phone")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>

                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_users.labels.dob")}
                                name="dob"
                                rules={[
                                    { required: true, message: t("manage_users.errors.dobRequired") }
                                ]}
                            >
                                <DatePicker
                                    className="w-full"
                                    format="YYYY-MM-DD"
                                    disabled={viewMode}
                                    placeholder={t("manage_users.placeholders.dob")}
                                    onChange={(date) => {
                                        if (moment.isMoment(date)) {
                                            form.setFieldsValue({ dob: date.format("YYYY-MM-DD") });
                                        }
                                    }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.clientType")}
                                name="id_client_type"
                                rules={[{ required: true, message: t("manage_users.errors.clientTypeRequired") }]}
                            >
                                <Select
                                    onChange={(value) => {
                                        const selectedType:any = clientTypes?.find((el: any) => el.id === value);
                                        console.log(selectedType);   
                                        setSelectedClientType(selectedType);
                                    }}
                                    disabled={viewMode}
                                    placeholder={t("manage_users.client.placeholders.clientType")}
                                >
                                    {clientTypes?.map((el: any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={
                                    selectedClientType && selectedClientType?.hasCIN
                                        ? t("manage_users.labels.cin")
                                        : t("manage_users.labels.matricule")
                                }
                                name="identity_number"
                                rules={[
                                    { required: true, message: t("manage_users.errors.cinRequired") },
                                    {
                                        pattern: /^[A-Z0-9]+$/,
                                        message:
                                            t("manage_delegations.errors.cinInvalid")
                                    },
                                ]}
                            >
                                <Input placeholder={
                                    selectedClientType && selectedClientType?.hasCIN
                                        ? t("manage_users.client.placeholders.cin")
                                        : t("manage_users.client.placeholders.matricule")
                                }disabled={viewMode} />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.address")}
                                name="address"
                                rules={[{ required: true, message: t("manage_users.errors.addressRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_users.client.placeholders.address")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>

                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.email")}
                                name="email"
                                rules={[
                                    { required: true, type: "email", message: t("manage_users.errors.emailRequired") },
                                ]}
                            >
                                <Input placeholder={t("manage_users.client.placeholders.email")} disabled={viewMode} />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_governorate"
                                label={t("manage_users.labels.governorate")}
                                rules={[{ required: true, message: t("manage_users.errors.governorateRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_users.placeholders.governorate")}
                                    onChange={handleGovernorateChange}
                                    options={governorates?.map((gov: any) => ({
                                        label: gov[`nom_${currentLang}`],
                                        value: gov.id,
                                    }))}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_delegation"
                                label={t("manage_users.labels.delegation")}
                                rules={[{ required: true, message: t("manage_users.errors.delegationRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_users.placeholders.delegation")}
                                    options={filteredDelegations.map((del: any) => ({
                                        label: del[`nom_${currentLang}`],
                                        value: del.id,
                                    }))}
                                    notFoundContent={
                                        isDelegationsLoading ? (
                                            <div className="flex items-center justify-center py-2">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                            </div>
                                        ) : (
                                            <div className="text-center py-2 text-gray-500">
                                                {!selectedFilterGovernorate
                                                    ? t("manage_users.selectGovernorate")
                                                    : t("common.noData")}
                                            </div>
                                        )
                                    }
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    {
                        selectedClientType && selectedClientType?.is_student && (
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item
                                        label={t("manage_users.labels.establishment")}
                                        name="id_establishment"
                                        rules={[{ required: true, message: t("manage_users.errors.establishmentRequired") }]}
                                    >
                                        <Select
                                            disabled={viewMode}
                                            placeholder={t("manage_users.client.placeholders.establishment")}
                                        >
                                            {establishments?.map((el:any) => (
                                                <Select.Option key={el.id} value={el.id}>{el[`nom_${currentLang}`]}</Select.Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        label={t("manage_users.labels.schoolDegree")}
                                        name="id_degree"
                                        rules={[{ required: true, message: t("manage_users.errors.schoolDegreeRequired") }]}
                                    >
                                        <Select
                                            disabled={viewMode}
                                            placeholder={t("manage_users.client.placeholders.schoolDegree")}
                                        >
                                            {schoolDegrees?.map((el:any) => (
                                                <Select.Option key={el.id} value={el.id}>{el[`nom_${currentLang}`]}</Select.Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Col>
                            </Row>
                        )
                    }

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.password")}
                                name="password"
                            >
                                <div className="flex items-center gap-4">
                                    <div>
                                        <Input
                                            placeholder={t("manage_users.admin.placeholders.password")}
                                            disabled={!isPasswordEditable}
                                            value={form.getFieldValue("password")}
                                            onChange={(e) => form.setFieldsValue({ password: e.target.value })} // Manually bind value
                                        />
                                    </div>
                                    <div>
                                        <Switch
                                            disabled={viewMode}
                                            checked={isPasswordEditable}
                                            onChange={(checked) => setPasswordEditable(checked)}
                                        />
                                    </div>
                                </div>
                                {!viewMode && (
                                    <small style={{color:"var(--secondary-color)"}}>{t("manage_users.admin.defaultPasswordMessage")}</small>
                                )}
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageClients;
