import {Form, Input} from "antd";
import {useEffect} from "react";
import {useWatch} from "antd/es/form/Form";
import {useTranslation} from "react-i18next";

const RouteNameField:any = ({ form, name, stationsData }:any) => {
    const {i18n} = useTranslation();
    const currentLang = i18n.language;
    const prevStationId = useWatch(['stations', name - 1, 'id_station'], form);
    const currentStationId = useWatch(['stations', name, 'id_station'], form);
    const {t} = useTranslation();
    useEffect(() => {
        const prevStation = stationsData.find((s:any) => s.id === prevStationId);
        const currentStation = stationsData.find((s:any) => s.id === currentStationId);

        if (prevStation && currentStation) {
            form.setFieldsValue({
                stations: {
                    [name]: {
                        route: {
                            name: `${prevStation[`nom_${currentLang}`]} - ${currentStation[`nom_${currentLang}`]}`
                        }
                    }
                }
            });
        }
    }, [prevStationId, currentStationId, form, name, stationsData]);

    return (
        <Form.Item
            name={[name, 'route', 'name']}
            label={t("manage_routes.labels.name")}
        >
            <Input disabled={true} className="w-full" placeholder={"en cours..."} />
        </Form.Item>
    );
};

export default RouteNameField