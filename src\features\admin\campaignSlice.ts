import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/campaigns";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null,
    salesPeriods: []
};

export const getCampaignsAll: any = createAsyncThunk(
    "getCampaignsAll",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-all`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getCampaigns: any = createAsyncThunk(
    "getCampaigns",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar, status } = data.params;
            console.log(status);
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (status !== undefined) {
                searchParams.push(`status:${status}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeCampaign: any = createAsyncThunk(
    "storeCampaign",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateCampaign: any = createAsyncThunk(
    "updateCampaign",
    async (data: any, thunkAPI: any) => {
        try {
            const { id, ...payload } = data;
            const url: string = `${URL}/${id}`;
            const resp: any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteCampaign: any = createAsyncThunk(
    "deleteCampaign",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


export const getCampaignSalesPeriods: any = createAsyncThunk(
    "getCampaignSalesPeriods",
    async (campaignId: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${campaignId}/sales-periods`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const campaignSlice = createSlice({
    name: 'campaign',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getCampaignsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCampaignsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getCampaignsAll.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getCampaigns.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCampaigns.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getCampaigns.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(storeCampaign.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeCampaign.fulfilled, (state: any, action) => {
                state.loading = false;
                state.items.push(action.payload);
            })
            .addCase(storeCampaign.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(updateCampaign.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateCampaign.fulfilled, (state: any, action) => {
                state.loading = false;
                const index = state.items.findIndex((item: any) => item.id === action.payload.id);
                if (index !== -1) {
                    state.items[index] = action.payload;
                }
            })
            .addCase(updateCampaign.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(deleteCampaign.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteCampaign.fulfilled, (state: any, action) => {
                state.loading = false;
                state.items = state.items.filter((item: any) => item.id !== action.payload);
            })
            .addCase(deleteCampaign.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getCampaignSalesPeriods.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCampaignSalesPeriods.fulfilled, (state, action) => {
                state.loading = false;
                state.salesPeriods = action.payload;
            })
            .addCase(getCampaignSalesPeriods.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = campaignSlice.actions;
export default campaignSlice.reducer;