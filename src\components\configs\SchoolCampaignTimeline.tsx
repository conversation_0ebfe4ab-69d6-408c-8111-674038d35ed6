import {Timeline, Tag, Empty, Typography} from "antd";
import moment from "moment";
import {useTranslation} from "react-i18next";

const SchoolCampaignTimeline:any = ({ salesPeriods }: any) => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;
    
    return (
        <>
            {t("manage_campaigns.salesPeriods")}

                {
                    salesPeriods && salesPeriods.length > 0 ? (
                        <Timeline className="pt-8 my-2 text-sm" mode={"alternate"}>
                            {
                                salesPeriods.map((period: any) => {

                                    const isOutDateClient = moment(period.date_end_client).isBefore(moment(), 'day');
                                    const isOutDateValidity = moment(period.date_end_validity).isBefore(moment(), 'day');
                                    const isOutDateAgent = moment(period.date_end_agent).isBefore(moment(), 'day');
                                    const isOutDate = isOutDateClient || isOutDateValidity || isOutDateAgent;
                                    return (
                                        (
                                            <Timeline.Item key={period.id} color={isOutDate ? "red" : "green"}>
                                                <div className="text-xs mb-2">
                                                    <strong>{period[`nom_${currentLang}`]}</strong>
                                                </div>
                                                <div className={`text-xs mb-2 ${isOutDateClient ? "text-red-500" : "text-green-500"}`}>
                                                    {moment(period.date_start_client).format("DD MMM YYYY")} - {moment(period.date_end_client).format("DD MMM YYYY")}
                                                </div>
                                                <div className={`text-xs mb-2 ${isOutDateAgent ? "text-red-500" : "text-green-500"}`}>
                                                    {moment(period.date_start_agent).format("DD MMM YYYY")} - {moment(period.date_end_agent).format("DD MMM YYYY")}
                                                </div>
                                                <div className={`text-xs mb-2 ${isOutDateValidity ? "text-red-500" : "text-green-500"}`}>
                                                    {moment(period.date_start_validity).format("DD MMM YYYY")} - {moment(period.date_end_validity).format("DD MMM YYYY")}
                                                </div>
                                            </Timeline.Item>
                                        )
                                    )
                                })
                            }
                        </Timeline>
                        ) : (
                        <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description={
                                <Typography.Text type="secondary">
                                    {t("manage_campaigns.noSalesPeriods")}
                                </Typography.Text>
                            }
                            className="py-8"
                        />
                    )

                }
        </>
    );
};

export default SchoolCampaignTimeline;
