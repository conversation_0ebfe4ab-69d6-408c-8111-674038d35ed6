/*|--------------------------------------------------------------------------
|  export components
|-------------------------------------------------------------------------- */

import Loading from "./Loading";
import Footer from "./layouts/AuthFooter.tsx";
import LanguageSelector  from "./LanguageSelector";
import LandingSide  from "./LandingSide";
import AuthHeader from "./layouts/AuthHeader";
import AuthSidebar from "./layouts/AuthSidebar";
import PermissionsManager from "./configs/PermissionsManager";
import RolesManager from "./configs/RolesManager";
import SchoolCampaignTimeline from "./configs/SchoolCampaignTimeline";
import LineStationsManager from "./configs/LineStationsManager";
import RouteNameField from "./configs/RouteNameField";
import PurchaseOrdersManager from "./configs/PurchaseOrdersManager";
import CampaignPeriodsManager from "./configs/CampaignPeriodsManager.tsx";
import LineDisplay from "./configs/LineDisplay.tsx";
import SubsOptions from "./subs/SubsOptions.tsx";
import SubsCardPrintModal from "./subs/SubsCardPrintModal.tsx";
import SellsPeriodClosedInfo from "./subs/SellsPeriodClosedInfo";
import AgencySalesPointsManager from "./configs/AgencySalesPointsManager.tsx";

// Social Affairs Components
import SocialAffairsList from "./Governorate/SocialAffairsList.tsx";
import SocialAffairForm from "./Governorate/SocialAffairForm.tsx";
import SocialAffairDetails from "./Governorate/SocialAffairDetails.tsx";
import SocialAffairImport from "./Governorate/SocialAffairImport.tsx";

export {
    Loading,
    Footer,
    LanguageSelector,
    LandingSide,
    AuthHeader,
    AuthSidebar,
    PermissionsManager,
    RolesManager,
    SchoolCampaignTimeline,
    LineStationsManager,
    RouteNameField,
    PurchaseOrdersManager,
    CampaignPeriodsManager,
    LineDisplay,
    SubsOptions,
    SubsCardPrintModal,
    SellsPeriodClosedInfo,
    AgencySalesPointsManager,

    // Social Affairs Components
    SocialAffairsList,
    SocialAffairForm,
    SocialAffairDetails,
    SocialAffairImport
};