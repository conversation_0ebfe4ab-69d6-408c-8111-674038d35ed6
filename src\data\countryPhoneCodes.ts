/*const countryOptions = [
    { value: '+1', label: 'United States', flag: 'https://flagcdn.com/w40/us.png' },
    { value: '+7', label: 'Russia', flag: 'https://flagcdn.com/w40/ru.png' },
    { value: '+20', label: 'Egypt', flag: 'https://flagcdn.com/w40/eg.png' },
    { value: '+27', label: 'South Africa', flag: 'https://flagcdn.com/w40/za.png' },
    { value: '+30', label: 'Greece', flag: 'https://flagcdn.com/w40/gr.png' },
    { value: '+31', label: 'Netherlands', flag: 'https://flagcdn.com/w40/nl.png' },
    { value: '+32', label: 'Belgium', flag: 'https://flagcdn.com/w40/be.png' },
    { value: '+33', label: 'France', flag: 'https://flagcdn.com/w40/fr.png' },
    { value: '+34', label: 'Spain', flag: 'https://flagcdn.com/w40/es.png' },
    { value: '+39', label: 'Italy', flag: 'https://flagcdn.com/w40/it.png' },
    { value: '+44', label: 'United Kingdom', flag: 'https://flagcdn.com/w40/gb.png' },
    { value: '+49', label: 'Germany', flag: 'https://flagcdn.com/w40/de.png' },
    { value: '+52', label: 'Mexico', flag: 'https://flagcdn.com/w40/mx.png' },
    { value: '+55', label: 'Brazil', flag: 'https://flagcdn.com/w40/br.png' },
    { value: '+61', label: 'Australia', flag: 'https://flagcdn.com/w40/au.png' },
    { value: '+62', label: 'Indonesia', flag: 'https://flagcdn.com/w40/id.png' },
    { value: '+63', label: 'Philippines', flag: 'https://flagcdn.com/w40/ph.png' },
    { value: '+64', label: 'New Zealand', flag: 'https://flagcdn.com/w40/nz.png' },
    { value: '+65', label: 'Singapore', flag: 'https://flagcdn.com/w40/sg.png' },
    { value: '+81', label: 'Japan', flag: 'https://flagcdn.com/w40/jp.png' },
    { value: '+82', label: 'South Korea', flag: 'https://flagcdn.com/w40/kr.png' },
    { value: '+86', label: 'China', flag: 'https://flagcdn.com/w40/cn.png' },
    { value: '+90', label: 'Turkey', flag: 'https://flagcdn.com/w40/tr.png' },
    { value: '+91', label: 'India', flag: 'https://flagcdn.com/w40/in.png' },
    { value: '+92', label: 'Pakistan', flag: 'https://flagcdn.com/w40/pk.png' },
    { value: '+93', label: 'Afghanistan', flag: 'https://flagcdn.com/w40/af.png' },
    { value: '+94', label: 'Sri Lanka', flag: 'https://flagcdn.com/w40/lk.png' },
    { value: '+95', label: 'Myanmar', flag: 'https://flagcdn.com/w40/mm.png' },
    { value: '+98', label: 'Iran', flag: 'https://flagcdn.com/w40/ir.png' },
    { value: '+212', label: 'Morocco', flag: 'https://flagcdn.com/w40/ma.png' },
    { value: '+213', label: 'Algeria', flag: 'https://flagcdn.com/w40/dz.png' },
    { value: '+216', label: 'Tunisia', flag: 'https://flagcdn.com/w40/tn.png' },
    { value: '+218', label: 'Libya', flag: 'https://flagcdn.com/w40/ly.png' },
    { value: '+220', label: 'Gambia', flag: 'https://flagcdn.com/w40/gm.png' },
    { value: '+221', label: 'Senegal', flag: 'https://flagcdn.com/w40/sn.png' },
    { value: '+222', label: 'Mauritania', flag: 'https://flagcdn.com/w40/mr.png' },
    { value: '+223', label: 'Mali', flag: 'https://flagcdn.com/w40/ml.png' },
    { value: '+224', label: 'Guinea', flag: 'https://flagcdn.com/w40/gn.png' },
    { value: '+225', label: 'Ivory Coast', flag: 'https://flagcdn.com/w40/ci.png' },
    { value: '+226', label: 'Burkina Faso', flag: 'https://flagcdn.com/w40/bf.png' },
    { value: '+227', label: 'Niger', flag: 'https://flagcdn.com/w40/ne.png' },
    { value: '+228', label: 'Togo', flag: 'https://flagcdn.com/w40/tg.png' },
    { value: '+229', label: 'Benin', flag: 'https://flagcdn.com/w40/bj.png' },
    { value: '+230', label: 'Mauritius', flag: 'https://flagcdn.com/w40/mu.png' },
    { value: '+231', label: 'Liberia', flag: 'https://flagcdn.com/w40/lr.png' },
    { value: '+234', label: 'Nigeria', flag: 'https://flagcdn.com/w40/ng.png' },
    { value: '+250', label: 'Rwanda', flag: 'https://flagcdn.com/w40/rw.png' },
    { value: '+251', label: 'Ethiopia', flag: 'https://flagcdn.com/w40/et.png' },
    { value: '+255', label: 'Tanzania', flag: 'https://flagcdn.com/w40/tz.png' },
    { value: '+256', label: 'Uganda', flag: 'https://flagcdn.com/w40/ug.png' },
    { value: '+260', label: 'Zambia', flag: 'https://flagcdn.com/w40/zm.png' },
    { value: '+263', label: 'Zimbabwe', flag: 'https://flagcdn.com/w40/zw.png' },
];*/
import {assets} from "../assets/assets.ts";

const countryOptions = [
    { value: '+216', label: 'Tunisia', flag: assets.tn },
]
export default countryOptions;
