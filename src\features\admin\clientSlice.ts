import { createAsyncThunk } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/clients';

export const getClientsAll: any = createAsyncThunk(
    "getClientsAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getClients: any = createAsyncThunk(
    "getClients",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { nom, prenom, email, telephone, identity_number,id_governorate,id_delegation,id_establishment,id_client_type } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom) {
                searchParams.push(`nom:${nom}`);
            }
            if (prenom) {
                searchParams.push(`prenom:${prenom}`);
            }
            if (email) {
                searchParams.push(`email:${email}`);
            }
            if (telephone) {
                searchParams.push(`telephone:${telephone}`);
            }
            if (identity_number) {
                searchParams.push(`identity_number:${identity_number}`);
            }
            if (id_governorate) {
                searchParams.push(`id_governorate:${id_governorate}`);
            }
            if (id_delegation) {
                searchParams.push(`id_delegation:${id_delegation}`);
            }
            if (id_establishment) {
                searchParams.push(`id_establishment:${id_establishment}`);
            }
            if (id_client_type) {
                searchParams.push(`id_client_type:${id_client_type}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }


            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeClient: any = createAsyncThunk(
    "storeClient",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            console.log(data);

            const resp:any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateClient: any = createAsyncThunk(
    "updateClient",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteClient: any = createAsyncThunk(
    "deleteClient",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const searchStudentByMatriculeAndDob: any = createAsyncThunk(
    "searchStudentByMatriculeAndDob",
    async (data: { identity_number: string, dob: string }, thunkAPI: any) => {
        console.log(data);

      try {
        const url: string = `${URL}/search-student`;
        const resp: any = await api.post(url, {
          identity_number: data.identity_number,
          dob: data.dob
        });
        console.log(resp.data);
        return resp.data;
      } catch (error: any) {
        if (error.response) {
          const { status, data } = error.response;
          if (status === 403) {
            window.location.href = '/unauthorized';
          }
          return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
        } else {
          return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
      }
    }
  );

export const getClientsHasCIN: any = createAsyncThunk(
    "getClientsHasCIN",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-has-cin`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getImpersonalClients: any = createAsyncThunk(
    "getImpersonalClients",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-impersonal`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getConventionalClients: any = createAsyncThunk(
    "getConventionalClients",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-conventional`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

