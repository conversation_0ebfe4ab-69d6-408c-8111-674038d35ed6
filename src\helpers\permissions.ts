export const hasPermission = (permission: string): boolean => {
    try {
      const permissionsString = localStorage.getItem('PERMISSIONS');
      if (!permissionsString) return false;
  
      const permissions: string[] = JSON.parse(permissionsString);
      return permissions.includes(permission);
    } catch (error) {
      console.error('Error reading permissions from localStorage:', error);
      return false;
    }
  };