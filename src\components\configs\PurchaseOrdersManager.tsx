import {useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    message,
    DatePicker, Tag, Select, Badge, Space, Card, Col, Row, Spin, Empty, Typography,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import {useTranslation} from "react-i18next";
import moment from "moment";
import {ShoppingCart} from "lucide-react";
import {
    getPurchaseOrders,
    updatePurchaseOrder,
    storePurchaseOrder,
    deletePurchaseOrder
} from "../../features/admin/purchaseOrderSlice.ts";
import {toast, ToastContainer} from "react-toastify";


interface PurchaseOrdersManagerProps {
    record: number;
    onUpdate?: () => void;
}

const PurchaseOrdersManager: React.FC<PurchaseOrdersManagerProps> = ({ 
    record,
    onUpdate 
}) => {
    const {t} = useTranslation();
    const dispatch = useDispatch<any>();
    const [form] = Form.useForm();

    const [submitLoading, setSubmitLoading] = useState(false);
    const [tableLoading, setTableLoading] = useState(false);
    const [deleteLoading, setDeleteLoading] = useState<number | null>(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingPurchaseOrder, setEditingPurchaseOrder] = useState<any>(null);
    const [purchaseOrders, setPurchaseOrders] = useState<any[]>([]);


    /*|--------------------------------------------------------------------------
    | FETCH PURCHASE ORDERS FOR SPECIFIC GOVERNORATE
    |-------------------------------------------------------------------------- */
    const handleGetPurchaseOrders = () => {
        setTableLoading(true);
        return dispatch(getPurchaseOrders({
            governorateId: record,
        }))
        .unwrap()
        .then((originalPromiseResult: any) => {
            const formattedData = originalPromiseResult.data.map((item: any) => ({
                ...item,
                key: item.id
            }));
            setPurchaseOrders(formattedData);
            return formattedData;
        })
        .catch((error: any) => {
            console.error('Error fetching purchase orders:', error);
            message.error('Failed to fetch purchase orders');
        })
        .finally(() => {
            setTableLoading(false);
        });
    };

    useEffect(() => {
        handleGetPurchaseOrders();
    }, [record]);

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const cardStyle = {
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        borderRadius: "8px",
        transition: "all 0.3s ease",
        cursor: "pointer",
        marginBottom: "16px",
        ":hover": {
            transform: "translateY(-2px)",
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        },
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView:any = (record: any) => {
        setEditingPurchaseOrder(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit:any = (record: any) => {
        setEditingPurchaseOrder(record);
        form.setFieldsValue(record);
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd:any = () => {
        setEditingPurchaseOrder(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };


    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE PurchaseOrder
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setSubmitLoading(true);
        const formattedValues = {
            ...values,
            id_governorate: record,
            date: moment(values.date).format('YYYY-MM-DD')
        };

        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        try {
            if (editingPurchaseOrder && editingPurchaseOrder.id) {
                await dispatch(updatePurchaseOrder({
                    id: editingPurchaseOrder.id,
                    ...formattedValues,
                    id_governorate: record
                })).unwrap();
            } else {
                await dispatch(storePurchaseOrder({
                    ...formattedValues,
                    id_governorate: record
                })).unwrap();
            }
            
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });

            handleReset();
            await handleGetPurchaseOrders();
            if (onUpdate) {
                onUpdate();
            }
        } catch (error: any) {
            if (error.errors) {
                const fieldErrors = Object.keys(error.errors).map((field) => ({
                    name: field === 'ref' ? 'ref' : field,
                    errors: [error.errors[field][0]],
                }));
                form.setFields(fieldErrors);
            }
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setSubmitLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_governorates.po.confirmAction"),
            content: editingPurchaseOrder
                ? t("manage_governorates.po.confirmUpdate")
                : t("manage_governorates.po.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE PURCHASE ORDER
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        setDeleteLoading(id);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        
        try {
            await dispatch(deletePurchaseOrder(id)).unwrap();
            
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });

            await handleGetPurchaseOrders();
            if (onUpdate) {
                onUpdate();
            }
        } catch (error: any) {
            toast.update(toastId, {
                render: error.message || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setDeleteLoading(null);
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setEditingPurchaseOrder(null);
        form.resetFields();
    };

    return (
        <div className="expand-custom p-6 bg-white rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-800">
                    {t("manage_governorates.po.title")}
                </h2>
                {
                    purchaseOrders && purchaseOrders.length > 0 && (
                        <Button
                            type="dashed"
                            onClick={() => handleAdd(record)}
                            className="w-full sm:w-auto"
                        >
                            {t("manage_governorates.addPO")}
                        </Button>
                    )
                }
            </div>

            <Spin spinning={tableLoading}>
                <Row gutter={[16, 16]} style={{ marginTop: "10px", marginBottom:"10px" }}>
                    {
                        purchaseOrders && purchaseOrders.length > 0 ?
                            (
                                    purchaseOrders.map((item: any) => (
                                        <Col xs={24} sm={12} lg={8} xl={6} key={item.id}>
                                            <Card
                                                className="!shadow-sm"
                                                title={
                                                    <div className="flex items-center justify-between">
                                                        <Space>
                                                            <span className="font-semibold">{item.ref}</span>
                                                        </Space>
                                                        <Tag color={item.status ? "green" : "red"}>
                                                            {item.status ? t("common.active") : t("common.rupture")}
                                                        </Tag>
                                                    </div>
                                                }
                                                style={cardStyle}
                                                actions={[
                                                    <EyeOutlined key="view" onClick={() => handleView(item)} />,
                                                    <EditOutlined key="edit" onClick={() => handleEdit(item)} />,
                                                    <Popconfirm
                                                        title={t("manage_governorates.po.confirmAction")}
                                                        description={t("manage_governorates.po.confirmDelete")}
                                                        onConfirm={() => handleDelete(item.id)}
                                                        okText={t("common.yes")}
                                                        cancelText={t("common.no")}
                                                        placement="topRight"
                                                        open={deleteLoading === item.id ? false : undefined}
                                                    >
                                                        <DeleteOutlined
                                                            key="delete"
                                                            style={{ color: "#ff4d4f" }}

                                                        />
                                                    </Popconfirm>,
                                                ]}
                                            >
                                                <div className="po-card-content">
                                                    <div className="flex justify-between mb-2">
                                                        <span className="text-gray-500">{t("manage_governorates.po.labels.date")}</span>
                                                        <span className="font-medium">
                                                        {moment(item.date).format("DD MMM YYYY")}
                                                    </span>
                                                    </div>

                                                    <div className="flex justify-between mb-2">
                                                        <span className="text-gray-500">{t("manage_governorates.po.labels.initialAmount")}</span>
                                                        <Badge
                                                            count={item.initial_amount + " TND"}
                                                            color="default"
                                                        />
                                                    </div>

                                                    <div className="flex justify-between mb-2">
                                                        <span className="text-gray-500">{t("manage_governorates.po.labels.currentAmount")}</span>
                                                        <Badge
                                                            count={item.current_amount + " TND"}
                                                            style={{
                                                                backgroundColor: item.status ? "#f6ffed" : "#fff1f0",
                                                                color: item.status ? "#389e0d" : "#cf1322",
                                                            }}
                                                        />
                                                    </div>

                                                    <div className="flex justify-between">
                                                        <span className="text-gray-500">{t("manage_governorates.po.labels.status")}</span>
                                                        <span className={`font-medium ${item.status ? "text-green-600" : "text-red-600"}`}>
                                                            {item.status ? t("common.active") : t("common.inactive")}
                                                        </span>
                                                    </div>
                                                </div>
                                            </Card>
                                        </Col>
                                    ))

                        ): (
                               <div className="flex justify-center items-center w-full">
                                 <Empty
                                    description={
                                        <Typography.Text type="secondary">
                                            {t("manage_governorates.noPO")}
                                        </Typography.Text>
                                    }
                                    className="py-8"
                                >
                                    <Button type="dashed" onClick={() => handleAdd(record)}>
                                        {t("manage_governorates.addPO")}
                                    </Button>
                                </Empty>
                               </div>
                         )
                    }
                </Row>
            </Spin>

            <Modal
                width={900}
                title={
                    <h3 className="text-lg font-semibold">
                        {viewMode
                            ? t("manage_governorates.po.details")
                            : editingPurchaseOrder
                                ? t("manage_governorates.po.edit")
                                : t("manage_governorates.po.add")}
                    </h3>
                }
                open={modalVisible}
                onCancel={handleReset}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_governorates.save")}
                confirmLoading={submitLoading}
                footer={viewMode ? null : undefined}
                className="rounded-lg"
            >
                <Form 
                    className='form-inputs' 
                    form={form} 
                    layout="vertical" 
                    onFinish={confirmSubmit}
                    disabled={submitLoading}
                >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Form.Item
                            label={<span
                                className="font-medium text-gray-700">{t("manage_governorates.po.labels.reference")}</span>}
                            name="ref"
                            rules={[{
                                required: true,
                                message: t("manage_governorates.po.errors.referenceRequired")
                            }]}
                        >
                            <Input
                                placeholder={t("manage_governorates.po.placeholders.reference")}
                                disabled={viewMode}
                            />
                        </Form.Item>

                        <Form.Item
                            label={<span
                                className="font-medium text-gray-700">{t("manage_governorates.po.labels.initialAmount")}</span>}
                            name="initial_amount"
                            rules={[{required: true, message: t("manage_governorates.po.errors.amountRequired")}]}
                        >
                            <Input
                                type="number"
                                placeholder={t("manage_governorates.po.placeholders.amount")}
                                disabled={viewMode}
                                className="rounded-md hover:border-blue-300 focus:border-blue-400 focus:ring-1"
                                addonAfter="TND"
                            />
                        </Form.Item>


                        <Form.Item
                            label={t("manage_governorates.po.labels.status")}
                            name="status"
                            rules={[{ required: true }]}
                        >
                            <Select
                                disabled={viewMode}
                                placeholder={t("manage_governorates.po.placeholders.status")}
                            >
                                <Select.Option value={true}>Actif</Select.Option>
                                <Select.Option value={false}>Epuisé</Select.Option>
                            </Select>
                        </Form.Item>


                        <Form.Item
                            label={<span
                                className="font-medium text-gray-700">{t("manage_governorates.po.labels.date")}</span>}
                            name="date"
                            getValueProps={(value) => ({
                                value: value ? moment(value) : null,
                            })}
                            normalize={(value) => (value ? value.format("YYYY-MM-DD") : null)}
                            rules={[{required: true, message: t("manage_governorates.po.errors.dateRequired")}]}
                        >
                            <DatePicker
                                className="w-full rounded-md hover:border-blue-300 focus:border-blue-400 focus:ring-1"
                                format="YYYY-MM-DD"
                                disabled={viewMode}
                                placeholder={t("manage_governorates.po.placeholders.date")}
                            />
                        </Form.Item>
                    </div>
                </Form>
            </Modal>
        </div>
    );
}

export default PurchaseOrdersManager;
