import { useState, useEffect } from "react";
import {
    Row,
    Col,
    Card,
    Statistic,
    DatePicker,
    Space,
    Breadcrumb,
    Table,
    Tag
} from "antd";
import { Pie, Column, Line } from "@ant-design/plots";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { toast, ToastContainer } from "react-toastify";
import dayjs from "dayjs";
import {
    getAuditStatistics,
    getMostActiveUsers,
    getMostAuditedModels,
    clearError
} from "../../../../features/admin/auditSlice.ts";

const { RangePicker } = DatePicker;

function AuditStatistics() {
    const { t } = useTranslation();
    const dispatch: any = useDispatch();

    const {
        statistics,
        mostActiveUsers,
        mostAuditedModels,
        loading,
        error
    } = useSelector((state: any) => state.audit);

    const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

    useEffect(() => {
        loadStatistics();
    }, [dispatch]);

    useEffect(() => {
        if (error) {
            toast.error(error);
            dispatch(clearError());
        }
    }, [error, dispatch]);

    const loadStatistics = (startDate?: string, endDate?: string) => {
        dispatch(getAuditStatistics({ startDate, endDate }));
        dispatch(getMostActiveUsers({ limit: 10, startDate, endDate }));
        dispatch(getMostAuditedModels({ limit: 10, startDate, endDate }));
    };

    const handleDateRangeChange = (dates: any) => {
        setDateRange(dates);
        if (dates) {
            const [start, end] = dates;
            loadStatistics(
                start.format('YYYY-MM-DD'),
                end.format('YYYY-MM-DD')
            );
        } else {
            loadStatistics();
        }
    };

    // Prepare chart data
    const eventData = statistics?.by_event ? Object.entries(statistics.by_event).map(([key, value]) => ({
        name: t(`audits.events.${key}`),
        value: value as number,
        color: getEventColor(key)
    })) : [];

    const modelData = statistics?.by_model ? Object.entries(statistics.by_model)
        .slice(0, 10)
        .map(([key, value]) => ({
            name: key.replace('App\\Models\\', ''),
            count: value as number
        })) : [];

    const activityData = statistics?.recent_activity ? Object.entries(statistics.recent_activity)
        .slice(-7)
        .map(([date, count]) => ({
            date: dayjs(date).format('DD/MM'),
            count: count as number
        })) : [];

    function getEventColor(event: string) {
        switch (event) {
            case 'created': return '#52c41a';
            case 'updated': return '#faad14';
            case 'deleted': return '#ff4d4f';
            case 'restored': return '#1890ff';
            default: return '#d9d9d9';
        }
    }

    const mostActiveUsersColumns = [
        {
            title: t("audits.labels.user"),
            dataIndex: "user_name",
            key: "user_name",
        },
        {
            title: t("audits.labels.user_type"),
            dataIndex: "type",
            key: "type",
            render: (type: string) => (
                <Tag color="blue">{type?.replace('App\\Models\\', '')}</Tag>
            ),
        },
        {
            title: t("audits.labels.audit_count"),
            dataIndex: "audit_count",
            key: "audit_count",
            sorter: (a: any, b: any) => a.audit_count - b.audit_count,
        },
    ];

    const mostAuditedModelsColumns = [
        {
            title: t("audits.labels.model"),
            dataIndex: "model_name",
            key: "model_name",
        },
        {
            title: t("audits.labels.audit_count"),
            dataIndex: "audit_count",
            key: "audit_count",
            sorter: (a: any, b: any) => a.audit_count - b.audit_count,
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.system")}</Link>,
        },
        {
            title: <Link to="/auth/admin/configs/audits">{t("audits.title")}</Link>,
        },
        {
            title: t("audits.statistics"),
        },
    ];

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <ToastContainer className="z-50" />

            <div className="space-y-6">
                {/* Date Range Filter */}
                <Card>
                    <Space>
                        <span>{t("audits.date_range")}:</span>
                        <RangePicker
                            value={dateRange}
                            onChange={handleDateRangeChange}
                            format="YYYY-MM-DD"
                            allowClear
                        />
                    </Space>
                </Card>

                {/* Statistics Cards */}
                <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12} md={6}>
                        <Card>
                            <Statistic
                                title={t("audits.total_audits")}
                                value={statistics?.total_audits || 0}
                                loading={loading}
                            />
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                        <Card>
                            <Statistic
                                title={t("audits.events.created")}
                                value={statistics?.by_event?.created || 0}
                                valueStyle={{ color: '#52c41a' }}
                                loading={loading}
                            />
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                        <Card>
                            <Statistic
                                title={t("audits.events.updated")}
                                value={statistics?.by_event?.updated || 0}
                                valueStyle={{ color: '#faad14' }}
                                loading={loading}
                            />
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                        <Card>
                            <Statistic
                                title={t("audits.events.deleted")}
                                value={statistics?.by_event?.deleted || 0}
                                valueStyle={{ color: '#ff4d4f' }}
                                loading={loading}
                            />
                        </Card>
                    </Col>
                </Row>

                {/* Charts */}
                <Row gutter={[16, 16]}>
                    {/* Events Distribution Pie Chart */}
                    <Col xs={24} lg={12}>
                        <Card title={t("audits.events_distribution")} loading={loading}>
                            <Pie
                                data={eventData}
                                angleField="value"
                                colorField="name"
                                radius={0.8}
                                label={{
                                    type: 'outer',
                                    content: '{name} {percentage}',
                                }}
                                interactions={[{ type: 'element-active' }]}
                                height={300}
                            />
                        </Card>
                    </Col>

                    {/* Most Audited Models Bar Chart */}
                    <Col xs={24} lg={12}>
                        <Card title={t("audits.most_audited_models")} loading={loading}>
                            <Column
                                data={modelData}
                                xField="name"
                                yField="count"
                                height={300}
                                label={{
                                    position: 'middle',
                                    style: {
                                        fill: '#FFFFFF',
                                        opacity: 0.6,
                                    },
                                }}
                                xAxis={{
                                    label: {
                                        autoRotate: true,
                                    },
                                }}
                                meta={{
                                    name: {
                                        alias: t("audits.labels.model"),
                                    },
                                    count: {
                                        alias: t("audits.labels.audit_count"),
                                    },
                                }}
                            />
                        </Card>
                    </Col>
                </Row>

                {/* Tables */}
                <Row gutter={[16, 16]}>
                    {/* Most Active Users */}
                    <Col xs={24} lg={12}>
                        <Card title={t("audits.most_active_users")} loading={loading}>
                            <Table
                                dataSource={mostActiveUsers}
                                columns={mostActiveUsersColumns}
                                rowKey="user_id"
                                pagination={{ pageSize: 10 }}
                                size="small"
                            />
                        </Card>
                    </Col>

                    {/* Most Audited Models Table */}
                    <Col xs={24} lg={12}>
                        <Card title={t("audits.most_audited_models")} loading={loading}>
                            <Table
                                dataSource={mostAuditedModels}
                                columns={mostAuditedModelsColumns}
                                rowKey="auditable_type"
                                pagination={{ pageSize: 10 }}
                                size="small"
                            />
                        </Card>
                    </Col>
                </Row>
            </div>
        </>
    );
}

export default AuditStatistics;
