import { useEffect, useRef, useState } from "react";
import {
  Button,
  Modal,
  Form,
  Input,
  Popconfirm,
  Breadcrumb,
  Row,
  Col,
  Select,
  Tag,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import AgentsManager from "../../../components/configs/AgentsManager.tsx";
import { useDispatch } from "react-redux";
import {
  deleteSalePoint,
  getSalePoints,
  storeSalePoint,
  updateSalePoint,
} from "../../../features/admin/salePointSlice.ts";
import { getGovernorateAll } from "../../../features/admin/governorateSlice.ts";
import { getAgencyAll } from "../../../features/admin/agencySlice.ts";
import { toast } from "react-toastify";
import { getDelegationsByGovernorate } from "../../../features/admin/delegationSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageSalesPoints() {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;

  const actionRef = useRef<any>();
  const dispatch: any = useDispatch<any>();

  const [agencies, setAgencies] = useState([]);
  const [governorates, setGovernorates] = useState([]);
  const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<
    number | null
  >(null);
  const [filteredDelegations, setFilteredDelegations] = useState<any[]>([]);
  const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editingSalesPoint, setEditingSalesPoint] = useState<any>(null);
  const [form] = Form.useForm();

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState(1);

  /*|--------------------------------------------------------------------------
    |  - FETCH ALL SALESPROINTS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
  const handleGetSalePoints = (params: any, sort: any, filter: any) => {
    return dispatch(
      getSalePoints({
        pageNumber,
        perPage: pageSize,
        params,
        sort,
        filter,
      })
    )
      .unwrap()
      .then((originalPromiseResult: any) => {
        console.log(originalPromiseResult);
        setTotal(originalPromiseResult.meta.total);
        return originalPromiseResult?.data;
      })
      .catch((rejectedValueOrSerializedError: any) => {
        console.log(rejectedValueOrSerializedError);
      });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [govResponse, agencyResponse] = await Promise.all([
          dispatch(getGovernorateAll()).unwrap(),
          dispatch(getAgencyAll()).unwrap(),
        ]);

        setGovernorates(govResponse?.data || []);
        setAgencies(agencyResponse?.data || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error(t("common.errors.unexpected"));
      }
    };

    fetchData();
  }, []);

  /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
  const columns: any = [
    {
      title: `${t("manage_salesPoints.labels.name")}`,
      dataIndex: `nom_${currentLang}`,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => data[`nom_${currentLang}`],
    },
    {
      title: `${t("manage_salesPoints.labels.contact")}`,
      dataIndex: "contact",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => (
        <div className="flex items-center gap-1 font-medium">
          <span className="text-primary-color">+216</span>
          <span className="text-gray-400">|</span>
          <span>{data.contact}</span>
        </div>
      ),
    },
    {
      title: `${t("manage_salesPoints.labels.governorate")}`,
      dataIndex: "id_governorate",
      valueType: "select",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) =>
        record?.governorate?.[`nom_${currentLang}`] || "-",
      renderFormItem: () => (
        <Select
          showSearch
          filterOption={(input, option) =>
            (option?.label as string)
              ?.toLowerCase()
              ?.includes(input.toLowerCase())
          }
          allowClear
          placeholder={t("manage_agencies.filters.governorate")}
          options={governorates?.map((el: any) => ({
            label: el[`nom_${currentLang}`] || el.name || "-",
            value: el.id,
          }))}
          onChange={handleGovernorateChange}
        />
      ),
    },
    {
      title: `${t("manage_salesPoints.labels.delegation")}`,
      dataIndex: "id_delegation",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) =>
        record?.delegation?.[`nom_${currentLang}`] || "-",
      renderFormItem: () => (
        <Select
          showSearch
          filterOption={(input, option) =>
            (option?.label as string)
              ?.toLowerCase()
              ?.includes(input.toLowerCase())
          }
          allowClear
          loading={isDelegationsLoading}
          placeholder={t("manage_agencies.filters.delegation")}
          options={filteredDelegations?.map((el: any) => ({
            label: el[`nom_${currentLang}`] || el.name || "-",
            value: el.id,
          }))}
          disabled={!selectedFilterGovernorate}
          className="w-full"
          notFoundContent={
            isDelegationsLoading ? (
              <div className="flex items-center justify-center py-2">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
              </div>
            ) : (
              <div className="text-center py-2 text-gray-500">
                {!selectedFilterGovernorate
                  ? t("manage_agencies.selectGovernorate")
                  : t("common.noData")}
              </div>
            )
          }
        />
      ),
    },
    {
      title: `${t("manage_salesPoints.labels.address")}`,
      dataIndex: "address",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => data.address,
    },
    {
      title: t("manage_salesPoints.labels.status"),
      dataIndex: "status",
      responsive: ["xs", "sm", "md", "lg"],
      valueType: "select",
      render: (_: any, record: any) => (
        <Tag color={record.status === true ? "success" : "error"}>
          {record.status === true ? t("manage_salesPeriods.open") : t("manage_salesPeriods.closed")}
        </Tag>
      ),
      valueEnum: {
        "1": {
          text: t("manage_salesPeriods.open"),
          status: "Success",
        },
        "0": {
          text: t("manage_salesPeriods.closed") ,
          status: "Error",
        },
      },
    },
    {
      title: `${t("manage_salesPoints.labels.createdAt")}`,
      dataIndex: "created_at",
      valueType: "date",
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: `${t("manage_salesPoints.labels.actions")}`,
      fixed: "right",
      width: 170,
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => (
        <div className="flex gap-1">
          <Button
            className="btn-view"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          {
            hasPermission("edit_sales_points") && (
              <Button
                className="btn-edit"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            )
          }
          {
            hasPermission("delete_sales_points") && (
              <Popconfirm
                title={t("manage_salesPoints.confirmDelete")}
                onConfirm={() => handleDelete(record.id)}
                okText={t("manage_salesPoints.yes")}
                cancelText={t("manage_salesPoints.no")}
              >
                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
              </Popconfirm>
            )
          }
        </div>
      ),
    },
  ];
  const breadcrumbItems = [
    {
      title: (
        <Link className="!bg-white" to="/auth/commercial-dashboard">
          {t("auth_sidebar.categories.sales")}
        </Link>
      ),
    },
    {
      title: t("manage_salesPoints.title"),
    },
  ];

  /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
  const handleView = (record: any) => {
    setEditingSalesPoint(record);
    form.setFieldsValue({
      ...record,
      id_governorate: record.governorate?.id,
      id_delegation: record.delegation?.id,
      id_agency: record.agency?.id,
    });
    fetchDelegation(record.governorate?.id);
    setViewMode(true);
    setModalVisible(true);
  };
  const handleEdit = (record: any) => {
    setEditingSalesPoint(record);
    form.setFieldsValue({
      ...record,
      id_governorate: record.governorate?.id,
      id_delegation: record.delegation?.id,
      id_agency: record.agency?.id,
    });
    fetchDelegation(record.governorate?.id);
    setViewMode(false);
    setModalVisible(true);
  };
  const handleAdd = () => {
    setEditingSalesPoint(null);
    form.resetFields();
    setViewMode(false);
    setModalVisible(true);
  };
  const fetchDelegation=async (governorateId: number | null) => {
    try {
        console.log(governorateId)
      const response: any = await dispatch(
        getDelegationsByGovernorate(governorateId)
      ).unwrap();
      setFilteredDelegations(response.data || []);
    } catch (error) {
      console.error("Error fetching delegations:", error);
      toast.error(t("common.errors.unexpected"));
    } finally {
      setIsDelegationsLoading(false);
    }
  }
  const handleGovernorateChange: any = async (governorateId: number | null) => {
    form.setFieldsValue({ id_delegation: null });
    setSelectedFilterGovernorate(governorateId);
    setIsDelegationsLoading(true);
    setFilteredDelegations([]);
    if (!governorateId) {
      setFilteredDelegations([]);
      setIsDelegationsLoading(false);
      return;
    }
   fetchDelegation(governorateId);
  };

  /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE SALES POINT
    |-------------------------------------------------------------------------- */
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    const payload = editingSalesPoint
      ? { id: editingSalesPoint.id, ...values }
      : values;
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      if (editingSalesPoint) {
        await dispatch(updateSalePoint(payload)).unwrap();
      } else {
        await dispatch(storeSalePoint(values)).unwrap();
      }
      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
      handleReset();
    } catch (error: any) {
      const fieldErrors: any = Object.keys(error.errors || {}).map(
        (field: any) => ({
          name: field,
          errors: [error.errors[field][0]],
        })
      );
      form.setFields(fieldErrors);
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
    }
  };
  const confirmSubmit = (values: any) => {
    const modal = Modal.confirm({
      title: t("manage_salesPoints.confirmAction"),
      content: editingSalesPoint
        ? t("manage_salesPoints.confirmUpdate")
        : t("manage_salesPoints.confirmAdd"),
      okText: t("common.yes"),
      cancelText: t("common.no"),
      onOk: async () => {
        modal.destroy();
        await handleFormSubmit(values);
      },
      centered: true,
    });
  };

  /*|--------------------------------------------------------------------------
    |  - DELETE SALES POINT
    |-------------------------------------------------------------------------- */
  const handleDelete = async (id: number) => {
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      await dispatch(deleteSalePoint(id)).unwrap();
      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
    } catch (error: any) {
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    }
  };

  /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
  const handleReset = () => {
    setEditingSalesPoint(null);
    setModalVisible(false);
    setViewMode(false);
    setSelectedFilterGovernorate(null);
    setFilteredDelegations([]);
    form.resetFields();
  };

  return (
    <>
      <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_salesPoints.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        /*expandable={{
                            expandedRowRender: (record:any) => (
                                <AgentsManager record={record} />
                            ),
                        }}*/
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetSalePoints(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={"id"}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_salesPoints.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

      <Modal
        width={900}
        title={
          viewMode
            ? t("manage_salesPoints.details")
            : editingSalesPoint
            ? t("manage_salesPoints.edit")
            : t("manage_salesPoints.add")
        }
        open={modalVisible}
        onCancel={() => handleReset()}
        onOk={() => (viewMode ? handleReset() : form.submit())}
        okText={viewMode ? null : t("manage_salesPoints.save")}
        footer={viewMode ? null : undefined}
      >
        <Form
          className="form-inputs"
          form={form}
          layout="vertical"
          onFinish={confirmSubmit}
        >
          <Row gutter={16}>
            <Col xs={24} sm={8}>
              <Form.Item
                label={t("manage_salesPoints.labels.name_fr")}
                name="nom_fr"
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.nameFrRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_salesPoints.placeholders.name_fr")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                label={t("manage_salesPoints.labels.name_en")}
                name="nom_en"
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.nameEnRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_salesPoints.placeholders.name_en")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                label={t("manage_salesPoints.labels.name_ar")}
                name="nom_ar"
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.nameArRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_salesPoints.placeholders.name_ar")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t("manage_salesPoints.labels.contact")}
                name="contact"
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.contactRequired"),
                  },
                  {
                    pattern: /^\d{8}$/,
                    message: t("manage_salesPoints.errors.contactInvalid"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_salesPoints.placeholders.contact")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t("manage_salesPoints.labels.address")}
                name="address"
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.addressRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_salesPoints.placeholders.address")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t("manage_salesPoints.labels.agency")}
                name="id_agency"
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.agencyRequired"),
                  },
                ]}
              >
                <Select
                  placeholder={t("manage_salesPoints.placeholders.agency")}
                  disabled={viewMode}
                >
                  {agencies?.map((el: any) => (
                    <Select.Option key={el.id} value={el.id}>
                      {el[`nom_${currentLang}`]}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="status"
                label={t("manage_salesPoints.labels.status")}
                initialValue={true}
                rules={[{ required: true, message: t("common.required") }]}
              >
                <Select
                  disabled={viewMode}
                  options={[
                    { label: t("manage_salesPeriods.open") , value: true },
                    { label: t("manage_salesPeriods.closed") , value: false },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="id_governorate"
                label={t("manage_salesPoints.labels.governorate")}
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.governorateRequired"),
                  },
                ]}
              >
                <Select
                  disabled={viewMode || loading}
                  placeholder={t("manage_salesPoints.placeholders.governorate")}
                  onChange={handleGovernorateChange}
                  options={governorates.map((gov: any) => ({
                    label: gov[`nom_${currentLang}`],
                    value: gov.id,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="id_delegation"
                label={t("manage_salesPoints.labels.delegation")}
                rules={[
                  {
                    required: true,
                    message: t("manage_salesPoints.errors.delegationRequired"),
                  },
                ]}
              >
                <Select
                  disabled={viewMode}
                  placeholder={t("manage_salesPoints.placeholders.delegation")}
                  options={filteredDelegations.map((del: any) => ({
                    label: del[`nom_${currentLang}`],
                    value: del.id,
                  }))}
                  notFoundContent={
                    isDelegationsLoading ? (
                      <div className="flex items-center justify-center py-2">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                      </div>
                    ) : (
                      <div className="text-center py-2 text-gray-500">
                        {!selectedFilterGovernorate
                          ? t("manage_salesPoints.selectGovernorate")
                          : t("common.noData")}
                      </div>
                    )
                  }
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
}

export default ManageSalesPoints;
