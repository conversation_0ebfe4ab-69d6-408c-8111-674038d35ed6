import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import dayjs from 'dayjs';

export interface ExportColumn {
  title: string;
  dataIndex: string | string[];
  key: string;
  render?: (value: any, record: any) => string | number;
}

export interface ExportOptions {
  filename?: string;
  sheetName?: string;
  columns: ExportColumn[];
  data: any[];
  statisticType?: string;
  currentLang?: string;
}


export const exportToExcel = (options: ExportOptions) => {
  const {
    filename = 'export',
    sheetName = 'Données',
    columns,
    data
  } = options;

  // Préparer les données pour l'export
  const exportData = data.map((record: any) => {
    const row: Record<string, any> = {};

    columns.forEach((column) => {
      let value: any;

      // Gérer les dataIndex complexes (tableaux)
      if (Array.isArray(column.dataIndex)) {
        value = column.dataIndex.reduce((obj, key) => obj?.[key], record);
      } else {
        value = record[column.dataIndex];
      }

      // Appliquer le render si défini
      if (column.render && typeof column.render === 'function') {
        value = column.render(value, record);
      }

      // Nettoyer la valeur pour l'export
      if (typeof value === 'string') {
        // Supprimer les balises HTML et les caractères spéciaux
        value = value.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, '');
        // Supprimer "DT" à la fin si présent
        value = value.replace(/\s*DT\s*$/i, '');
      }

      // Convertir les valeurs undefined, null ou vides en 0 pour les colonnes numériques
      if (value === undefined || value === null || value === '') {
        // Vérifier si c'est une colonne numérique (montant, nombre, etc.)
        const isNumericColumn = column.key.includes('amount') ||
                               column.key.includes('count') ||
                               column.key.includes('total') ||
                               column.key.includes('montant') ||
                               column.key.includes('nombre') ||
                               column.title.toLowerCase().includes('montant') ||
                               column.title.toLowerCase().includes('nombre') ||
                               column.title.toLowerCase().includes('amount') ||
                               column.title.toLowerCase().includes('count');

        value = isNumericColumn ? 0 : '';
      }

      row[column.title] = value;
    });

    return row;
  });

  // Créer le workbook
  const workbook = XLSX.utils.book_new();

  // Créer la feuille de données directement sans informations de filtres
  const worksheet = XLSX.utils.json_to_sheet(exportData);

  // Ajuster la largeur des colonnes
  const columnWidths = columns.map((col) => ({ wch: Math.max(col.title.length, 15) }));
  worksheet['!cols'] = columnWidths;
  const truncatedSheetName = sheetName.length > 31 ? sheetName.substring(0, 31) : sheetName;

  // Ajouter la feuille au workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, truncatedSheetName);

  // Générer le nom de fichier avec timestamp
  const timestamp = dayjs().format('YYYY-MM-DD_HH-mm-ss');
  const finalFilename = `${filename}_${timestamp}.xlsx`;

  // Exporter le fichier
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, finalFilename);
};




export const getExportFilename = (statisticType: string, currentLang: string): string => {
  const filenames: Record<string, Record<string, string>> = {
    '1': {
      fr: 'statistiques_abonnes_trajet',
      en: 'subscribers_by_trip_statistics',
      ar: 'احصائيات_المشتركين_حسب_الرحلة'
    },
    '2': {
      fr: 'statistiques_abonnes_etablissement',
      en: 'subscribers_by_establishment_statistics',
      ar: 'احصائيات_المشتركين_حسب_المؤسسة'
    },
    '3': {
      fr: 'statistiques_abonnes_tranche_km',
      en: 'subscribers_by_km_range_statistics',
      ar: 'احصائيات_المشتركين_حسب_المسافة'
    },
    '4': {
      fr: 'statistiques_abonnes_remise',
      en: 'subscribers_by_discount_statistics',
      ar: 'احصائيات_المشتركين_حسب_الخصم'
    },
    '5': {
      fr: 'statistiques_recettes_periode_vente',
      en: 'revenue_by_sale_period_statistics',
      ar: 'احصائيات_الايرادات_حسب_فترة_البيع'
    },
    '6': {
      fr: 'statistiques_recettes_agence',
      en: 'revenue_by_agency_statistics',
      ar: 'احصائيات_الايرادات_حسب_الوكالة'
    },
    '7': {
      fr: 'statistiques_abonnes_ligne',
      en: 'subscribers_by_line_statistics',
      ar: 'احصائيات_المشتركين_حسب_الخط'
    },
    '8': {
      fr: 'statistiques_recettes_gouvernorat',
      en: 'revenue_by_governorate_statistics',
      ar: 'احصائيات_الايرادات_حسب_الولاية'
    },
    '9': {
      fr: 'statistiques_recettes_methode_paiement',
      en: 'revenue_by_payment_method_statistics',
      ar: 'احصائيات_الايرادات_حسب_طريقة_الدفع'
    },
    '10': {
      fr: 'statistiques_abonnements_type',
      en: 'subscriptions_by_type_statistics',
      ar: 'احصائيات_الاشتراكات_حسب_النوع'
    },
    '11': {
      fr: 'statistiques_abonnes_point_vente',
      en: 'subscribers_by_sale_point_statistics',
      ar: 'احصائيات_المشتركين_حسب_نقطة_البيع'
    }
  };

  return filenames[statisticType]?.[currentLang] || 'statistiques_export';
};
