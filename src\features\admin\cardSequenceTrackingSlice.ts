import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/card-sequences';

const initialState = {
    availableSequences: {},
    occupiedSequences: {},
    sequenceStats: {},
    allCardTypeStats: [],
    loading: false,
    error: null,
    isSequenceAvailable: null
};

/**
 * Get available sequences for a specific card type
 */
export const getAvailableSequences: any = createAsyncThunk(
    "getAvailableSequences",
    async (cardTypeId: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/available/${cardTypeId}`;
            const resp: any = await api.get(url);
            return { cardTypeId, data: resp.data };
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

/**
 * Get occupied sequences for a specific card type
 */
export const getOccupiedSequences: any = createAsyncThunk(
    "getOccupiedSequences",
    async (cardTypeId: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/occupied/${cardTypeId}`;
            const resp: any = await api.get(url);
            return { cardTypeId, data: resp.data };
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

/**
 * Get sequence statistics for a specific card type
 */
export const getSequenceStats: any = createAsyncThunk(
    "getSequenceStats",
    async (cardTypeId: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/summary`;
            const resp: any = await api.get(url);
            // Find the stats for the specific card type
            const cardTypeStats = resp.data.data.find((item: any) => item.id === cardTypeId) || {};
            return { cardTypeId, data: cardTypeStats };
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

/**
 * Get statistics for all card types
 */
export const getAllCardTypeStats: any = createAsyncThunk(
    "getAllCardTypeStats",
    async (_, thunkAPI: any) => {
        try {
            const url: string = `${URL}/summary`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

/**
 * Check if a sequence is available
 */
export const checkSequenceAvailability: any = createAsyncThunk(
    "checkSequenceAvailability",
    async (data: { id_card_type: number, start: number, end: number }, thunkAPI: any) => {
        try {
            // Check if the sequence is within any of the available ranges
            const url: string = `${URL}/available/${data.id_card_type}`;
            const resp: any = await api.get(url);

            const availableRanges = resp.data.data || [];
            let isAvailable = false;

            // Check if the requested sequence is fully contained within any available range
            for (const range of availableRanges) {
                if (data.start >= range.start_sequence && data.end <= range.end_sequence) {
                    isAvailable = true;
                    break;
                }
            }

            return { is_available: isAvailable };
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

/**
 * Recalculate available ranges for a specific card type
 * Note: This function is no longer needed with the new backend implementation
 * but we'll keep it for backward compatibility
 */
export const recalculateAvailableRanges: any = createAsyncThunk(
    "recalculateAvailableRanges",
    async (cardTypeId: number, thunkAPI: any) => {
        try {
            // Instead of recalculating, we'll just fetch the latest data
            const availableUrl: string = `${URL}/available/${cardTypeId}`;
            const occupiedUrl: string = `${URL}/occupied/${cardTypeId}`;
            const summaryUrl: string = `${URL}/summary`;

            const [availableResp, occupiedResp, summaryResp] = await Promise.all([
                api.get(availableUrl),
                api.get(occupiedUrl),
                api.get(summaryUrl)
            ]);

            // Find the stats for the specific card type
            const cardTypeStats = summaryResp.data.data.find((item: any) => item.id === cardTypeId) || {};

            return {
                cardTypeId,
                data: {
                    available: availableResp.data,
                    occupied: occupiedResp.data,
                    stats: cardTypeStats
                }
            };
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const cardSequenceTrackingSlice = createSlice({
    name: 'cardSequenceTracking',
    initialState,
    reducers: {
        clearSequenceAvailability: (state) => {
            state.isSequenceAvailable = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getAvailableSequences
            .addCase(getAvailableSequences.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAvailableSequences.fulfilled, (state, action) => {
                state.loading = false;
                const { cardTypeId, data } = action.payload;
                state.availableSequences = {
                    ...state.availableSequences,
                    [cardTypeId]: data.data
                };
            })
            .addCase(getAvailableSequences.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // getOccupiedSequences
            .addCase(getOccupiedSequences.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getOccupiedSequences.fulfilled, (state, action) => {
                state.loading = false;
                const { cardTypeId, data } = action.payload;
                state.occupiedSequences = {
                    ...state.occupiedSequences,
                    [cardTypeId]: data.data
                };
            })
            .addCase(getOccupiedSequences.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // getSequenceStats
            .addCase(getSequenceStats.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSequenceStats.fulfilled, (state, action) => {
                state.loading = false;
                const { cardTypeId, data } = action.payload;
                state.sequenceStats = {
                    ...state.sequenceStats,
                    [cardTypeId]: data
                };
            })
            .addCase(getSequenceStats.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // getAllCardTypeStats
            .addCase(getAllCardTypeStats.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAllCardTypeStats.fulfilled, (state, action) => {
                state.loading = false;
                state.allCardTypeStats = action.payload.data;
            })
            .addCase(getAllCardTypeStats.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // checkSequenceAvailability
            .addCase(checkSequenceAvailability.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.isSequenceAvailable = null;
            })
            .addCase(checkSequenceAvailability.fulfilled, (state, action) => {
                state.loading = false;
                state.isSequenceAvailable = action.payload.is_available;
            })
            .addCase(checkSequenceAvailability.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
                state.isSequenceAvailable = false;
            })

            // recalculateAvailableRanges
            .addCase(recalculateAvailableRanges.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(recalculateAvailableRanges.fulfilled, (state, action) => {
                state.loading = false;
                const { cardTypeId, data } = action.payload;

                // Update available sequences
                state.availableSequences = {
                    ...state.availableSequences,
                    [cardTypeId]: data.available.data
                };

                // Update occupied sequences
                state.occupiedSequences = {
                    ...state.occupiedSequences,
                    [cardTypeId]: data.occupied.data
                };

                // Update stats
                state.sequenceStats = {
                    ...state.sequenceStats,
                    [cardTypeId]: data.stats
                };
            })
            .addCase(recalculateAvailableRanges.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { clearSequenceAvailability } = cardSequenceTrackingSlice.actions;
export default cardSequenceTrackingSlice.reducer;
